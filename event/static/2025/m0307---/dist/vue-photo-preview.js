!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("vue-photo-preview",[],t):"object"==typeof exports?exports["vue-photo-preview"]=t():e["vue-photo-preview"]=t()}("undefined"!=typeof self?self:this,function(){return function(e){function t(o){if(n[o])return n[o].exports;var i=n[o]={i:o,l:!1,exports:{}};return e[o].call(i.exports,i,i.exports,t),i.l=!0,i.exports}var n={};return t.m=e,t.c=n,t.d=function(e,n,o){t.o(e,n)||Object.defineProperty(e,n,{configurable:!1,enumerable:!0,get:o})},t.n=function(e){var n=e&&e.__esModule?function(){return e.default}:function(){return e};return t.d(n,"a",n),n},t.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},t.p="/dist/",t(t.s=1)}([function(e,t,n){"use strict";var o=n(7),i=(n.n(o),n(8));n.n(i);t.a={}},function(e,t,n){"use strict";function o(e){return function(){var t=e.apply(this,arguments);return new Promise(function(e,n){function o(i,r){try{var a=t[i](r),l=a.value}catch(e){return void n(e)}if(!a.done)return Promise.resolve(l).then(function(e){o("next",e)},function(e){o("throw",e)});e(l)}return o("next")})}}Object.defineProperty(t,"__esModule",{value:!0});var i=n(2),r=n.n(i),a=n(5),l=n(10),s=n.n(l),u=n(11),c=n.n(u),d=void 0,p={install:function(e,t){var n=e.extend(a.a),t=t||{};d||(d=new n({el:document.createElement("div")}),document.body.appendChild(d.$el));var i=void 0,l=void 0;e.prototype.$preview={self:null,on:function(e,t){i=e,l=t}},e.mixin({data:function(){return{galleryElements:null,galleryPicLoading:!1}},methods:{$previewRefresh:function(){var e=this;setTimeout(function(){e.galleryElements=document.querySelectorAll("img[preview]");for(var t=0,n=e.galleryElements.length;t<n;t++)e.galleryElements[t].setAttribute("data-pswp-uid",t+1),e.galleryElements[t].onclick=e.onThumbnailsClick},200)},onThumbnailsClick:function(e){if(this.galleryPicLoading)return!1;this.galleryPicLoading=!0,e=e||window.event,e.preventDefault?e.preventDefault():e.returnValue=!1;var t,n=e.target||e.srcElement,o=n.getAttribute("preview");t=o?document.querySelectorAll('img[preview="'+o+'"]'):document.querySelectorAll("img[preview]");for(var i,r=t,a=0;a<r.length;a++)if(r[a]===n){i=a;break}return i>=0&&(this.openPhotoSwipe(i,r),this.$emit("preview-open",e,n.src)),!1},openPhotoSwipe:function(n,a,u,p){var f=this;return o(r.a.mark(function o(){var m,h,v,w,g,y,x,b,_;return r.a.wrap(function(o){for(;;)switch(o.prev=o.next){case 0:return m=document.querySelectorAll(".pswp")[0],o.next=3,f.parseThumbnailElements(a);case 3:if(w=o.sent,v={getThumbBoundsFn:function(e){var t=w[e].el,n=window.pageYOffset||document.documentElement.scrollTop,o=t.getBoundingClientRect();return{x:o.left,y:o.top+n,w:o.width}},addCaptionHTMLFn:function(e,t,n){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerText="",!1)},showHideOpacity:!0,history:!1,shareEl:!1,maxSpreadZoom:3,getDoubleTapZoom:function(e,t){return e?1.5:t.initialZoomLevel<.7?1:1.5}},!p){o.next=20;break}if(!v.galleryPIDs){o.next=17;break}g=0;case 8:if(!(g<w.length)){o.next=15;break}if(w[g].pid!=n){o.next=12;break}return v.index=g,o.abrupt("break",15);case 12:g++,o.next=8;break;case 15:o.next=18;break;case 17:v.index=parseInt(n,10)-1;case 18:o.next=21;break;case 20:v.index=parseInt(n,10);case 21:if(!isNaN(v.index)){o.next=23;break}return o.abrupt("return");case 23:v=f.preViewExtend(v,t),u&&(v.showAnimationDuration=0),h=new s.a(m,c.a,w,v),e.prototype.$preview.self=h,x=!1,b=!0,h.listen("beforeResize",function(){var e=window.devicePixelRatio?window.devicePixelRatio:1;e=Math.min(e,2.5),y=h.viewportSize.x*e,y>=1200||!h.likelyTouchDevice&&y>800||screen.width>1200?x||(x=!0,_=!0):x&&(x=!1,_=!0),_&&!b&&h.invalidateCurrItems(),b&&(b=!1),_=!1}),h.listen("gettingData",function(e,t){t.el.getAttribute("large")?(t.src=t.o.src,t.w=t.o.w,t.h=t.o.h):(t.src=t.m.src,t.w=t.m.w,t.h=t.m.h)}),h.listen("imageLoadComplete",function(e,t){f.galleryPicLoading=!1}),h.listen(i,l),h.init(),d.$el.classList=d.$el.classList+" pswp--zoom-allowed";case 34:case"end":return o.stop()}},o,f)}))()},parseThumbnailElements:function(e){return new Promise(function(t){var n,o,i=[];o={};for(var r=0;r<e.length;r++){if(n=e[r],1===n.nodeType){if(void 0===n.naturalWidth){var r=new Image;r.src=n.src;var a=r.width,l=r.height}else var a=n.naturalWidth,l=n.naturalHeight;!function(r){var u=new Image;u.src=n.getAttribute("large")?n.getAttribute("large"):n.getAttribute("src"),u.text=n.getAttribute("preview-text"),u.author=n.getAttribute("data-author"),u.onload=function(){o={title:u.text,el:e[r],src:u.src,w:a,h:l,author:u.author,o:{src:u.src,w:this.width,h:this.height},m:{src:u.src,w:this.width,h:this.height}},i[r]=o,++s==e.length&&t(i)}}(r);var s=0}}})},preViewExtend:function(e,t){for(var n in t)e[n]=t[n];return e},initPreview:function(e){this.galleryElements=document.querySelectorAll(e);for(var t=0,n=this.galleryElements.length;t<n;t++)this.galleryElements[t].setAttribute("data-pswp-uid",t+1),this.galleryElements[t].onclick=this.onThumbnailsClick}},mounted:function(){this.initPreview("img[preview]")}})}};t.default=p,"undefined"==typeof window||window.vuePhotoPreview||(window.vuePhotoPreview=p),"undefined"!=typeof window&&window.Vue&&p.install(window.Vue)},function(e,t,n){e.exports=n(3)},function(e,t,n){var o=function(){return this}()||Function("return this")(),i=o.regeneratorRuntime&&Object.getOwnPropertyNames(o).indexOf("regeneratorRuntime")>=0,r=i&&o.regeneratorRuntime;if(o.regeneratorRuntime=void 0,e.exports=n(4),i)o.regeneratorRuntime=r;else try{delete o.regeneratorRuntime}catch(e){o.regeneratorRuntime=void 0}},function(e,t){!function(t){"use strict";function n(e,t,n,o){var r=t&&t.prototype instanceof i?t:i,a=Object.create(r.prototype),l=new f(o||[]);return a._invoke=u(e,n,l),a}function o(e,t,n){try{return{type:"normal",arg:e.call(t,n)}}catch(e){return{type:"throw",arg:e}}}function i(){}function r(){}function a(){}function l(e){["next","throw","return"].forEach(function(t){e[t]=function(e){return this._invoke(t,e)}})}function s(e){function t(n,i,r,a){var l=o(e[n],e,i);if("throw"!==l.type){var s=l.arg,u=s.value;return u&&"object"==typeof u&&g.call(u,"__await")?Promise.resolve(u.__await).then(function(e){t("next",e,r,a)},function(e){t("throw",e,r,a)}):Promise.resolve(u).then(function(e){s.value=e,r(s)},a)}a(l.arg)}function n(e,n){function o(){return new Promise(function(o,i){t(e,n,o,i)})}return i=i?i.then(o,o):o()}var i;this._invoke=n}function u(e,t,n){var i=T;return function(r,a){if(i===S)throw new Error("Generator is already running");if(i===k){if("throw"===r)throw a;return h()}for(n.method=r,n.arg=a;;){var l=n.delegate;if(l){var s=c(l,n);if(s){if(s===D)continue;return s}}if("next"===n.method)n.sent=n._sent=n.arg;else if("throw"===n.method){if(i===T)throw i=k,n.arg;n.dispatchException(n.arg)}else"return"===n.method&&n.abrupt("return",n.arg);i=S;var u=o(e,t,n);if("normal"===u.type){if(i=n.done?k:I,u.arg===D)continue;return{value:u.arg,done:n.done}}"throw"===u.type&&(i=k,n.method="throw",n.arg=u.arg)}}}function c(e,t){var n=e.iterator[t.method];if(n===v){if(t.delegate=null,"throw"===t.method){if(e.iterator.return&&(t.method="return",t.arg=v,c(e,t),"throw"===t.method))return D;t.method="throw",t.arg=new TypeError("The iterator does not provide a 'throw' method")}return D}var i=o(n,e.iterator,t.arg);if("throw"===i.type)return t.method="throw",t.arg=i.arg,t.delegate=null,D;var r=i.arg;return r?r.done?(t[e.resultName]=r.value,t.next=e.nextLoc,"return"!==t.method&&(t.method="next",t.arg=v),t.delegate=null,D):r:(t.method="throw",t.arg=new TypeError("iterator result is not an object"),t.delegate=null,D)}function d(e){var t={tryLoc:e[0]};1 in e&&(t.catchLoc=e[1]),2 in e&&(t.finallyLoc=e[2],t.afterLoc=e[3]),this.tryEntries.push(t)}function p(e){var t=e.completion||{};t.type="normal",delete t.arg,e.completion=t}function f(e){this.tryEntries=[{tryLoc:"root"}],e.forEach(d,this),this.reset(!0)}function m(e){if(e){var t=e[x];if(t)return t.call(e);if("function"==typeof e.next)return e;if(!isNaN(e.length)){var n=-1,o=function t(){for(;++n<e.length;)if(g.call(e,n))return t.value=e[n],t.done=!1,t;return t.value=v,t.done=!0,t};return o.next=o}}return{next:h}}function h(){return{value:v,done:!0}}var v,w=Object.prototype,g=w.hasOwnProperty,y="function"==typeof Symbol?Symbol:{},x=y.iterator||"@@iterator",b=y.asyncIterator||"@@asyncIterator",_=y.toStringTag||"@@toStringTag",C="object"==typeof e,E=t.regeneratorRuntime;if(E)return void(C&&(e.exports=E));E=t.regeneratorRuntime=C?e.exports:{},E.wrap=n;var T="suspendedStart",I="suspendedYield",S="executing",k="completed",D={},O={};O[x]=function(){return this};var L=Object.getPrototypeOf,F=L&&L(L(m([])));F&&F!==w&&g.call(F,x)&&(O=F);var A=a.prototype=i.prototype=Object.create(O);r.prototype=A.constructor=a,a.constructor=r,a[_]=r.displayName="GeneratorFunction",E.isGeneratorFunction=function(e){var t="function"==typeof e&&e.constructor;return!!t&&(t===r||"GeneratorFunction"===(t.displayName||t.name))},E.mark=function(e){return Object.setPrototypeOf?Object.setPrototypeOf(e,a):(e.__proto__=a,_ in e||(e[_]="GeneratorFunction")),e.prototype=Object.create(A),e},E.awrap=function(e){return{__await:e}},l(s.prototype),s.prototype[b]=function(){return this},E.AsyncIterator=s,E.async=function(e,t,o,i){var r=new s(n(e,t,o,i));return E.isGeneratorFunction(t)?r:r.next().then(function(e){return e.done?e.value:r.next()})},l(A),A[_]="Generator",A[x]=function(){return this},A.toString=function(){return"[object Generator]"},E.keys=function(e){var t=[];for(var n in e)t.push(n);return t.reverse(),function n(){for(;t.length;){var o=t.pop();if(o in e)return n.value=o,n.done=!1,n}return n.done=!0,n}},E.values=m,f.prototype={constructor:f,reset:function(e){if(this.prev=0,this.next=0,this.sent=this._sent=v,this.done=!1,this.delegate=null,this.method="next",this.arg=v,this.tryEntries.forEach(p),!e)for(var t in this)"t"===t.charAt(0)&&g.call(this,t)&&!isNaN(+t.slice(1))&&(this[t]=v)},stop:function(){this.done=!0;var e=this.tryEntries[0],t=e.completion;if("throw"===t.type)throw t.arg;return this.rval},dispatchException:function(e){function t(t,o){return r.type="throw",r.arg=e,n.next=t,o&&(n.method="next",n.arg=v),!!o}if(this.done)throw e;for(var n=this,o=this.tryEntries.length-1;o>=0;--o){var i=this.tryEntries[o],r=i.completion;if("root"===i.tryLoc)return t("end");if(i.tryLoc<=this.prev){var a=g.call(i,"catchLoc"),l=g.call(i,"finallyLoc");if(a&&l){if(this.prev<i.catchLoc)return t(i.catchLoc,!0);if(this.prev<i.finallyLoc)return t(i.finallyLoc)}else if(a){if(this.prev<i.catchLoc)return t(i.catchLoc,!0)}else{if(!l)throw new Error("try statement without catch or finally");if(this.prev<i.finallyLoc)return t(i.finallyLoc)}}}},abrupt:function(e,t){for(var n=this.tryEntries.length-1;n>=0;--n){var o=this.tryEntries[n];if(o.tryLoc<=this.prev&&g.call(o,"finallyLoc")&&this.prev<o.finallyLoc){var i=o;break}}i&&("break"===e||"continue"===e)&&i.tryLoc<=t&&t<=i.finallyLoc&&(i=null);var r=i?i.completion:{};return r.type=e,r.arg=t,i?(this.method="next",this.next=i.finallyLoc,D):this.complete(r)},complete:function(e,t){if("throw"===e.type)throw e.arg;return"break"===e.type||"continue"===e.type?this.next=e.arg:"return"===e.type?(this.rval=this.arg=e.arg,this.method="return",this.next="end"):"normal"===e.type&&t&&(this.next=t),D},finish:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.finallyLoc===e)return this.complete(n.completion,n.afterLoc),p(n),D}},catch:function(e){for(var t=this.tryEntries.length-1;t>=0;--t){var n=this.tryEntries[t];if(n.tryLoc===e){var o=n.completion;if("throw"===o.type){var i=o.arg;p(n)}return i}}throw new Error("illegal catch attempt")},delegateYield:function(e,t,n){return this.delegate={iterator:m(e),resultName:t,nextLoc:n},"next"===this.method&&(this.arg=v),D}}}(function(){return this}()||Function("return this")())},function(e,t,n){"use strict";var o=n(0),i=n(9),r=n(6),a=r(o.a,i.a,!1,null,null,null);t.a=a.exports},function(e,t){e.exports=function(e,t,n,o,i,r){var a,l=e=e||{},s=typeof e.default;"object"!==s&&"function"!==s||(a=e,l=e.default);var u="function"==typeof l?l.options:l;t&&(u.render=t.render,u.staticRenderFns=t.staticRenderFns,u._compiled=!0),n&&(u.functional=!0),i&&(u._scopeId=i);var c;if(r?(c=function(e){e=e||this.$vnode&&this.$vnode.ssrContext||this.parent&&this.parent.$vnode&&this.parent.$vnode.ssrContext,e||"undefined"==typeof __VUE_SSR_CONTEXT__||(e=__VUE_SSR_CONTEXT__),o&&o.call(this,e),e&&e._registeredComponents&&e._registeredComponents.add(r)},u._ssrRegister=c):o&&(c=o),c){var d=u.functional,p=d?u.render:u.beforeCreate;d?(u._injectStyles=c,u.render=function(e,t){return c.call(t),p(e,t)}):u.beforeCreate=p?[].concat(p,c):[c]}return{esModule:a,exports:l,options:u}}},function(e,t){},function(e,t){},function(e,t,n){"use strict";var o=function(){var e=this,t=e.$createElement;e._self._c;return e._m(0)},i=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"pswp",attrs:{tabindex:"-1",role:"dialog","aria-hidden":"true"}},[n("div",{staticClass:"pswp__bg"}),e._v(" "),n("div",{staticClass:"pswp__scroll-wrap"},[n("div",{staticClass:"pswp__container"},[n("div",{staticClass:"pswp__item"}),e._v(" "),n("div",{staticClass:"pswp__item"}),e._v(" "),n("div",{staticClass:"pswp__item"})]),e._v(" "),n("div",{staticClass:"pswp__ui pswp__ui--hidden"},[n("div",{staticClass:"pswp__top-bar"},[n("div",{staticClass:"pswp__counter"}),e._v(" "),n("button",{staticClass:"pswp__button pswp__button--close",attrs:{title:"Close (Esc)"}}),e._v(" "),n("button",{staticClass:"pswp__button pswp__button--share",attrs:{title:"Share"}}),e._v(" "),n("button",{staticClass:"pswp__button pswp__button--fs",attrs:{title:"Toggle fullscreen"}}),e._v(" "),n("button",{staticClass:"pswp__button pswp__button--zoom",attrs:{title:"Zoom in/out"}}),e._v(" "),n("div",{staticClass:"pswp__preloader"},[n("div",{staticClass:"pswp__preloader__icn"},[n("div",{staticClass:"pswp__preloader__cut"},[n("div",{staticClass:"pswp__preloader__donut"})])])])]),e._v(" "),n("div",{staticClass:"pswp__share-modal pswp__share-modal--hidden pswp__single-tap"},[n("div",{staticClass:"pswp__share-tooltip"})]),e._v(" "),n("button",{staticClass:"pswp__button pswp__button--arrow--left",attrs:{title:"Previous (arrow left)"}}),e._v(" "),n("button",{staticClass:"pswp__button pswp__button--arrow--right",attrs:{title:"Next (arrow right)"}}),e._v(" "),n("div",{staticClass:"pswp__caption"},[n("div",{staticClass:"pswp__caption__center"})])])])])}],r={render:o,staticRenderFns:i};t.a=r},function(e,t,n){var o,i;/*! PhotoSwipe - v4.1.3 - 2019-01-08
* http://photoswipe.com
* Copyright (c) 2019 Dmitry Semenov; */
!function(r,a){o=a,void 0!==(i="function"==typeof o?o.call(t,n,t,e):o)&&(e.exports=i)}(0,function(){"use strict";return function(e,t,n,o){var i={features:null,bind:function(e,t,n,o){var i=(o?"remove":"add")+"EventListener";t=t.split(" ");for(var r=0;r<t.length;r++)t[r]&&e[i](t[r],n,!1)},isArray:function(e){return e instanceof Array},createEl:function(e,t){var n=document.createElement(t||"div");return e&&(n.className=e),n},getScrollY:function(){var e=window.pageYOffset;return void 0!==e?e:document.documentElement.scrollTop},unbind:function(e,t,n){i.bind(e,t,n,!0)},removeClass:function(e,t){var n=new RegExp("(\\s|^)"+t+"(\\s|$)");e.className=e.className.replace(n," ").replace(/^\s\s*/,"").replace(/\s\s*$/,"")},addClass:function(e,t){i.hasClass(e,t)||(e.className+=(e.className?" ":"")+t)},hasClass:function(e,t){return e.className&&new RegExp("(^|\\s)"+t+"(\\s|$)").test(e.className)},getChildByClass:function(e,t){for(var n=e.firstChild;n;){if(i.hasClass(n,t))return n;n=n.nextSibling}},arraySearch:function(e,t,n){for(var o=e.length;o--;)if(e[o][n]===t)return o;return-1},extend:function(e,t,n){for(var o in t)if(t.hasOwnProperty(o)){if(n&&e.hasOwnProperty(o))continue;e[o]=t[o]}},easing:{sine:{out:function(e){return Math.sin(e*(Math.PI/2))},inOut:function(e){return-(Math.cos(Math.PI*e)-1)/2}},cubic:{out:function(e){return--e*e*e+1}}},detectFeatures:function(){if(i.features)return i.features;var e=i.createEl(),t=e.style,n="",o={};if(o.oldIE=document.all&&!document.addEventListener,o.touch="ontouchstart"in window,window.requestAnimationFrame&&(o.raf=window.requestAnimationFrame,o.caf=window.cancelAnimationFrame),o.pointerEvent=!!window.PointerEvent||navigator.msPointerEnabled,!o.pointerEvent){var r=navigator.userAgent;if(/iP(hone|od)/.test(navigator.platform)){var a=navigator.appVersion.match(/OS (\d+)_(\d+)_?(\d+)?/);a&&a.length>0&&(a=parseInt(a[1],10))>=1&&a<8&&(o.isOldIOSPhone=!0)}var l=r.match(/Android\s([0-9\.]*)/),s=l?l[1]:0;s=parseFloat(s),s>=1&&(s<4.4&&(o.isOldAndroid=!0),o.androidVersion=s),o.isMobileOpera=/opera mini|opera mobi/i.test(r)}for(var u,c,d=["transform","perspective","animationName"],p=["","webkit","Moz","ms","O"],f=0;f<4;f++){n=p[f];for(var m=0;m<3;m++)u=d[m],c=n+(n?u.charAt(0).toUpperCase()+u.slice(1):u),!o[u]&&c in t&&(o[u]=c);n&&!o.raf&&(n=n.toLowerCase(),o.raf=window[n+"RequestAnimationFrame"],o.raf&&(o.caf=window[n+"CancelAnimationFrame"]||window[n+"CancelRequestAnimationFrame"]))}if(!o.raf){var h=0;o.raf=function(e){var t=(new Date).getTime(),n=Math.max(0,16-(t-h)),o=window.setTimeout(function(){e(t+n)},n);return h=t+n,o},o.caf=function(e){clearTimeout(e)}}return o.svg=!!document.createElementNS&&!!document.createElementNS("http://www.w3.org/2000/svg","svg").createSVGRect,i.features=o,o}};i.detectFeatures(),i.features.oldIE&&(i.bind=function(e,t,n,o){t=t.split(" ");for(var i,r=(o?"detach":"attach")+"Event",a=function(){n.handleEvent.call(n)},l=0;l<t.length;l++)if(i=t[l])if("object"==typeof n&&n.handleEvent){if(o){if(!n["oldIE"+i])return!1}else n["oldIE"+i]=a;e[r]("on"+i,n["oldIE"+i])}else e[r]("on"+i,n)});var r=this,a={allowPanToNext:!0,spacing:.12,bgOpacity:1,mouseUsed:!1,loop:!0,pinchToClose:!0,closeOnScroll:!0,closeOnVerticalDrag:!0,verticalDragRange:.75,hideAnimationDuration:333,showAnimationDuration:333,showHideOpacity:!1,focus:!0,escKey:!0,arrowKeys:!0,mainScrollEndFriction:.35,panEndFriction:.35,isClickableElement:function(e){return"A"===e.tagName},getDoubleTapZoom:function(e,t){return e?1:t.initialZoomLevel<.7?1:1.33},maxSpreadZoom:1.33,modal:!0,scaleMode:"fit"};i.extend(a,o);var l,s,u,c,d,p,f,m,h,v,w,g,y,x,b,_,C,E,T,I,S,k,D,O,L,F,A,M,R,P,Z,z,N,U,K,H,W,B,G,Y,j,q,V,$,X,J,Q,ee,te,ne,oe,ie,re,ae,le,se,ue,ce=function(){return{x:0,y:0}},de=ce(),pe=ce(),fe=ce(),me={},he=0,ve={},we=ce(),ge=0,ye=!0,xe=[],be={},_e=!1,Ce=function(e,t){i.extend(r,t.publicMethods),xe.push(e)},Ee=function(e){var t=$t();return e>t-1?e-t:e<0?t+e:e},Te={},Ie=function(e,t){return Te[e]||(Te[e]=[]),Te[e].push(t)},Se=function(e){var t=Te[e];if(t){var n=Array.prototype.slice.call(arguments);n.shift();for(var o=0;o<t.length;o++)t[o].apply(r,n)}},ke=function(){return(new Date).getTime()},De=function(e){le=e,r.bg.style.opacity=e*a.bgOpacity},Oe=function(e,t,n,o,i){(!_e||i&&i!==r.currItem)&&(o/=i?i.fitRatio:r.currItem.fitRatio),e[k]=g+t+"px, "+n+"px"+y+" scale("+o+")"},Le=function(e){ne&&(e&&(v>r.currItem.fitRatio?_e||(un(r.currItem,!1,!0),_e=!0):_e&&(un(r.currItem),_e=!1)),Oe(ne,fe.x,fe.y,v))},Fe=function(e){e.container&&Oe(e.container.style,e.initialPosition.x,e.initialPosition.y,e.initialZoomLevel,e)},Ae=function(e,t){t[k]=g+e+"px, 0px"+y},Me=function(e,t){if(!a.loop&&t){var n=c+(we.x*he-e)/we.x,o=Math.round(e-ht.x);(n<0&&o>0||n>=$t()-1&&o<0)&&(e=ht.x+o*a.mainScrollEndFriction)}ht.x=e,Ae(e,d)},Re=function(e,t){var n=vt[e]-ve[e];return pe[e]+de[e]+n-n*(t/w)},Pe=function(e,t){e.x=t.x,e.y=t.y,t.id&&(e.id=t.id)},Ze=function(e){e.x=Math.round(e.x),e.y=Math.round(e.y)},ze=null,Ne=function(){ze&&(i.unbind(document,"mousemove",Ne),i.addClass(e,"pswp--has_mouse"),a.mouseUsed=!0,Se("mouseUsed")),ze=setTimeout(function(){ze=null},100)},Ue=function(){i.bind(document,"keydown",r),Z.transform&&i.bind(r.scrollWrap,"click",r),a.mouseUsed||i.bind(document,"mousemove",Ne),i.bind(window,"resize scroll orientationchange",r),Se("bindEvents")},Ke=function(){i.unbind(window,"resize scroll orientationchange",r),i.unbind(window,"scroll",h.scroll),i.unbind(document,"keydown",r),i.unbind(document,"mousemove",Ne),Z.transform&&i.unbind(r.scrollWrap,"click",r),G&&i.unbind(window,f,r),clearTimeout(z),Se("unbindEvents")},He=function(e,t){var n=rn(r.currItem,me,e);return t&&(te=n),n},We=function(e){return e||(e=r.currItem),e.initialZoomLevel},Be=function(e){return e||(e=r.currItem),e.w>0?a.maxSpreadZoom:1},Ge=function(e,t,n,o){return o===r.currItem.initialZoomLevel?(n[e]=r.currItem.initialPosition[e],!0):(n[e]=Re(e,o),n[e]>t.min[e]?(n[e]=t.min[e],!0):n[e]<t.max[e]&&(n[e]=t.max[e],!0))},Ye=function(){if(k){var t=Z.perspective&&!O;return g="translate"+(t?"3d(":"("),void(y=Z.perspective?", 0px)":")")}k="left",i.addClass(e,"pswp--ie"),Ae=function(e,t){t.left=e+"px"},Fe=function(e){var t=e.fitRatio>1?1:e.fitRatio,n=e.container.style,o=t*e.w,i=t*e.h;n.width=o+"px",n.height=i+"px",n.left=e.initialPosition.x+"px",n.top=e.initialPosition.y+"px"},Le=function(){if(ne){var e=ne,t=r.currItem,n=t.fitRatio>1?1:t.fitRatio,o=n*t.w,i=n*t.h;e.width=o+"px",e.height=i+"px",e.left=fe.x+"px",e.top=fe.y+"px"}}},je=function(e){var t="";a.escKey&&27===e.keyCode?t="close":a.arrowKeys&&(37===e.keyCode?t="prev":39===e.keyCode&&(t="next")),t&&(e.ctrlKey||e.altKey||e.shiftKey||e.metaKey||(e.preventDefault?e.preventDefault():e.returnValue=!1,r[t]()))},qe=function(e){e&&(q||j||oe||W)&&(e.preventDefault(),e.stopPropagation())},Ve=function(){r.setScrollOffset(0,i.getScrollY())},$e={},Xe=0,Je=function(e){$e[e]&&($e[e].raf&&F($e[e].raf),Xe--,delete $e[e])},Qe=function(e){$e[e]&&Je(e),$e[e]||(Xe++,$e[e]={})},et=function(){for(var e in $e)$e.hasOwnProperty(e)&&Je(e)},tt=function(e,t,n,o,i,r,a){var l,s=ke();Qe(e);var u=function(){if($e[e]){if((l=ke()-s)>=o)return Je(e),r(n),void(a&&a());r((n-t)*i(l/o)+t),$e[e].raf=L(u)}};u()},nt={shout:Se,listen:Ie,viewportSize:me,options:a,isMainScrollAnimating:function(){return oe},getZoomLevel:function(){return v},getCurrentIndex:function(){return c},isDragging:function(){return G},isZooming:function(){return J},setScrollOffset:function(e,t){ve.x=e,P=ve.y=t,Se("updateScrollOffset",ve)},applyZoomPan:function(e,t,n,o){fe.x=t,fe.y=n,v=e,Le(o)},init:function(){if(!l&&!s){var n;r.framework=i,r.template=e,r.bg=i.getChildByClass(e,"pswp__bg"),A=e.className,l=!0,Z=i.detectFeatures(),L=Z.raf,F=Z.caf,k=Z.transform,R=Z.oldIE,r.scrollWrap=i.getChildByClass(e,"pswp__scroll-wrap"),r.container=i.getChildByClass(r.scrollWrap,"pswp__container"),d=r.container.style,r.itemHolders=_=[{el:r.container.children[0],wrap:0,index:-1},{el:r.container.children[1],wrap:0,index:-1},{el:r.container.children[2],wrap:0,index:-1}],_[0].el.style.display=_[2].el.style.display="none",Ye(),h={resize:r.updateSize,orientationchange:function(){clearTimeout(z),z=setTimeout(function(){me.x!==r.scrollWrap.clientWidth&&r.updateSize()},500)},scroll:Ve,keydown:je,click:qe};var o=Z.isOldIOSPhone||Z.isOldAndroid||Z.isMobileOpera;for(Z.animationName&&Z.transform&&!o||(a.showAnimationDuration=a.hideAnimationDuration=0),n=0;n<xe.length;n++)r["init"+xe[n]]();if(t){(r.ui=new t(r,i)).init()}Se("firstUpdate"),c=c||a.index||0,(isNaN(c)||c<0||c>=$t())&&(c=0),r.currItem=Vt(c),(Z.isOldIOSPhone||Z.isOldAndroid)&&(ye=!1),e.setAttribute("aria-hidden","false"),a.modal&&(ye?e.style.position="fixed":(e.style.position="absolute",e.style.top=i.getScrollY()+"px")),void 0===P&&(Se("initialLayout"),P=M=i.getScrollY());var u="pswp--open ";for(a.mainClass&&(u+=a.mainClass+" "),a.showHideOpacity&&(u+="pswp--animate_opacity "),u+=O?"pswp--touch":"pswp--notouch",u+=Z.animationName?" pswp--css_animation":"",u+=Z.svg?" pswp--svg":"",i.addClass(e,u),r.updateSize(),p=-1,ge=null,n=0;n<3;n++)Ae((n+p)*we.x,_[n].el.style);R||i.bind(r.scrollWrap,m,r),Ie("initialZoomInEnd",function(){r.setContent(_[0],c-1),r.setContent(_[2],c+1),_[0].el.style.display=_[2].el.style.display="block",a.focus&&e.focus(),Ue()}),r.setContent(_[1],c),r.updateCurrItem(),Se("afterInit"),ye||(x=setInterval(function(){Xe||G||J||v!==r.currItem.initialZoomLevel||r.updateSize()},1e3)),i.addClass(e,"pswp--visible")}},close:function(){l&&(l=!1,s=!0,Se("close"),Ke(),Jt(r.currItem,null,!0,r.destroy))},destroy:function(){Se("destroy"),Gt&&clearTimeout(Gt),e.setAttribute("aria-hidden","true"),e.className=A,x&&clearInterval(x),i.unbind(r.scrollWrap,m,r),i.unbind(window,"scroll",r),bt(),et(),Te=null},panTo:function(e,t,n){n||(e>te.min.x?e=te.min.x:e<te.max.x&&(e=te.max.x),t>te.min.y?t=te.min.y:t<te.max.y&&(t=te.max.y)),fe.x=e,fe.y=t,Le()},handleEvent:function(e){e=e||window.event,h[e.type]&&h[e.type](e)},goTo:function(e){e=Ee(e);var t=e-c;ge=t,c=e,r.currItem=Vt(c),he-=t,Me(we.x*he),et(),oe=!1,r.updateCurrItem()},next:function(){r.goTo(c+1)},prev:function(){r.goTo(c-1)},updateCurrZoomItem:function(e){if(e&&Se("beforeChange",0),_[1].el.children.length){var t=_[1].el.children[0];ne=i.hasClass(t,"pswp__zoom-wrap")?t.style:null}else ne=null;te=r.currItem.bounds,w=v=r.currItem.initialZoomLevel,fe.x=te.center.x,fe.y=te.center.y,e&&Se("afterChange")},invalidateCurrItems:function(){b=!0;for(var e=0;e<3;e++)_[e].item&&(_[e].item.needsUpdate=!0)},updateCurrItem:function(e){if(0!==ge){var t,n=Math.abs(ge);if(!(e&&n<2)){r.currItem=Vt(c),_e=!1,Se("beforeChange",ge),n>=3&&(p+=ge+(ge>0?-3:3),n=3);for(var o=0;o<n;o++)ge>0?(t=_.shift(),_[2]=t,p++,Ae((p+2)*we.x,t.el.style),r.setContent(t,c-n+o+1+1)):(t=_.pop(),_.unshift(t),p--,Ae(p*we.x,t.el.style),r.setContent(t,c+n-o-1-1));if(ne&&1===Math.abs(ge)){var i=Vt(C);i.initialZoomLevel!==v&&(rn(i,me),un(i),Fe(i))}ge=0,r.updateCurrZoomItem(),C=c,Se("afterChange")}}},updateSize:function(t){if(!ye&&a.modal){var n=i.getScrollY();if(P!==n&&(e.style.top=n+"px",P=n),!t&&be.x===window.innerWidth&&be.y===window.innerHeight)return;be.x=window.innerWidth,be.y=window.innerHeight,e.style.height=be.y+"px"}if(me.x=r.scrollWrap.clientWidth,me.y=r.scrollWrap.clientHeight,Ve(),we.x=me.x+Math.round(me.x*a.spacing),we.y=me.y,Me(we.x*he),Se("beforeResize"),void 0!==p){for(var o,l,s,u=0;u<3;u++)o=_[u],Ae((u+p)*we.x,o.el.style),s=c+u-1,a.loop&&$t()>2&&(s=Ee(s)),l=Vt(s),l&&(b||l.needsUpdate||!l.bounds)?(r.cleanSlide(l),r.setContent(o,s),1===u&&(r.currItem=l,r.updateCurrZoomItem(!0)),l.needsUpdate=!1):-1===o.index&&s>=0&&r.setContent(o,s),l&&l.container&&(rn(l,me),un(l),Fe(l));b=!1}w=v=r.currItem.initialZoomLevel,te=r.currItem.bounds,te&&(fe.x=te.center.x,fe.y=te.center.y,Le(!0)),Se("resize")},zoomTo:function(e,t,n,o,r){t&&(w=v,vt.x=Math.abs(t.x)-fe.x,vt.y=Math.abs(t.y)-fe.y,Pe(pe,fe));var a=He(e,!1),l={};Ge("x",a,l,e),Ge("y",a,l,e);var s=v,u={x:fe.x,y:fe.y};Ze(l);var c=function(t){1===t?(v=e,fe.x=l.x,fe.y=l.y):(v=(e-s)*t+s,fe.x=(l.x-u.x)*t+u.x,fe.y=(l.y-u.y)*t+u.y),r&&r(t),Le(1===t)};n?tt("customZoomTo",0,1,n,o||i.easing.sine.inOut,c):c(1)}},ot={},it={},rt={},at={},lt={},st=[],ut={},ct=[],dt={},pt=0,ft=ce(),mt=0,ht=ce(),vt=ce(),wt=ce(),gt=function(e,t){return e.x===t.x&&e.y===t.y},yt=function(e,t){return Math.abs(e.x-t.x)<25&&Math.abs(e.y-t.y)<25},xt=function(e,t){return dt.x=Math.abs(e.x-t.x),dt.y=Math.abs(e.y-t.y),Math.sqrt(dt.x*dt.x+dt.y*dt.y)},bt=function(){V&&(F(V),V=null)},_t=function(){G&&(V=L(_t),zt())},Ct=function(){return!("fit"===a.scaleMode&&v===r.currItem.initialZoomLevel)},Et=function(e,t){return!(!e||e===document)&&(!(e.getAttribute("class")&&e.getAttribute("class").indexOf("pswp__scroll-wrap")>-1)&&(t(e)?e:Et(e.parentNode,t)))},Tt={},It=function(e,t){return Tt.prevent=!Et(e.target,a.isClickableElement),Se("preventDragEvent",e,t,Tt),Tt.prevent},St=function(e,t){return t.x=e.pageX,t.y=e.pageY,t.id=e.identifier,t},kt=function(e,t,n){n.x=.5*(e.x+t.x),n.y=.5*(e.y+t.y)},Dt=function(e,t,n){if(e-U>50){var o=ct.length>2?ct.shift():{};o.x=t,o.y=n,ct.push(o),U=e}},Ot=function(){var e=fe.y-r.currItem.initialPosition.y;return 1-Math.abs(e/(me.y/2))},Lt={},Ft={},At=[],Mt=function(e){for(;At.length>0;)At.pop();return D?(ue=0,st.forEach(function(e){0===ue?At[0]=e:1===ue&&(At[1]=e),ue++})):e.type.indexOf("touch")>-1?e.touches&&e.touches.length>0&&(At[0]=St(e.touches[0],Lt),e.touches.length>1&&(At[1]=St(e.touches[1],Ft))):(Lt.x=e.pageX,Lt.y=e.pageY,Lt.id="",At[0]=Lt),At},Rt=function(e,t){var n,o,i,l,s=fe[e]+t[e],u=t[e]>0,c=ht.x+t.x,d=ht.x-ut.x;if(n=s>te.min[e]||s<te.max[e]?a.panEndFriction:1,s=fe[e]+t[e]*n,(a.allowPanToNext||v===r.currItem.initialZoomLevel)&&(ne?"h"!==ie||"x"!==e||j||(u?(s>te.min[e]&&(n=a.panEndFriction,te.min[e]-s,o=te.min[e]-pe[e]),(o<=0||d<0)&&$t()>1?(l=c,d<0&&c>ut.x&&(l=ut.x)):te.min.x!==te.max.x&&(i=s)):(s<te.max[e]&&(n=a.panEndFriction,s-te.max[e],o=pe[e]-te.max[e]),(o<=0||d>0)&&$t()>1?(l=c,d>0&&c<ut.x&&(l=ut.x)):te.min.x!==te.max.x&&(i=s))):l=c,"x"===e))return void 0!==l&&(Me(l,!0),$=l!==ut.x),te.min.x!==te.max.x&&(void 0!==i?fe.x=i:$||(fe.x+=t.x*n)),void 0!==l;oe||$||v>r.currItem.fitRatio&&(fe[e]+=t[e]*n)},Pt=function(e){if(!("mousedown"===e.type&&e.button>0)){if(qt)return void e.preventDefault();if(!B||"mousedown"!==e.type){if(It(e,!0)&&e.preventDefault(),Se("pointerDown"),D){var t=i.arraySearch(st,e.pointerId,"id");t<0&&(t=st.length),st[t]={x:e.pageX,y:e.pageY,id:e.pointerId}}var n=Mt(e),o=n.length;X=null,et(),G&&1!==o||(G=re=!0,i.bind(window,f,r),H=se=ae=W=$=q=Y=j=!1,ie=null,Se("firstTouchStart",n),Pe(pe,fe),de.x=de.y=0,Pe(at,n[0]),Pe(lt,at),ut.x=we.x*he,ct=[{x:at.x,y:at.y}],U=N=ke(),He(v,!0),bt(),_t()),!J&&o>1&&!oe&&!$&&(w=v,j=!1,J=Y=!0,de.y=de.x=0,Pe(pe,fe),Pe(ot,n[0]),Pe(it,n[1]),kt(ot,it,wt),vt.x=Math.abs(wt.x)-fe.x,vt.y=Math.abs(wt.y)-fe.y,Q=ee=xt(ot,it))}}},Zt=function(e){if(e.preventDefault(),D){var t=i.arraySearch(st,e.pointerId,"id");if(t>-1){var n=st[t];n.x=e.pageX,n.y=e.pageY}}if(G){var o=Mt(e);if(ie||q||J)X=o;else if(ht.x!==we.x*he)ie="h";else{var r=Math.abs(o[0].x-at.x)-Math.abs(o[0].y-at.y);Math.abs(r)>=10&&(ie=r>0?"h":"v",X=o)}}},zt=function(){if(X){var e=X.length;if(0!==e)if(Pe(ot,X[0]),rt.x=ot.x-at.x,rt.y=ot.y-at.y,J&&e>1){if(at.x=ot.x,at.y=ot.y,!rt.x&&!rt.y&&gt(X[1],it))return;Pe(it,X[1]),j||(j=!0,Se("zoomGestureStarted"));var t=xt(ot,it),n=Wt(t);n>r.currItem.initialZoomLevel+r.currItem.initialZoomLevel/15&&(se=!0);var o=1,i=We(),l=Be();if(n<i)if(a.pinchToClose&&!se&&w<=r.currItem.initialZoomLevel){var s=i-n,u=1-s/(i/1.2);De(u),Se("onPinchClose",u),ae=!0}else o=(i-n)/i,o>1&&(o=1),n=i-o*(i/3);else n>l&&(o=(n-l)/(6*i),o>1&&(o=1),n=l+o*i);o<0&&(o=0),Q=t,kt(ot,it,ft),de.x+=ft.x-wt.x,de.y+=ft.y-wt.y,Pe(wt,ft),fe.x=Re("x",n),fe.y=Re("y",n),H=n>v,v=n,Le()}else{if(!ie)return;if(re&&(re=!1,Math.abs(rt.x)>=10&&(rt.x-=X[0].x-lt.x),Math.abs(rt.y)>=10&&(rt.y-=X[0].y-lt.y)),at.x=ot.x,at.y=ot.y,0===rt.x&&0===rt.y)return;if("v"===ie&&a.closeOnVerticalDrag&&!Ct()){de.y+=rt.y,fe.y+=rt.y;var c=Ot();return W=!0,Se("onVerticalDrag",c),De(c),void Le()}Dt(ke(),ot.x,ot.y),q=!0,te=r.currItem.bounds;var d=Rt("x",rt);d||(Rt("y",rt),Ze(fe),Le())}}},Nt=function(e){if(Z.isOldAndroid){if(B&&"mouseup"===e.type)return;e.type.indexOf("touch")>-1&&(clearTimeout(B),B=setTimeout(function(){B=0},600))}Se("pointerUp"),It(e,!1)&&e.preventDefault();var t;if(D){var n=i.arraySearch(st,e.pointerId,"id");if(n>-1)if(t=st.splice(n,1)[0],navigator.msPointerEnabled){var o={4:"mouse",2:"touch",3:"pen"};t.type=o[e.pointerType],t.type||(t.type=e.pointerType||"mouse")}else t.type=e.pointerType||"mouse"}var l,s=Mt(e),u=s.length;if("mouseup"===e.type&&(u=0),2===u)return X=null,!0;1===u&&Pe(lt,s[0]),0!==u||ie||oe||(t||("mouseup"===e.type?t={x:e.pageX,y:e.pageY,type:"mouse"}:e.changedTouches&&e.changedTouches[0]&&(t={x:e.changedTouches[0].pageX,y:e.changedTouches[0].pageY,type:"touch"})),Se("touchRelease",e,t));var c=-1;if(0===u&&(G=!1,i.unbind(window,f,r),bt(),J?c=0:-1!==mt&&(c=ke()-mt)),mt=1===u?ke():-1,l=-1!==c&&c<150?"zoom":"swipe",J&&u<2&&(J=!1,1===u&&(l="zoomPointerUp"),Se("zoomGestureEnded")),X=null,q||j||oe||W)if(et(),K||(K=Ut()),K.calculateSwipeSpeed("x"),W){var d=Ot();if(d<a.verticalDragRange)r.close();else{var p=fe.y,m=le;tt("verticalDrag",0,1,300,i.easing.cubic.out,function(e){fe.y=(r.currItem.initialPosition.y-p)*e+p,De((1-m)*e+m),Le()}),Se("onVerticalDrag",1)}}else{if(($||oe)&&0===u){var h=Ht(l,K);if(h)return;l="zoomPointerUp"}if(!oe)return"swipe"!==l?void Bt():void(!$&&v>r.currItem.fitRatio&&Kt(K))}},Ut=function(){var e,t,n={lastFlickOffset:{},lastFlickDist:{},lastFlickSpeed:{},slowDownRatio:{},slowDownRatioReverse:{},speedDecelerationRatio:{},speedDecelerationRatioAbs:{},distanceOffset:{},backAnimDestination:{},backAnimStarted:{},calculateSwipeSpeed:function(o){ct.length>1?(e=ke()-U+50,t=ct[ct.length-2][o]):(e=ke()-N,t=lt[o]),n.lastFlickOffset[o]=at[o]-t,n.lastFlickDist[o]=Math.abs(n.lastFlickOffset[o]),n.lastFlickDist[o]>20?n.lastFlickSpeed[o]=n.lastFlickOffset[o]/e:n.lastFlickSpeed[o]=0,Math.abs(n.lastFlickSpeed[o])<.1&&(n.lastFlickSpeed[o]=0),n.slowDownRatio[o]=.95,n.slowDownRatioReverse[o]=1-n.slowDownRatio[o],n.speedDecelerationRatio[o]=1},calculateOverBoundsAnimOffset:function(e,t){n.backAnimStarted[e]||(fe[e]>te.min[e]?n.backAnimDestination[e]=te.min[e]:fe[e]<te.max[e]&&(n.backAnimDestination[e]=te.max[e]),void 0!==n.backAnimDestination[e]&&(n.slowDownRatio[e]=.7,n.slowDownRatioReverse[e]=1-n.slowDownRatio[e],n.speedDecelerationRatioAbs[e]<.05&&(n.lastFlickSpeed[e]=0,n.backAnimStarted[e]=!0,tt("bounceZoomPan"+e,fe[e],n.backAnimDestination[e],t||300,i.easing.sine.out,function(t){fe[e]=t,Le()}))))},calculateAnimOffset:function(e){n.backAnimStarted[e]||(n.speedDecelerationRatio[e]=n.speedDecelerationRatio[e]*(n.slowDownRatio[e]+n.slowDownRatioReverse[e]-n.slowDownRatioReverse[e]*n.timeDiff/10),n.speedDecelerationRatioAbs[e]=Math.abs(n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]),n.distanceOffset[e]=n.lastFlickSpeed[e]*n.speedDecelerationRatio[e]*n.timeDiff,fe[e]+=n.distanceOffset[e])},panAnimLoop:function(){if($e.zoomPan&&($e.zoomPan.raf=L(n.panAnimLoop),n.now=ke(),n.timeDiff=n.now-n.lastNow,n.lastNow=n.now,n.calculateAnimOffset("x"),n.calculateAnimOffset("y"),Le(),n.calculateOverBoundsAnimOffset("x"),n.calculateOverBoundsAnimOffset("y"),n.speedDecelerationRatioAbs.x<.05&&n.speedDecelerationRatioAbs.y<.05))return fe.x=Math.round(fe.x),fe.y=Math.round(fe.y),Le(),void Je("zoomPan")}};return n},Kt=function(e){if(e.calculateSwipeSpeed("y"),te=r.currItem.bounds,e.backAnimDestination={},e.backAnimStarted={},Math.abs(e.lastFlickSpeed.x)<=.05&&Math.abs(e.lastFlickSpeed.y)<=.05)return e.speedDecelerationRatioAbs.x=e.speedDecelerationRatioAbs.y=0,e.calculateOverBoundsAnimOffset("x"),e.calculateOverBoundsAnimOffset("y"),!0;Qe("zoomPan"),e.lastNow=ke(),e.panAnimLoop()},Ht=function(e,t){var n;oe||(pt=c);var o;if("swipe"===e){var l=at.x-lt.x,s=t.lastFlickDist.x<10;l>30&&(s||t.lastFlickOffset.x>20)?o=-1:l<-30&&(s||t.lastFlickOffset.x<-20)&&(o=1)}var u;o&&(c+=o,c<0?(c=a.loop?$t()-1:0,u=!0):c>=$t()&&(c=a.loop?0:$t()-1,u=!0),u&&!a.loop||(ge+=o,he-=o,n=!0));var d,p=we.x*he,f=Math.abs(p-ht.x);return n||p>ht.x==t.lastFlickSpeed.x>0?(d=Math.abs(t.lastFlickSpeed.x)>0?f/Math.abs(t.lastFlickSpeed.x):333,d=Math.min(d,400),d=Math.max(d,250)):d=333,pt===c&&(n=!1),oe=!0,Se("mainScrollAnimStart"),tt("mainScroll",ht.x,p,d,i.easing.cubic.out,Me,function(){et(),oe=!1,pt=-1,(n||pt!==c)&&r.updateCurrItem(),Se("mainScrollAnimComplete")}),n&&r.updateCurrItem(!0),n},Wt=function(e){return 1/ee*e*w},Bt=function(){var e=v,t=We(),n=Be();v<t?e=t:v>n&&(e=n);var o,a=le;return ae&&!H&&!se&&v<t?(r.close(),!0):(ae&&(o=function(e){De((1-a)*e+a)}),r.zoomTo(e,0,200,i.easing.cubic.out,o),!0)};Ce("Gestures",{publicMethods:{initGestures:function(){var e=function(e,t,n,o,i){E=e+t,T=e+n,I=e+o,S=i?e+i:""};D=Z.pointerEvent,D&&Z.touch&&(Z.touch=!1),D?navigator.msPointerEnabled?e("MSPointer","Down","Move","Up","Cancel"):e("pointer","down","move","up","cancel"):Z.touch?(e("touch","start","move","end","cancel"),O=!0):e("mouse","down","move","up"),f=T+" "+I+" "+S,m=E,D&&!O&&(O=navigator.maxTouchPoints>1||navigator.msMaxTouchPoints>1),r.likelyTouchDevice=O,h[E]=Pt,h[T]=Zt,h[I]=Nt,S&&(h[S]=h[I]),Z.touch&&(m+=" mousedown",f+=" mousemove mouseup",h.mousedown=h[E],h.mousemove=h[T],h.mouseup=h[I]),O||(a.allowPanToNext=!1)}}});var Gt,Yt,jt,qt,Vt,$t,Xt,Jt=function(t,n,o,l){Gt&&clearTimeout(Gt),qt=!0,jt=!0;var s;t.initialLayout?(s=t.initialLayout,t.initialLayout=null):s=a.getThumbBoundsFn&&a.getThumbBoundsFn(c);var d=o?a.hideAnimationDuration:a.showAnimationDuration,p=function(){Je("initialZoom"),o?(r.template.removeAttribute("style"),r.bg.removeAttribute("style")):(De(1),n&&(n.style.display="block"),i.addClass(e,"pswp--animated-in"),Se("initialZoom"+(o?"OutEnd":"InEnd"))),l&&l(),qt=!1};if(!d||!s||void 0===s.x)return Se("initialZoom"+(o?"Out":"In")),v=t.initialZoomLevel,Pe(fe,t.initialPosition),Le(),e.style.opacity=o?0:1,De(1),void(d?setTimeout(function(){p()},d):p());!function(){var n=u,l=!r.currItem.src||r.currItem.loadError||a.showHideOpacity;t.miniImg&&(t.miniImg.style.webkitBackfaceVisibility="hidden"),o||(v=s.w/t.w,fe.x=s.x,fe.y=s.y-M,r[l?"template":"bg"].style.opacity=.001,Le()),Qe("initialZoom"),o&&!n&&i.removeClass(e,"pswp--animated-in"),l&&(o?i[(n?"remove":"add")+"Class"](e,"pswp--animate_opacity"):setTimeout(function(){i.addClass(e,"pswp--animate_opacity")},30)),Gt=setTimeout(function(){if(Se("initialZoom"+(o?"Out":"In")),o){var r=s.w/t.w,a={x:fe.x,y:fe.y},u=v,c=le,f=function(t){1===t?(v=r,fe.x=s.x,fe.y=s.y-P):(v=(r-u)*t+u,fe.x=(s.x-a.x)*t+a.x,fe.y=(s.y-P-a.y)*t+a.y),Le(),l?e.style.opacity=1-t:De(c-t*c)};n?tt("initialZoom",0,1,d,i.easing.cubic.out,f,p):(f(1),Gt=setTimeout(p,d+20))}else v=t.initialZoomLevel,Pe(fe,t.initialPosition),Le(),De(1),l?e.style.opacity=1:De(1),Gt=setTimeout(p,d+20)},o?25:90)}()},Qt={},en=[],tn={index:0,errorMsg:'<div class="pswp__error-msg"><a href="%url%" target="_blank">The image</a> could not be loaded.</div>',forceProgressiveLoading:!1,preload:[1,1],getNumItemsFn:function(){return Yt.length}},nn=function(){return{center:{x:0,y:0},max:{x:0,y:0},min:{x:0,y:0}}},on=function(e,t,n){var o=e.bounds;o.center.x=Math.round((Qt.x-t)/2),o.center.y=Math.round((Qt.y-n)/2)+e.vGap.top,o.max.x=t>Qt.x?Math.round(Qt.x-t):o.center.x,o.max.y=n>Qt.y?Math.round(Qt.y-n)+e.vGap.top:o.center.y,o.min.x=t>Qt.x?0:o.center.x,o.min.y=n>Qt.y?e.vGap.top:o.center.y},rn=function(e,t,n){if(e.src&&!e.loadError){var o=!n;if(o&&(e.vGap||(e.vGap={top:0,bottom:0}),Se("parseVerticalMargin",e)),Qt.x=t.x,Qt.y=t.y-e.vGap.top-e.vGap.bottom,o){var i=Qt.x/e.w,r=Qt.y/e.h;e.fitRatio=i<r?i:r;var l=a.scaleMode;"orig"===l?n=1:"fit"===l&&(n=e.fitRatio),n>1&&(n=1),e.initialZoomLevel=n,e.bounds||(e.bounds=nn())}if(!n)return;return on(e,e.w*n,e.h*n),o&&n===e.initialZoomLevel&&(e.initialPosition=e.bounds.center),e.bounds}return e.w=e.h=0,e.initialZoomLevel=e.fitRatio=1,e.bounds=nn(),e.initialPosition=e.bounds.center,e.bounds},an=function(e,t,n,o,i,a){t.loadError||o&&(t.imageAppended=!0,un(t,o,t===r.currItem&&_e),n.appendChild(o),a&&setTimeout(function(){t&&t.loaded&&t.placeholder&&(t.placeholder.style.display="none",t.placeholder=null)},500))},ln=function(e){e.loading=!0,e.loaded=!1;var t=e.img=i.createEl("pswp__img","img"),n=function(){e.loading=!1,e.loaded=!0,e.loadComplete?e.loadComplete(e):e.img=null,t.onload=t.onerror=null,t=null};return t.onload=n,t.onerror=function(){e.loadError=!0,n()},t.src=e.src,t},sn=function(e,t){if(e.src&&e.loadError&&e.container)return t&&(e.container.innerHTML=""),e.container.innerHTML=a.errorMsg.replace("%url%",e.src),!0},un=function(e,t,n){if(e.src){t||(t=e.container.lastChild);var o=n?e.w:Math.round(e.w*e.fitRatio),i=n?e.h:Math.round(e.h*e.fitRatio);e.placeholder&&!e.loaded&&(e.placeholder.style.width=o+"px",e.placeholder.style.height=i+"px"),t.style.width=o+"px",t.style.height=i+"px"}},cn=function(){if(en.length){for(var e,t=0;t<en.length;t++)e=en[t],e.holder.index===e.index&&an(e.index,e.item,e.baseDiv,e.img,0,e.clearPlaceholder);en=[]}};Ce("Controller",{publicMethods:{lazyLoadItem:function(e){e=Ee(e);var t=Vt(e);t&&(!t.loaded&&!t.loading||b)&&(Se("gettingData",e,t),t.src&&ln(t))},initController:function(){i.extend(a,tn,!0),r.items=Yt=n,Vt=r.getItemAt,$t=a.getNumItemsFn,Xt=a.loop,$t()<3&&(a.loop=!1),Ie("beforeChange",function(e){var t,n=a.preload,o=null===e||e>=0,i=Math.min(n[0],$t()),l=Math.min(n[1],$t());for(t=1;t<=(o?l:i);t++)r.lazyLoadItem(c+t);for(t=1;t<=(o?i:l);t++)r.lazyLoadItem(c-t)}),Ie("initialLayout",function(){r.currItem.initialLayout=a.getThumbBoundsFn&&a.getThumbBoundsFn(c)}),Ie("mainScrollAnimComplete",cn),Ie("initialZoomInEnd",cn),Ie("destroy",function(){for(var e,t=0;t<Yt.length;t++)e=Yt[t],e.container&&(e.container=null),e.placeholder&&(e.placeholder=null),e.img&&(e.img=null),e.preloader&&(e.preloader=null),e.loadError&&(e.loaded=e.loadError=!1);en=null})},getItemAt:function(e){return e>=0&&(void 0!==Yt[e]&&Yt[e])},allowProgressiveImg:function(){return a.forceProgressiveLoading||!O||a.mouseUsed||screen.width>1200},setContent:function(e,t){a.loop&&(t=Ee(t));var n=r.getItemAt(e.index);n&&(n.container=null);var o,s=r.getItemAt(t);if(!s)return void(e.el.innerHTML="");Se("gettingData",t,s),e.index=t,e.item=s;var u=s.container=i.createEl("pswp__zoom-wrap");if(!s.src&&s.html&&(s.html.tagName?u.appendChild(s.html):u.innerHTML=s.html),sn(s),rn(s,me),!s.src||s.loadError||s.loaded)s.src&&!s.loadError&&(o=i.createEl("pswp__img","img"),o.style.opacity=1,o.src=s.src,un(s,o),an(0,s,u,o));else{if(s.loadComplete=function(n){if(l){if(e&&e.index===t){if(sn(n,!0))return n.loadComplete=n.img=null,rn(n,me),Fe(n),void(e.index===c&&r.updateCurrZoomItem());n.imageAppended?!qt&&n.placeholder&&(n.placeholder.style.display="none",n.placeholder=null):Z.transform&&(oe||qt)?en.push({item:n,baseDiv:u,img:n.img,index:t,holder:e,clearPlaceholder:!0}):an(0,n,u,n.img,0,!0)}n.loadComplete=null,n.img=null,Se("imageLoadComplete",t,n)}},i.features.transform){var d="pswp__img pswp__img--placeholder";d+=s.msrc?"":" pswp__img--placeholder--blank";var p=i.createEl(d,s.msrc?"img":"");s.msrc&&(p.src=s.msrc),un(s,p),u.appendChild(p),s.placeholder=p}s.loading||ln(s),r.allowProgressiveImg()&&(!jt&&Z.transform?en.push({item:s,baseDiv:u,img:s.img,index:t,holder:e}):an(0,s,u,s.img,0,!0))}jt||t!==c?Fe(s):(ne=u.style,Jt(s,o||s.img)),e.el.innerHTML="",e.el.appendChild(u)},cleanSlide:function(e){e.img&&(e.img.onload=e.img.onerror=null),e.loaded=e.loading=e.img=e.imageAppended=!1}}});var dn,pn={},fn=function(e,t,n){var o=document.createEvent("CustomEvent"),i={origEvent:e,target:e.target,releasePoint:t,pointerType:n||"touch"};o.initCustomEvent("pswpTap",!0,!0,i),e.target.dispatchEvent(o)};Ce("Tap",{publicMethods:{initTap:function(){Ie("firstTouchStart",r.onTapStart),Ie("touchRelease",r.onTapRelease),Ie("destroy",function(){pn={},dn=null})},onTapStart:function(e){e.length>1&&(clearTimeout(dn),dn=null)},onTapRelease:function(e,t){if(t&&!q&&!Y&&!Xe){var n=t;if(dn&&(clearTimeout(dn),dn=null,yt(n,pn)))return void Se("doubleTap",n);if("mouse"===t.type)return void fn(e,t,"mouse");if("BUTTON"===e.target.tagName.toUpperCase()||i.hasClass(e.target,"pswp__single-tap"))return void fn(e,t);Pe(pn,n),dn=setTimeout(function(){fn(e,t),dn=null},300)}}}});var mn;Ce("DesktopZoom",{publicMethods:{initDesktopZoom:function(){R||(O?Ie("mouseUsed",function(){r.setupDesktopZoom()}):r.setupDesktopZoom(!0))},setupDesktopZoom:function(t){mn={};var n="wheel mousewheel DOMMouseScroll";Ie("bindEvents",function(){i.bind(e,n,r.handleMouseWheel)}),Ie("unbindEvents",function(){mn&&i.unbind(e,n,r.handleMouseWheel)}),r.mouseZoomedIn=!1;var o,a=function(){r.mouseZoomedIn&&(i.removeClass(e,"pswp--zoomed-in"),r.mouseZoomedIn=!1),v<1?i.addClass(e,"pswp--zoom-allowed"):i.removeClass(e,"pswp--zoom-allowed"),l()},l=function(){o&&(i.removeClass(e,"pswp--dragging"),o=!1)};Ie("resize",a),Ie("afterChange",a),Ie("pointerDown",function(){r.mouseZoomedIn&&(o=!0,i.addClass(e,"pswp--dragging"))}),Ie("pointerUp",l),t||a()},handleMouseWheel:function(e){if(v<=r.currItem.fitRatio)return a.modal&&(!a.closeOnScroll||Xe||G?e.preventDefault():k&&Math.abs(e.deltaY)>2&&(u=!0,r.close())),!0;if(e.stopPropagation(),mn.x=0,"deltaX"in e)1===e.deltaMode?(mn.x=18*e.deltaX,mn.y=18*e.deltaY):(mn.x=e.deltaX,mn.y=e.deltaY);else if("wheelDelta"in e)e.wheelDeltaX&&(mn.x=-.16*e.wheelDeltaX),e.wheelDeltaY?mn.y=-.16*e.wheelDeltaY:mn.y=-.16*e.wheelDelta;else{if(!("detail"in e))return;mn.y=e.detail}He(v,!0);var t=fe.x-mn.x,n=fe.y-mn.y;(a.modal||t<=te.min.x&&t>=te.max.x&&n<=te.min.y&&n>=te.max.y)&&e.preventDefault(),r.panTo(t,n)},toggleDesktopZoom:function(t){t=t||{x:me.x/2+ve.x,y:me.y/2+ve.y};var n=a.getDoubleTapZoom(!0,r.currItem),o=v===n;r.mouseZoomedIn=!o,r.zoomTo(o?r.currItem.initialZoomLevel:n,t,333),i[(o?"remove":"add")+"Class"](e,"pswp--zoomed-in")}}});var hn,vn,wn,gn,yn,xn,bn,_n,Cn,En,Tn,In,Sn={history:!0,galleryUID:1},kn=function(){return Tn.hash.substring(1)},Dn=function(){hn&&clearTimeout(hn),wn&&clearTimeout(wn)},On=function(){var e=kn(),t={};if(e.length<5)return t;var n,o=e.split("&");for(n=0;n<o.length;n++)if(o[n]){var i=o[n].split("=");i.length<2||(t[i[0]]=i[1])}if(a.galleryPIDs){var r=t.pid;for(t.pid=0,n=0;n<Yt.length;n++)if(Yt[n].pid===r){t.pid=n;break}}else t.pid=parseInt(t.pid,10)-1;return t.pid<0&&(t.pid=0),t},Ln=function(){if(wn&&clearTimeout(wn),Xe||G)return void(wn=setTimeout(Ln,500));gn?clearTimeout(vn):gn=!0;var e=c+1,t=Vt(c);t.hasOwnProperty("pid")&&(e=t.pid);var n=bn+"&gid="+a.galleryUID+"&pid="+e;_n||-1===Tn.hash.indexOf(n)&&(En=!0);var o=Tn.href.split("#")[0]+"#"+n;In?"#"+n!==window.location.hash&&history[_n?"replaceState":"pushState"]("",document.title,o):_n?Tn.replace(o):Tn.hash=n,_n=!0,vn=setTimeout(function(){gn=!1},60)};Ce("History",{publicMethods:{initHistory:function(){if(i.extend(a,Sn,!0),a.history){Tn=window.location,En=!1,Cn=!1,_n=!1,bn=kn(),In="pushState"in history,bn.indexOf("gid=")>-1&&(bn=bn.split("&gid=")[0],bn=bn.split("?gid=")[0]),Ie("afterChange",r.updateURL),Ie("unbindEvents",function(){i.unbind(window,"hashchange",r.onHashChange)});var e=function(){xn=!0,Cn||(En?history.back():bn?Tn.hash=bn:In?history.pushState("",document.title,Tn.pathname+Tn.search):Tn.hash=""),Dn()};Ie("unbindEvents",function(){u&&e()}),Ie("destroy",function(){xn||e()}),Ie("firstUpdate",function(){c=On().pid});var t=bn.indexOf("pid=");t>-1&&(bn=bn.substring(0,t),"&"===bn.slice(-1)&&(bn=bn.slice(0,-1))),setTimeout(function(){l&&i.bind(window,"hashchange",r.onHashChange)},40)}},onHashChange:function(){if(kn()===bn)return Cn=!0,void r.close();gn||(yn=!0,r.goTo(On().pid),yn=!1)},updateURL:function(){Dn(),yn||(_n?hn=setTimeout(Ln,800):Ln())}}}),i.extend(r,nt)}})},function(e,t,n){var o,i;/*! PhotoSwipe Default UI - 4.1.3 - 2019-01-08
* http://photoswipe.com
* Copyright (c) 2019 Dmitry Semenov; */
!function(r,a){o=a,void 0!==(i="function"==typeof o?o.call(t,n,t,e):o)&&(e.exports=i)}(0,function(){"use strict";return function(e,t){var n,o,i,r,a,l,s,u,c,d,p,f,m,h,v,w,g,y,x,b=this,_=!1,C=!0,E=!0,T={barsSize:{top:44,bottom:"auto"},closeElClasses:["item","caption","zoom-wrap","ui","top-bar"],timeToIdle:4e3,timeToIdleOutside:1e3,loadingIndicatorDelay:1e3,addCaptionHTMLFn:function(e,t){return e.title?(t.children[0].innerHTML=e.title,!0):(t.children[0].innerHTML="",!1)},closeEl:!0,captionEl:!0,fullscreenEl:!0,zoomEl:!0,shareEl:!0,counterEl:!0,arrowEl:!0,preloaderEl:!0,tapToClose:!1,tapToToggleControls:!0,clickToCloseNonZoomable:!0,shareButtons:[{id:"facebook",label:"Share on Facebook",url:"https://www.facebook.com/sharer/sharer.php?u={{url}}"},{id:"twitter",label:"Tweet",url:"https://twitter.com/intent/tweet?text={{text}}&url={{url}}"},{id:"pinterest",label:"Pin it",url:"http://www.pinterest.com/pin/create/button/?url={{url}}&media={{image_url}}&description={{text}}"},{id:"download",label:"Download image",url:"{{raw_image_url}}",download:!0}],getImageURLForShare:function(){return e.currItem.src||""},getPageURLForShare:function(){return window.location.href},getTextForShare:function(){return e.currItem.title||""},indexIndicatorSep:" / ",fitControlsWidth:1200},I=function(e){if(w)return!0;e=e||window.event,v.timeToIdle&&v.mouseUsed&&!c&&P();for(var n,o,i=e.target||e.srcElement,r=i.getAttribute("class")||"",a=0;a<B.length;a++)n=B[a],n.onTap&&r.indexOf("pswp__"+n.name)>-1&&(n.onTap(),o=!0);if(o){e.stopPropagation&&e.stopPropagation(),w=!0;var l=t.features.isOldAndroid?600:30;g=setTimeout(function(){w=!1},l)}},S=function(){return!e.likelyTouchDevice||v.mouseUsed||screen.width>v.fitControlsWidth},k=function(e,n,o){t[(o?"add":"remove")+"Class"](e,"pswp__"+n)},D=function(){var e=1===v.getNumItemsFn();e!==h&&(k(o,"ui--one-slide",e),h=e)},O=function(){k(s,"share-modal--hidden",E)},L=function(){return E=!E,E?(t.removeClass(s,"pswp__share-modal--fade-in"),setTimeout(function(){E&&O()},300)):(O(),setTimeout(function(){E||t.addClass(s,"pswp__share-modal--fade-in")},30)),E||A(),!1},F=function(t){t=t||window.event;var n=t.target||t.srcElement;return e.shout("shareLinkClick",t,n),!!n.href&&(!!n.hasAttribute("download")||(window.open(n.href,"pswp_share","scrollbars=yes,resizable=yes,toolbar=no,location=yes,width=550,height=420,top=100,left="+(window.screen?Math.round(screen.width/2-275):100)),E||L(),!1))},A=function(){for(var e,t,n,o,i,r="",a=0;a<v.shareButtons.length;a++)e=v.shareButtons[a],n=v.getImageURLForShare(e),o=v.getPageURLForShare(e),i=v.getTextForShare(e),t=e.url.replace("{{url}}",encodeURIComponent(o)).replace("{{image_url}}",encodeURIComponent(n)).replace("{{raw_image_url}}",n).replace("{{text}}",encodeURIComponent(i)),r+='<a href="'+t+'" target="_blank" class="pswp__share--'+e.id+'"'+(e.download?"download":"")+">"+e.label+"</a>",v.parseShareButtonOut&&(r=v.parseShareButtonOut(e,r));s.children[0].innerHTML=r,s.children[0].onclick=F},M=function(e){for(var n=0;n<v.closeElClasses.length;n++)if(t.hasClass(e,"pswp__"+v.closeElClasses[n]))return!0},R=0,P=function(){clearTimeout(x),R=0,c&&b.setIdle(!1)},Z=function(e){e=e||window.event;var t=e.relatedTarget||e.toElement;t&&"HTML"!==t.nodeName||(clearTimeout(x),x=setTimeout(function(){b.setIdle(!0)},v.timeToIdleOutside))},z=function(){v.fullscreenEl&&!t.features.isOldAndroid&&(n||(n=b.getFullscreenAPI()),n?(t.bind(document,n.eventK,b.updateFullscreen),b.updateFullscreen(),t.addClass(e.template,"pswp--supports-fs")):t.removeClass(e.template,"pswp--supports-fs"))},N=function(){v.preloaderEl&&(U(!0),d("beforeChange",function(){clearTimeout(m),m=setTimeout(function(){e.currItem&&e.currItem.loading?(!e.allowProgressiveImg()||e.currItem.img&&!e.currItem.img.naturalWidth)&&U(!1):U(!0)},v.loadingIndicatorDelay)}),d("imageLoadComplete",function(t,n){e.currItem===n&&U(!0)}))},U=function(e){f!==e&&(k(p,"preloader--active",!e),f=e)},K=function(e){var n=e.vGap;if(S()){var a=v.barsSize;if(v.captionEl&&"auto"===a.bottom)if(r||(r=t.createEl("pswp__caption pswp__caption--fake"),r.appendChild(t.createEl("pswp__caption__center")),o.insertBefore(r,i),t.addClass(o,"pswp__ui--fit")),v.addCaptionHTMLFn(e,r,!0)){var l=r.clientHeight;n.bottom=parseInt(l,10)||44}else n.bottom=a.top;else n.bottom="auto"===a.bottom?0:a.bottom;n.top=a.top}else n.top=n.bottom=0},H=function(){v.timeToIdle&&d("mouseUsed",function(){t.bind(document,"mousemove",P),t.bind(document,"mouseout",Z),y=setInterval(function(){2===++R&&b.setIdle(!0)},v.timeToIdle/2)})},W=function(){d("onVerticalDrag",function(e){C&&e<.95?b.hideControls():!C&&e>=.95&&b.showControls()});var e;d("onPinchClose",function(t){C&&t<.9?(b.hideControls(),e=!0):e&&!C&&t>.9&&b.showControls()}),d("zoomGestureEnded",function(){(e=!1)&&!C&&b.showControls()})},B=[{name:"caption",option:"captionEl",onInit:function(e){i=e}},{name:"share-modal",option:"shareEl",onInit:function(e){s=e},onTap:function(){L()}},{name:"button--share",option:"shareEl",onInit:function(e){l=e},onTap:function(){L()}},{name:"button--zoom",option:"zoomEl",onTap:e.toggleDesktopZoom},{name:"counter",option:"counterEl",onInit:function(e){a=e}},{name:"button--close",option:"closeEl",onTap:e.close},{name:"button--arrow--left",option:"arrowEl",onTap:e.prev},{name:"button--arrow--right",option:"arrowEl",onTap:e.next},{name:"button--fs",option:"fullscreenEl",onTap:function(){n.isFullscreen()?n.exit():n.enter()}},{name:"preloader",option:"preloaderEl",onInit:function(e){p=e}}],G=function(){var e,n,i,r=function(o){if(o)for(var r=o.length,a=0;a<r;a++){e=o[a],n=e.className;for(var l=0;l<B.length;l++)i=B[l],n.indexOf("pswp__"+i.name)>-1&&(v[i.option]?(t.removeClass(e,"pswp__element--disabled"),i.onInit&&i.onInit(e)):t.addClass(e,"pswp__element--disabled"))}};r(o.children);var a=t.getChildByClass(o,"pswp__top-bar");a&&r(a.children)};b.init=function(){t.extend(e.options,T,!0),v=e.options,o=t.getChildByClass(e.scrollWrap,"pswp__ui"),d=e.listen,W(),d("beforeChange",b.update),d("doubleTap",function(t){var n=e.currItem.initialZoomLevel;e.getZoomLevel()!==n?e.zoomTo(n,t,333):e.zoomTo(v.getDoubleTapZoom(!1,e.currItem),t,333)}),d("preventDragEvent",function(e,t,n){var o=e.target||e.srcElement;o&&o.getAttribute("class")&&e.type.indexOf("mouse")>-1&&(o.getAttribute("class").indexOf("__caption")>0||/(SMALL|STRONG|EM)/i.test(o.tagName))&&(n.prevent=!1)}),d("bindEvents",function(){t.bind(o,"pswpTap click",I),t.bind(e.scrollWrap,"pswpTap",b.onGlobalTap),e.likelyTouchDevice||t.bind(e.scrollWrap,"mouseover",b.onMouseOver)}),d("unbindEvents",function(){E||L(),y&&clearInterval(y),t.unbind(document,"mouseout",Z),t.unbind(document,"mousemove",P),t.unbind(o,"pswpTap click",I),t.unbind(e.scrollWrap,"pswpTap",b.onGlobalTap),t.unbind(e.scrollWrap,"mouseover",b.onMouseOver),n&&(t.unbind(document,n.eventK,b.updateFullscreen),n.isFullscreen()&&(v.hideAnimationDuration=0,n.exit()),n=null)}),d("destroy",function(){v.captionEl&&(r&&o.removeChild(r),t.removeClass(i,"pswp__caption--empty")),s&&(s.children[0].onclick=null),t.removeClass(o,"pswp__ui--over-close"),t.addClass(o,"pswp__ui--hidden"),b.setIdle(!1)}),v.showAnimationDuration||t.removeClass(o,"pswp__ui--hidden"),d("initialZoomIn",function(){v.showAnimationDuration&&t.removeClass(o,"pswp__ui--hidden")}),d("initialZoomOut",function(){t.addClass(o,"pswp__ui--hidden")}),d("parseVerticalMargin",K),G(),v.shareEl&&l&&s&&(E=!0),D(),H(),z(),N()},b.setIdle=function(e){c=e,k(o,"ui--idle",e)},b.update=function(){C&&e.currItem?(b.updateIndexIndicator(),v.captionEl&&(v.addCaptionHTMLFn(e.currItem,i),k(i,"caption--empty",!e.currItem.title)),_=!0):_=!1,E||L(),D()},b.updateFullscreen=function(o){o&&setTimeout(function(){e.setScrollOffset(0,t.getScrollY())},50),t[(n.isFullscreen()?"add":"remove")+"Class"](e.template,"pswp--fs")},b.updateIndexIndicator=function(){v.counterEl&&(a.innerHTML=e.getCurrentIndex()+1+v.indexIndicatorSep+v.getNumItemsFn())},b.onGlobalTap=function(n){n=n||window.event;var o=n.target||n.srcElement;if(!w)if(n.detail&&"mouse"===n.detail.pointerType){if(M(o))return void e.close();t.hasClass(o,"pswp__img")&&(1===e.getZoomLevel()&&e.getZoomLevel()<=e.currItem.fitRatio?v.clickToCloseNonZoomable&&e.close():e.toggleDesktopZoom(n.detail.releasePoint))}else if(v.tapToToggleControls&&(C?b.hideControls():b.showControls()),v.tapToClose&&(t.hasClass(o,"pswp__img")||M(o)))return void e.close()},b.onMouseOver=function(e){e=e||window.event;var t=e.target||e.srcElement;k(o,"ui--over-close",M(t))},b.hideControls=function(){t.addClass(o,"pswp__ui--hidden"),C=!1},b.showControls=function(){C=!0,_||b.update(),t.removeClass(o,"pswp__ui--hidden")},b.supportsFullscreen=function(){var e=document;return!!(e.exitFullscreen||e.mozCancelFullScreen||e.webkitExitFullscreen||e.msExitFullscreen)},b.getFullscreenAPI=function(){var t,n=document.documentElement,o="fullscreenchange";return n.requestFullscreen?t={enterK:"requestFullscreen",exitK:"exitFullscreen",elementK:"fullscreenElement",eventK:o}:n.mozRequestFullScreen?t={enterK:"mozRequestFullScreen",exitK:"mozCancelFullScreen",elementK:"mozFullScreenElement",eventK:"moz"+o}:n.webkitRequestFullscreen?t={enterK:"webkitRequestFullscreen",exitK:"webkitExitFullscreen",elementK:"webkitFullscreenElement",eventK:"webkit"+o}:n.msRequestFullscreen&&(t={enterK:"msRequestFullscreen",exitK:"msExitFullscreen",elementK:"msFullscreenElement",eventK:"MSFullscreenChange"}),t&&(t.enter=function(){if(u=v.closeOnScroll,v.closeOnScroll=!1,"webkitRequestFullscreen"!==this.enterK)return e.template[this.enterK]();e.template[this.enterK](Element.ALLOW_KEYBOARD_INPUT)},t.exit=function(){return v.closeOnScroll=u,document[this.exitK]()},t.isFullscreen=function(){return document[this.elementK]}),t}}})}])});