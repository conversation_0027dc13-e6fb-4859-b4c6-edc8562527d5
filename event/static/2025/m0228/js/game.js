var hasMobile = "1"
var itemName = ""
var openId = "";

function getCookie(name) {
	var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
	console.log(arr);
	if (arr != null) return unescape(arr[2]);
	return null;
}



$(function() {
	// var ltapi = "http://*************:8081";
	// var ltapi = "http://wxapi.fangxiaoer.com";
	
	
	
	/* var ltapi = "https://ltapi.fangxiaoer.com"
	var openId = "okQkzuA4EsCyBogvQgIpxr0AmWoU"; */

	var	openId=''
	var ltapi = "https://ltapi.fangxiaoer.com";
	openId = getCookie("openId")
	if(openId == "" || openId == null) {
		 window.location = ltapi + "/apiv1/wechat/activity/51.htm"
	}

	


	var status = 0
	window.onload = function() {
		var carGame = {
			stage: new createjs.Stage("gameView"), //舞台创建
			container: new createjs.Container(), //创建容器,
			bgSpeed: 4,
			time: 1500,
			initRed: 0,
			initScore: 0,
			carBody: null,
			queue: null,
			tempImgArray: [],
			tempRedArray: [],
			noCreate: [],
			redText: null,
			redFlag: true,
			image1: new createjs.Bitmap("../static/2022/m0308/img/div2-bg.jpg"), //创建位图
			image2: new createjs.Bitmap("../static/2022/m0308/img/div2-bg.jpg"), //创建位图,
			arrAyUnique: function(arr) { //数组去重
				arr.sort(function(a, b) {
					return a - b;
				});
				var result = [arr[0]];
				for (var i = 1; i < arr.length; i++) {
					if (arr[i] !== result[result.length - 1]) {
						result.push(arr[i]);
					}
				}
				return result;
			},
			handleImg: function(obj) {
				var pt = obj.localToLocal(Math.random() * 100, Math.random() * 100, this.carBody);
				if (this.carBody.hitTest(pt.x, pt.y)) {
					obj.visible = false;
					if (/1.png/.test(obj.image.src) || /2.png/.test(obj.image.src) || /3.png/.test(
							obj.image.src) || /4.png/.test(obj.image.src)) {
						obj.tickEnabled = false;
						this.tempRedArray.push(obj.id);
						//去重red.id
						this.noCreate.push(obj.id);
						var temp = this.arrAyUnique(this.tempRedArray);
						if (temp.length != this.initRed) {
							this.initScore += 10
							this.createImages();
						}
						this.initRed = temp.length;
						this.redText.text = "" + this.initScore; //得分
						$(".def").text(this.initScore)
					} else if (/5.png/.test(obj.image.src)) {
						obj.tickEnabled = false;
						this.tempRedArray.push(obj.id);
						//去重red.id
						this.noCreate.push(obj.id);
						var temp = this.arrAyUnique(this.tempRedArray);
						if (temp.length != this.initRed) {
							this.initScore += 5
							this.createImages();
						}
						this.initRed = temp.length;
						this.redText.text = "" + this.initScore; //得分
						$(".def").text(this.initScore)
					} else {
						this.container.removeChild(obj);
						createjs.Sound.play("sound");
						createjs.Sound.removeSound("victory");
						var score = this.initScore
						console.log(score)
						console.log('游戏结束')
						$(".numResultSpan").text(score)
						if (score >= 100) {
							$(".popup1").show()
							$(".popup").show()
						} else {
							$(".popup2").show()
							$(".popup").show()
						}

						//记录游戏分数
						console.log('记录游戏分数' + score)
						$.ajax({
							type: "POST",
							url: ltapi + "/apiv1/wechat/normalRecordResult",
							data: {
								'openId': openId,
								'score': score,
								'activityId': 49
							},
							success: function(data) {
								console.log(data)
							}
						})


						createjs.Sound.stop(); //停止背景音乐播放
						createjs.Ticker.removeAllEventListeners();

						//炸弹声
						// document.getElementById('vaud').play();
						// $(".aa").click()

					}
				}
			}
		};
		//初始化
		carGame.init = function() {

			createjs.Touch.enable(this.stage);
			this.image2.y = -this.stage.canvas.height; //设置位图2y轴位置
			//生成roadDom
			var roadHtml = document.createElement('div');
			roadHtml.id = 'road';
			document.body.appendChild(roadHtml);
			var roadDom = new createjs.DOMElement(roadHtml);


			//红包或者炸弹信息
			var redBitMap = new createjs.Bitmap('../static/2022/m0308/img/1.png');
			var redBitMap1 = new createjs.Bitmap('../static/2022/m0308/img/2.png');
			var redBitMap2 = new createjs.Bitmap('../static/2022/m0308/img/3.png');
			var redBitMap3 = new createjs.Bitmap('../static/2022/m0308/img/4.png');
			// var otherBitMap = new createjs.Bitmap('../static/2022/m0308/img/5.png');
			var boomBitMap = new createjs.Bitmap('../static/2022/m0308/img/0.png');
			this.tempImgArray.push(boomBitMap, redBitMap, redBitMap1, redBitMap2, redBitMap3);

			//显示红包个数文字  得分
			$(".def").text(this.initScore)
			this.redText = new createjs.Text("" + this.initScore, "28px 微软雅黑", "#ffffff");
			this.redText.x = 115;
			this.redText.y = -50;

			//生成小车
			var carImg = '../static/2022/m0308/img/6.png';
			this.carBody = new createjs.Bitmap(carImg);
			//小车大小与设置x轴y轴位置
			this.carBody.scaleX = 1;
			this.carBody.scaleY = 1;
			this.carBody.x = 324; //小车x轴初始位置
			this.carBody.y = this.stage.canvas.height - 254; //小车y轴初始位置

			//文件加载
			var _this = this;
			this.queue = new createjs.LoadQueue();
			this.queue.installPlugin(createjs.Sound);
			this.queue.loadManifest([{
				id: "sound",
				src: "../static/2022/m0308/mp3/boom.mp3"
			}, {
				id: "victory",
				src: "../static/2022/m0308/mp3/viry.mp3"
			}, {
				id: "bigBg",
				src: "../static/2022/m0308/img/bg.jpg"
			}]);
			//文件加载进程
			this.queue.on("progress", function() {
				document.querySelector("#load-msg").innerText = "加载中 " + parseInt(_this.queue
					.progress * 100) + "%...";
				//加载完成把进度条隐藏
				if (parseInt(_this.queue.progress * 100) === 100) {
					document.querySelector("#load-msg").style.display = "none";
					$(".timg,.def").show()
				}
			});
			//文件加载完成执行
			this.queue.addEventListener("complete", function() {
				status = 1
				createjs.Sound.play("victory");
				_this.stage.addChild(_this.container); //将容器添加到舞台
				_this.container.addChild(_this.image1); //将位图1添加到容器
				_this.container.addChild(_this.image2); //将位图2添加到容器
				_this.stage.addChild(roadDom); //将roadDom添加到容器
				_this.container.addChild(_this.redText); //将整体内容处理
				_this.container.addChild(_this.carBody); //将小车的位图添加到容器

				//调用生成炸弹或者红包函数
				_this.createImages();
				setTimeout(function() {
					_this.createImages()
				}, 300);
				setTimeout(function() {
					_this.createImages()
				}, 600);
				setTimeout(function() {
					_this.createImages()
				}, 800);
				//小车拖动
				_this.moveCar();
				//背景无限滚动
				createjs.Ticker.addEventListener("tick", function() {
					_this.handleTick(_this.image1, _this.image2);
					_this.stage.update();
				});
				createjs.Ticker.setFPS(60); //设置帧频
			});
		};
		//背景图无限运动
		carGame.handleTick = function(image1, image2) {
			//背景图速度调整***
			image1.y += this.bgSpeed * (1 + 2 * Math.sin(this.initRed / 60));
			image2.y += this.bgSpeed * (1 + 2 * Math.sin(this.initRed / 60));
			if (Math.abs(image1.y) >= this.stage.canvas.height) {
				image1.y = 0;
				image2.y = -this.stage.canvas.height;
			}
		};

		//生成红包或者炸弹
		carGame.createImages = function() {
			var index = 0;
			var result = parseInt(Math.random() * 100)
			if (result <= 50) {
				index = 0
			} else if (result < 66) {
				index = 1
			} else if (result < 82) {
				index = 2
			} else {
				index = 3
			}
			/* else if(result < 70) {
							index = 4
						} else {
							index = 5
						}*/
			var red = this.tempImgArray[index].clone(); //获取到图片地址
			//设置左边红包或者炸弹x轴位置
			var minX = this.tempImgArray[index].getBounds().width / 2
			var maxX = 750 - this.tempImgArray[index].getBounds().width / 2
			//设置红包或者炸点显示x轴位置
			red.x = (maxX - minX) * Math.random();
			red.y = -160 * (Math.random() < 0.5 ? 0.5 : Math.random());

			this.container.addChild(red);
			//添加一个监听器
			var _this = this;
			//判断红包是否碰撞
			//掉落速度调整***
			var times = _this.time
			if (_this.initRed < 60) {
				times = _this.time * (1 - 0.75 * Math.sin(_this.initRed / 60))
			} else {
				times = _this.time / 40
			}
			createjs.Tween.get(red).to({
				y: _this.stage.canvas.height
			}, times).addEventListener("change", function() {
				_this.handleImg(red);
				if (red.y >= _this.stage.canvas.height) {
					if (_this.noCreate.indexOf(red.id) != -1) {} else {
						_this.createImages();
					}
				}
			});
		};
		//拖动小车
		carGame.moveCar = function() {
			var _this = this;
			var minX = _this.carBody.getBounds().width / 2;
			var maxX = 750 - _this.carBody.getBounds().width / 2;

			_this.carBody.on("pressmove", function(evt) {
				//判断是否拖出了左右上下范围
				if (evt.stageX < minX || evt.stageX > maxX) {
					return
				}
				evt.currentTarget.x = evt.stageX - minX;
			});
		};
			
		/* 点击开始游戏 */
		$(document).on("click", "#start", function() {
			console.log(6666)		
			var status0 = $("#status0").val()
			if (status0 == "0") { //没次数 弹出分享
				$(".popup11").show()
				$(".popup").show()
			} else { //有次数 打开游戏
				$(".div1").hide();
				$(".div2").show()
				carGame.init();
			}
		})
	};

//进入页面时判断是否有次数获取头像
	$.ajax({
		type: "POST",
		data: {
			openId: openId,
			baseId: 35,
			activityId: 49
		},
		url: ltapi + "/apiv1/other/checkForPlay",
		success: function(data) {
			if (data.status == 1) {
				$(".timg img").attr('src', data.content.headPic)
			} else {
				$(".timg img").attr('src', 'https://static.fangxiaoer.com/m/images/manC.png')
				$("#status0").val('0')
			}
		}
	})
	// 查询是否需要填写电话号
	$.ajax({
		type: "POST",
		data: {
			openId: openId
		},
		url: ltapi + "/apiv1/wechat/checkMemberByOpenId",
		success: function(data) { //成功不需要获取电话号
			console.log(data)
			if (data.status == 1) {
				$("#needMobile").val("1")
			}
		},
	})

	// 点击去抽奖
	var mobile = ""
	var code = ""
	$(document).on("click", ".toPopup4", function() {
		var need = $("#needMobile").val()
		if (need == "1") { //直接去抽奖
			mobile = ''
			code = ''
			chou("", "")
		} else { //弹出填写信息框填写后再去抽奖
			$(".popup>div").hide()
			$(".popup9").show()
			$(".popup").show()
		}

	})

	// 抽奖方法
	function chou(mobile, code) {
		$.ajax({
			type: "POST",
			data: {
				mobile: mobile,
				code: code,
				openId: openId,
				activityId: 49,
				baseId: 35
			},
			url: ltapi + "/apiv1/other/newDrawForGame",
			success: function(data) {
				console.log(data)
				if (data.status == 1) {
					if (data.content.displayid == '1') { //未中奖
						$(".popup>div").hide()
						$(".popup10").show()
						$(".popup").show()
					} else { //中奖
						$(".popup>div").hide()
						$(".popup4").show()
						$(".popup").show()
						$(".prizeDesc1").text((data.content.gift).split("|")[0])
						$(".prizeDesc2").text((data.content.gift).split("|")[1])
					}
				} else {
					layer.msg(data.msg)
				}
			}
		})
	}

	// 填写信息
	$(document).on("click", ".toPopup5", function() {
		var mobile2 = $("#phone").val();
		var code2 = $("#code").val();
		if (!mobile2) {
			layer.msg("请输入手机号")
		} else if (!code2) {
			layer.msg("请输入验证码")
		} else {
			chou(mobile2, code2)
		}
	})


	// 点击去分享
	$(document).on("click", ".shareBtnn", function() {
		$.ajax({
			type: "POST",
			data: {
				openId: openId
			},
			url: ltapi + "/apiv1/other/forShareTimes",
			success: function(data) {
				console.log(data)
				$(".popup>div").hide()
				$(".popup8").show()
				$(".popup").show()
			}
		})
	})

	// 点击规则
	$(document).on("click", ".index-btn-skill", function() {
		$(".popup>div").hide()
		$(".popup").show()
		$(".popup6").show()
	})

	// 首页点击我的奖品
	$(document).on("click", ".index-btn-rule", function() {
		$.ajax({
			type: "POST",
			data: {
				openId: openId,
				baseId: 35
			},
			url: ltapi + "/apiv1/other/viewPrizeDrawByHistory",
			success: function(data) {
				console.log(data)
				if (data.status == 1) {
					$(".popup>div").hide()
					$(".popup").show()
					$(".popup7").show()
					$(".popu7-add1").text((data.content.giftName).split("|")[0])
					$(".popu7-add2").text((data.content.giftName).split("|")[1])
					
				} else {
					$(".popup>div").hide()
					$(".popup").show()
					$(".popup3").show()
				}
			}
		})

	})
	
	
	// 点击关闭首页规则丨我的奖品
	$(document).on("click", ".pou-tou-k>s", function() {
		location.reload() 
	})

	
	
	// 点击关闭所有弹窗
	$(document).on("click", ".pou-tou-k>i", function() {
		location.reload() 
	})

	// 点击分享
	$(document).on("click", ".popup8", function() {
		location.reload()
	})

})
