.pageW {
	width: 9.18rem;
	margin: 0 auto;
	margin-bottom: 0.41rem;
	padding: 0.31rem;
	background: #fff;
	border-radius: 0.21rem;
}

.page {
	/*background: url(../img/bg.png) top center repeat-y;*/
	background-color: #9D0303;
	padding-bottom: 0.6rem;
	background-size: cover;
}

a {
	text-decoration: none;
}

img {
	width: 100%;
	height: 100%;
	object-fit: contain;
	/*margin-top: 0.1rem;*/
}

li {
	list-style: none
}

.cont-1 {
	margin-bottom: 0.82rem;
}

.page-title {
	margin-bottom: 0.31rem;
	font-size: 0.41rem;
	font-weight: bold;
	line-height: 0.42rem;
}

.img-swiper-4-big {
	height: 6.8rem;
	margin-bottom: 0.3rem;
}

.page .img-swiper-thumbnail-4small-swiper1 {
	overflow: hidden;
	position: relative;
	padding: 0.31rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
	background: #fff;
}

.img-swiper-4-big-p {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: bold;
	font-size: 0.36rem;
	color: #303030;
	line-height: 0.36rem;
	/*margin-bottom: 0.2rem;*/
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.img-swiper-4-small {
	height: 1.44rem;
}

.img-swiper-4-small .swiper-slide {
	height: 94%;
}

.swiper-slide-thumb-active {
	border: 2px solid #FF8924;
}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-prev {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-l.png) top center;
	background-size: 100% 100%;
	left: 20px;
	cursor: pointer;
	top: 56%;
}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-next {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-r.png) top center;
	background-size: 100% 100%;
	right: 20px;
	cursor: pointer;
	top: 56%;
}

.swiper-button-prev:after,
.swiper-button-next:after {
	content: '' !important
}


.leader-speak-video-list {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 0.31rem;
}

.leader-speak-item-describe {
	margin-top: 0.15rem;
	color: #303030;
	font-size: 0.31rem;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal;
}

.leader-speak-video-container {
	position: relative;
	border-radius: 4px;
	overflow: hidden;
	margin-bottom: 10px;
}

.leader-speak-item-img {
	display: block;
	width: 100%;
	height: 2.74rem;
	object-fit: cover;
}

.leader-speak-item-play {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 0.79rem;
	height: 0.79rem;
	margin: auto;
	cursor: pointer;
}

.module-8-page>li:nth-child(n+5) {
	display: none;
}

.canzhan-more-btn {
	height: 0.95rem;
	width: 2.85rem;
	margin: auto;
	margin-top: 10px;
	cursor: pointer;
}

.cont-4 {}

.cont-4 .cont-4-ul {}

.cont-4 .cont-4-li {}

.cont-4 li:first-child a {
	/* border-top:none; */
}

.cont-4 .cont-4-li a {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.36rem;
	color: #303030;
	line-height: 1rem;
	border-bottom: 0.03rem solid #EDEDED;
	display: block;
	text-overflow: ellipsis;
	white-space: pre;
	overflow: hidden;
}

.canzhan-more-padding {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.cont-6 {}

.cont-6-ul {
	overflow: hidden;
}

.cont-6-ul li:nth-child(5n) {
	margin: 0
}

.cont-6-li {
	float: left;
	width: 1.44rem;
	height: 0.77rem;
	background: #EBEBEB;
	border-radius: 0.05rem 0.05rem 0.05rem 0.05rem;
	text-align: center;
	line-height: 0.77rem;
	margin: 0 0.34rem 0.26rem 0;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.41rem;
	color: #555555;
	cursor: pointer;
}

.cont-6-li.hover {
	background: #FF8C07;
	color: #fff;
}

.cont-6-ul-item li:first-child {
	border-top: none;
}

.cont-6-item {
	padding: 0.26rem 0;
	border-top: 1px solid #ededed;
	width: 100%;
	margin: 0 auto;
	overflow: hidden;
}

.newhouse_left {
	position: relative;
	width: 2.87rem;
	height: 2.15rem;
	background: #434343;
	border-radius: 0.12rem;
	float: left;
	overflow: hidden;
}

.newhouse_right {
	margin-left: 0.38rem;
	width: 5.3rem;
	position: relative;
	top: 0.05rem;
	float: left;
	height: 2.15rem;
}

.newhouse_right a {
	position: absolute;
	left: 0;
	bottom: 0.02rem;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
}

.newhouse_title {
	font-weight: 700;
	color: #222222;
	line-height: 0.41rem;
	margin-bottom: 0.18rem;
	text-overflow: ellipsis;
	/* overflow: hidden; */
	white-space: nowrap;
	font-size: 0.41rem;
	position: relative;
}

.newhouse_price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: PingFang SC-Bold;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.newhouse_price .rise {
	color: #B0B0B0;
	font-size: 0.31rem;
	font-weight: normal;
	margin-left: 0.1rem;
}

.newhouse_unit {
	font-weight: normal;
	font-size: 0.31rem;
}

.cont-7-ul {
	overflow: hidden;
}

.cont-7-ul li {
	float: left;
	padding: 0.31rem 0.29rem;
	border-bottom: 1px solid #ededed;
	position: relative;
	height: 8.26rem;
	width: 50%;
}

.cont-7-ul li:nth-child(2n+1):before {
	content: '';
	position: absolute;
	right: 0;
	top: 0.6rem;
	bottom: auto;
	height: 84%;
	width: 1px;
	background-color: #ededed;
}

.cont-7-ul li .cover_pic {
	width: 3.97rem;
	height: 5.28rem;
	border-radius: 0.1rem 0.1rem 0.1rem 0.1rem;
	margin-bottom: 0.49rem;
}

.cont-7-title {
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 0.41rem;
	color: #303030;
	line-height: 0.43rem;
	margin-bottom: 0.25rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: pre;
	width: 100%;
}

.cont-7-price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: PingFang SC-Bold;
	margin-top: 0.21rem;
	line-height: 0.55rem;
}

.cont-7-price .cont-7-unit {
	font-weight: normal;
	font-size: 0.31rem;
}

.cont-7-a {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
	line-height: 0.31rem;
	margin-top: 0.2rem;
	display: block;
}

.cont-7 {
	padding: 0 !important;
	padding-bottom: 0.3rem !important;
}

.page-title-7 {
	padding: 0.31rem 0 0 0.31rem;
}

.cont-5 {
	padding: 0 !important;
}

.cont-4-ul li:nth-child(n+8) {
	display: none
}

.cont-7-ul li:nth-child(n+7) {
	display: none
}

.cont-6-ul-item li:nth-child(n+11) {
	display: none
}

.cont-6-p {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
	line-height: 0.35rem;
	margin-bottom: 0.32rem;
}

.video-view {
	position: fixed;
	left: 0;
	top: 0;
	background: rgb(0 0 0 / 100%);
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	display: none;
	justify-content: center;
	align-items: center;
}

#video-view {}

.top-back {
	width: 100%;
	height: 43px;
	background-color: #fff;
	color: #000;
	text-align: center;
	font-size: 15px;
	line-height: 43px;
	z-index: 99;
	position: fixed;
	top: 0;
}

.top-back .return {
	background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
	background-size: 100% 100%;
	;
	display: block;
	width: 20px;
	height: 20px;
	float: left;
	cursor: pointer;
}
.itemIconshow li{
    display: flex;
    align-items: center; /* 垂直居中 */
    margin-bottom: 0.11rem;
}
.itemIconshow li+li+li{
    display: none;
}
.itemIconshow li span{
   display: inline-block;
   font-family: PingFang SC, PingFang SC;
   font-size: 0.31rem;
   color: #FF5200;
}
.itemIconshow .itemIconTxt{
  
}
.itemIconshow li:nth-child(2n){
}
.itemIconshow .itemIcon{
    width: 0.51rem;
    height: 0.33rem;
    float: left;
    margin-right: 0.1rem;
    display: inline-block;
}
.itemIconshow .itemIcon1{
    background: url(../img/icon1.png)  top center;
    background-size: 100% 100%;
}
.itemIconshow .itemIcon2{
	background: url(../img/icon2.png)  top center;
	background-size: 100% 100%;
}
.itemIconshow .itemIcon3{
	background: url(../img/icon3.png)  top center;
	background-size: 100% 100%;
}
.itemIconshow .itemIcon4{
	background: url(../img/icon4.png)  top center;
	background-size: 100% 100%;
}
.itemIconTxt {
    font-family: PingFang SC, PingFang SC;
    font-weight: 650;
    font-size: 0.41rem;
    color: #FF5200;
    line-height: 0.33rem;
    text-align: left;
    font-style: normal;
    text-transform: none;
    display: inline-block;
}
.itemIconTxt1 {}
.itemIconTxt2 {}
.itemIconTxt3 {}
.itemIconTxt4 {}
.itemIconshow{}
.cont-7-title2{
    font-family: PingFang SC, PingFang SC;
    /* font-weight: bold; */
    font-size: 0.36rem;
    color: #303030;
    line-height: 0.34rem;
}
.cont-8{}
.cont-8 a,.cont-9 a{
    display: block;
    width: 9.18rem;
    margin: 0 auto;
    margin-bottom: 0.41rem;
    border-radius: 0.21rem;
}
.cont-8 img{
}
.cont-9{}
.cont-9 a{}
.cont-9 img{
}
/* 适配pc端 */
@media (min-width: 750px) {
	body{
    /*max-width: max-content;*/
}
	.pageW {
		width: 1024px;
		padding: 35px;
		margin-bottom: 48px;
	}

	.page .img-swiper-thumbnail-4small-swiper1 {
		padding: 35px
	}

	.page-title {
		font-family: Microsoft YaHei, Microsoft YaHei;
		font-weight: bold;
		font-size: 46px;
		color: #303030;
		line-height: 48px;
		text-align: left;
		margin-bottom: 35px;
	}

	.cont-1 {
		margin-bottom: 48px;
	}


	.img-swiper-4-big {
		height: 730px;
	}

	.img-swiper-4-big-p {
		font-size: 40px;
		line-height: 42px;
		margin-bottom: 21px;
	}

	.img-swiper-4-small {
		height: 162px;
	}

	.leader-speak-item-describe {
		margin-top: 17px;
		font-weight: 400;
		font-size: 35px;
		line-height: 45px;
	}

	.leader-speak-video-container {}

	.leader-speak-item-img {
		width: 469px;
		height: 312px;
		border-radius: 4px 4px 4px 4px;
	}

	.leader-speak-item-play {
		width: 90px;
		height: 90px;
	}

	.canzhan-more-btn {
		width: 308px;
		height: 105px;
		background: #FF8C07;
		border-radius: 4px 4px 4px 4px;
		margin-top: 35px;
	}

	.cont-4 .cont-4-li a {
		line-height: 114px;
		font-size: 40px;
	}

	.cont-6-li {
		width: 163px;
		height: 88px;
		border-radius: 2px 2px 2px 2px;
		font-weight: 400;
		font-size: 45px;
		line-height: 88px;
		margin: 0 34px 29px 0;
	}

	.cont-6-item {
		padding: 30px 0;
	}

	.newhouse_left {
		width: 326px;
		height: 244px;
		border-radius: 4px 4px 4px 4px;
	}

	.newhouse_right {
		margin-left: 35px;
		height: 244px;
		width: 550px;
	}

	.newhouse_right a {
		font-size: 35px;
	}

	.newhouse_title {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 45px;
		line-height: 48px;
		margin-bottom: 20px;
	}

	.newhouse_price {
		font-size: 34px;
		line-height: 36px;
	}

	.newhouse_price .rise {
		font-size: 24px;
	}

	.newhouse_unit {
		font-size: 34px;
	}

	.cont-7-ul li {
		padding: 30px;
		height: 900px;
	}

	.cont-7-ul li .cover_pic {
		width: 451px;
		height: 602px;
		border-radius: 4px 4px 4px 4px;
		margin-bottom: 53px;
	}

	.cont-7-title {
		font-size: 45px;
		margin-bottom: 28px;
		line-height: 45px;
	}

	.cont-7-price {
		font-size: 52px;
		line-height: 54px;
		margin-top: 25px;
	}

	.cont-7-price .cont-7-unit {
		font-size: 35px;
	}

	.cont-7-a {
		font-size: 35px;
		line-height: 37px;
		margin-top: 12px;
	}

	.cont-7 {
		padding-bottom: 35px !important;

	}

	.page-title-7 {
		padding: 35px;
		padding-bottom: 0;
		margin-bottom: 0;
	}

	.cont-6-p {
		font-size: 34px;
		line-height: 36px;
		margin-bottom: 35px;
	}


	.top-back {
		width: 100%;
		height: 43px;
		color: #000;/
		text-align: center;
		font-size: 15px;
		line-height: 43px;
		z-index: 99;
		position: fixed;
		top: 0;
		background: none;
	}

	.top-back .return {
		background: url(https://static.fangxiaoer.com/web/images/ico/sign/kpTc_07.png) top center;
		background-size: 100% 100%;
		display: block;
		width: 50px;
		height: 50px;
		float: right;
		cursor: pointer;
		position: fixed;
		right: 40px;
		top: 40px;
	}

	.itemIconshow li{
	}
	.itemIconshow li+li+li{
	    display: none;
	}
	.itemIconshow li span{
	font-size: 33px;
	}
	.itemIconshow .itemIconTxt{
	  
	}
	.itemIconshow li:nth-child(2n){
	}
	.itemIconshow .itemIcon{
	width: 53px;
	height: 33px;
	margin-right: 10px;
	}
	
	.itemIconTxt {
	font-size: 48px;
	line-height: 48px;
	}
	.cont-7-title2{
    font-size: 36px;
    line-height: 38px;
	}
	.cont-8{}
	.cont-8 a,.cont-9 a{
    width: 1024px;
    margin-bottom: 48px;
}
	.cont-8 img{}
	.cont-9{}
	.cont-9 a{}
	.cont-9 img{}
}
.cover_pic{
	object-fit: unset !important;
}