
a { color: #000; text-decoration: none; }
i,em,b{ font-style: normal; }
*{ box-sizing: border-box; margin: 0; padding: 0;}
/*----------*/

body {
    background: #F7EEDF;
    font-family: "PingFang SC", "Microsoft YaHei", <PERSON><PERSON>, sans-serif;
    margin: 0;
    padding: 0;
}
.header {
    text-align: center;
    padding: 0.85rem 0 0 0;
    color: #7a3a1d;
    font-size: 0.96rem;
    font-weight: bold;
    letter-spacing: 0.053rem;
}
.trophy-section {
    text-align: center;
    margin-top: 0.21rem;
    margin-bottom: 0.64rem;
}
.trophy-img {
    width: 4.27rem;
    height: auto;
}
.tab-bar {
    width: 5.87rem;
    height: 1.07rem;
    margin: auto auto 0.5rem auto;
    background-image: url('../img/<EMAIL>'); background-size: 100%; background-repeat: no-repeat; background-position: center;

    display: flex;
    justify-content: space-between;
    gap: 0;
    align-items: center;
}

.tab-bar.tab-bar-active {
    background-image: url('../img/<EMAIL>');
}
.tab-btn {
    width: 2.9rem;
    height: 1.07rem;
    font-size: 0.53rem;
    border: none;
    border-radius: 0.64rem 0 0 0.64rem;
    color: #7a3a1d;
    font-weight: bold;
    cursor: pointer;
    outline: none;
    margin-right: -0.027rem;
    transition: background 0.2s;
}
.tab-btn.tab-btn-active {
    color: #fff !important;
}
.card {
    background: #fff;
    border-radius: 0.2rem;
    margin: 0 auto 0.64rem auto;
    width: 90%;
    max-width: 11.2rem;
    box-shadow: 0 0.053rem 0.32rem rgba(0,0,0,0.04);
    padding-bottom: 0.13rem;
}
.card2 {
    margin: 0 auto 0.64rem auto;
    width: 90%;
    max-width: 11.2rem;
    padding-bottom: 0.43rem;
}
.card-img-wrapper {
    position: relative;
    border-radius: 0.2rem 0.2rem 0 0;
    overflow: hidden;
}
.swiper, .swiper-wrapper, .swiper-slide {
    width: 100%;
    height: 5.87rem;
    border-radius: 0.2rem 0.2rem 0 0;
}
.card-img {
    width: 100%;
    height: 5.87rem;
    object-fit: cover;
    border-radius: 0.2rem 0.2rem 0 0;
}
.img-index {
    position: absolute;
    right: 0.43rem;
    bottom: 0.43rem;
    background: rgba(0,0,0,0.5);
    color: #fff;
    border-radius: 0.32rem;
    padding: 0.053rem 0.27rem;
    font-size: 0.37rem;
    z-index: 2;
}
.card-content {
    padding: 0.43rem;
}
.card-title-row {
    display: flex;
    align-items: flex-start;
    margin-bottom: 0.21rem;
}
.card-title {
    font-size: 0.53rem;
    font-weight: bold;
    color: #333;
    margin-right: 0.32rem;
}
.card-area {
    font-size: 0.37rem;
    color: #fff;
    border-radius: 0.09rem;
    padding: 0.053rem 0.2rem;
    color: #666666;
    border: 1px solid #CCCCCC;
}
.card-desc {
    font-size: 0.4rem;
    color: #666;
    margin-bottom: 0.43rem;
    line-height: 1.5;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
}
.card-footer {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.like {
    display: flex;
    align-items: center;
    color: #888;
    font-size: 0.43rem;
}
.like-icon {
    width: 0.4rem;
    margin-right: 0.27rem;
}
.vote-btn {
    background: #ff6b3b;
    color: #fff;
    border: none;
    border-radius: 0.53rem;
    width: 1.6rem;
    line-height: 0.64rem;
    text-align: center;
    font-size: 0.37rem;
    font-weight: bold;
    cursor: pointer;
    transition: background 0.2s;
}
.vote-btn:hover {
    background: #ff8c5a;
}
.tab-content { width: 100%; }
.tab-panel {     width: 100%;}
.rank-list {
    display: flex;
    flex-direction: column;
    gap: 0.15rem;
}
.rank-item {
    display: flex;
    align-items: center;
    background: #fff;
    border-radius: 0.6em;
    box-shadow: 0 2px 8px rgba(0,0,0,0.04);
    padding: 0.7em 1.1em 0.7em 1.1em;
    font-size: 1.08em;
    font-weight: 500;
    color: #333;
    min-height: 2.7em;
    margin: 0 0.1em;
    border: none;
    position: relative;
}
.rank-item:not(:last-child) {
    margin-bottom: 0.65em;
}
.rank-icon {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 2.2em;
    height: 2.2em;
    margin-right: 0.7em;
    position: relative;
}

.rank-num {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    font-size: 1.1em;
    font-weight: bold;
    color: #C8A59C;
    z-index: 1;
    text-shadow: 0 1px 4px rgba(0,0,0,0.18);
}
.rank-item.rank-1 .rank-num {
    color: #fff;
    text-shadow: 0 1px 4px #fffbe6;
}
.rank-item.rank-2 .rank-num {
    color: #fff;
    text-shadow: 0 1px 4px #f7f7f7;
}
.rank-item.rank-3 .rank-num {
    color: #fff;
    text-shadow: 0 1px 4px #fff3e0;
}
.rank-title {
    flex: 1;
    font-size: 1.08em;
    font-weight: 500;
    color: #333;
    margin-right: 0.5em;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.rank-vote {
    font-size: 1.08em;
    font-weight: 400;
    color: #888;
    margin-right: 1.1em;
    display: flex;
    align-items: baseline;
}
.rank-vote-num {
    font-size: 1.25em;
    font-weight: bold;
    color: #333;
    margin-right: 0.1em;
}
.rank-vote-btn {
    background: linear-gradient(90deg, #ffb86c 0%, #ff6b3b 100%);
    color: #fff;
    border: none;
    border-radius: 1.2em;
    padding: 0.4em 1.3em;
    font-size: 1.08em;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(255,85,0,0.08);
    transition: background 0.2s, box-shadow 0.2s;
    letter-spacing: 0.08em;
}
.rank-vote-btn:active {
    background: linear-gradient(90deg, #ff6b3b 0%, #ffb86c 100%);
    box-shadow: 0 1px 4px rgba(255,85,0,0.12);
}
#voteMask {
    position: fixed;
    left: 0; top: 0; right: 0; bottom: 0;
    background: rgba(0,0,0,0.5);
    z-index: 1000;
}
#voteDialog {
    position: fixed;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background: #fff;
    border-radius: 1.2em;
    box-shadow: 0 8px 32px rgba(0,0,0,0.13);
    width: 85vw;
    padding: 2.2em 1.2em 1.5em 1.2em;
    z-index: 1001;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}
.vote-dialog-icon {
    margin-bottom: 1.1em;
}
.vote-dialog-title {
    font-size: 1.25em;
    font-weight: bold;
    color: #8c3a1d;
    margin-bottom: 0.5em;
}
.vote-dialog-sub {
    font-size: 1em;
    color: #888;
    margin-bottom: 1.3em;
}
.vote-dialog-btn {
    background: linear-gradient(90deg, #ffb86c 0%, #ff6b3b 100%);
    color: #fff;
    border: none;
    border-radius: 1.2em;
    padding: 0.7em 2.2em;
    font-size: 1.1em;
    font-weight: bold;
    cursor: pointer;
    box-shadow: 0 2px 8px rgba(255,85,0,0.08);
    transition: background 0.2s, box-shadow 0.2s;
    letter-spacing: 0.08em;
}
.vote-dialog-btn:active {
    background: linear-gradient(90deg, #ff6b3b 0%, #ffb86c 100%);
    box-shadow: 0 1px 4px rgba(255,85,0,0.12);
}
/*------*/

.mtop{
    width: 10rem;
    height: 6.93rem;
}
.mtop img{
    width: 100%;
    height: 100%;
}


#voteLimitDialog{
    display:none;position:fixed;left:0;top:0;width:100vw;height:100vh;z-index:2000;
}
.vote-limit-dialog-title{
    position:absolute;left:50%;top:30%;transform:translate(-50%,0);background:rgba(0,0,0,0.7);color:#fff;font-size:0.43rem;padding:0.43rem 0.2rem;border-radius:0.32rem;text-align:center;box-shadow:0 2px 16px rgba(0,0,0,0.08);min-width:6rem;
}



