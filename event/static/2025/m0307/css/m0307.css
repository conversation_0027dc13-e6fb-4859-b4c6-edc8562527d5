* {
	margin: 0;
	padding: 0;
}

body {
    url(
    ../i m);
    background: #860909;
}

img {
	width: 100%;
	height: 100%;
    object-fit: contain;
}

.page {
    background: #860909;
}

li {
	list-style: none;
}

.page-title {
	margin-bottom: 0.31rem;
	font-size: 0.41rem;
	font-weight: bold;
	line-height: 0.42rem;
}
.page-title-plate3{
	position: absolute;
	width: 100%;
	text-align: center;
	left: 0;
	top: 0.5rem;
}

.more-btn{
    width: 2.72rem;
    height: 0.92rem;
    background: #FF8C07;
    border-radius: 0.1rem 0.1rem 0.1rem 0.1rem;
    display: block;
    margin: 0 auto;
    text-align: center;
    line-height: 0.92rem;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: bold;
    font-size: 0.31rem;
    color: #FFFFFF;
    margin-top: 0.4rem;
    text-decoration: none;
}
.morebtn-18,.morebtn-10{
    display: none;
}
.cont {
	width: 9.18rem;
	margin: 0 auto;
	margin-bottom: 0.62rem;
}
.cont5{
	 
}
.cont1{
    margin: 0;
}
.cont22{}
.cont22 a{
    display: block;
    width: 1.77rem;
    height: 1.66rem;
    position: absolute;
    right: 0;
    top: 10.2rem;
    z-index: 9999;
}
.cont22 img{}
.cont2{}
.cont3{
    background: hsl(0deg 0% 100% / 80%);
    border-radius: 0.21rem 0.21rem 0.21rem 0.21rem;
    padding: 0.31rem;
    width: 8.58rem;
    position: relative;
}
.cont4{
    height: auto;
    background-color: white;
    border-radius: 0.1rem;
    padding: 0.31rem;
    width: 8.58rem;
    overflow: hidden;
}
.cont5>a{
	    cursor: pointer;
	    text-decoration: none;
	    display: block;
	    overflow: hidden;
}
.cont6{}
.cont7{
    width: 8.56rem;
    background: linear-gradient( 134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
    border-radius: 0.21rem 0.21rem 0.21rem 0.21rem;
    padding: 0.31rem;
}
.cont7-img-1{
    height: 3.1rem;
    margin-bottom: 0.27rem;
}
.cont7-img-1 img{}
.cont7-img-2{
    overflow: hidden;
}
.cont7-img-2 div{
    float: left;
    overflow: hidden;
    width: 4.16rem;
}
.cont7-img-2 div+div{
    margin-left: 0.21rem;
}
.cont7-img-2 div img{
    width: 4.28rem;
    height: 3.32rem;
    border-radius: 0.11rem 0.11rem 0.11rem 0.11rem;
    margin-bottom: 0.16rem;
    overflow: hidden;
}
.cont7-img-2 div p{
    width: 4.14rem;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 0.33rem;
    color: #303030;
}
.cont8{
    overflow: hidden;
    position: relative;
    padding: 0.31rem;
    width: 8.58rem;
    border-radius: 0.21rem;
    margin-bottom: 0.41rem;
    background: #fff;
}
.cont9{
    overflow: hidden;
    position: relative;
    padding: 0.31rem;
    width: 8.58rem;
    border-radius: 0.21rem;
    margin-bottom: 0.41rem;
    background: #fff;
}
.cont10,.cont11,.cont12,.cont13{
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
    padding: 0.31rem;
    width: 8.58rem;
    border-radius: 0.21rem;
    margin-bottom: 0.41rem;
    overflow: hidden;
}
.cont10{
}
.cont11{
}
.cont20{
}
.cont21{
}
.cont12Ul{}
.cont12Ul  li{
    margin-bottom: 0.27rem;
}
.cont13{}
.cont17,.cont18,.cont19{
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
    padding: 0.31rem;
    width: 8.58rem;
    border-radius: 0.21rem;
    margin-bottom: 0.41rem;
    overflow: hidden;
}
.cont23{
	background: #fff;
	padding: 0.31rem;
	width: 8.58rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
	overflow: hidden;
}
.cont23Ul{}
.cont23Ul li:nth-child(2n+1) span{
    background: #C17223;
}
.cont23Ul li:nth-child(2n) span{
    background: #00A05C;
}
.cont23Ul li:nth-child(3n+3) span{
    background: #3A5AAF;
}
.cont23Li{
    background: #F5F5F5;
    border-radius: 0.1rem;
    margin: 0 auto;
    margin-bottom: 0.26rem;
    padding-bottom: 0.31rem;
}
.cont23Li span{
    width: 2.26rem;
    height: 0.9rem;
    display: block;
    text-align: center;
    line-height: 0.9rem;
    color: #fff;
    font-size: 0.36rem;
    margin-bottom: 0.26rem;
}
.cont23Li p{
    margin: 0 auto;
    width: 90%;
    margin-bottom: 0.26rem;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 0.31rem;
    color: #303030;
}
.cont23Li a{
    display: block;
    font-size: 0.32rem;
    margin: 0 auto;
    text-align: center;
    color: #006cff;
}
.cont3-span{
    float: right;
    margin-top: 0.3rem;
    color: #ff5200;
}
.page-title3{
    width: 5.49rem;
    margin: 0 auto;
}
.cont-main {
	overflow: hidden;
	position: relative;
	width: 8.82rem;
}

.cont-main1 {
	width: 8.2rem;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
}

.cont-main2 {}
.cont-main2 li:first-child{
	border-top: none !important;!i;!;
}
.cont-box {
	background: #fff;
	width: 96%;
	margin: 0 auto;
	padding: 0.21rem;
}

.cont-main li {}

.cont-main li.li1 {
	position: relative;
	width: 3.92rem;
	margin-bottom: 0.31rem;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-evenly;
}

.cont-main li.li2 {
	border-top: 1px solid #EDEDED;
	line-height: 1.03rem;
	overflow: hidden;
}

.cont-main li a {
	text-decoration: none;
	color: #333;
	cursor: pointer;
}
.imgBox{
	display: flex;
	justify-content: center; /* æ°´å¹³å±…ä¸­ */
	align-items: center; /* åž‚ç›´å±…ä¸­ */
	height: 100vh; /
;
	width: 3.92rem;
	height: 2.95rem;
	border-radius: 0.1rem;
	margin-bottom: 0.21rem;
	max-width: 100%; /* ä¿æŒå›¾ç‰‡å®½é«˜æ¯” */
}
.cont-main li img {

  height: auto;
}

.cont-main li p {
       display: -webkit-box;
       -webkit-box-orient: vertical;
       -webkit-line-clamp: 1;
       overflow: hidden;
       text-overflow: ellipsis;
       word-break: break-all;
       width: 100%;
}



.cont-main li .p2 {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	width: 90%;
	float: left;
}

.cont-video {position: absolute;left: 40%;top: 30%;width: 0.79rem;height: 0.79rem;margin: auto;background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/play.png) top center;background-size: 100% 100%;}

.cont-icon-r {
	display: inline-block;
	width: 0.11rem;
	height: 0.21rem;
	background: url(https://static.fangxiaoer.com/m/static/images/projectDetailNew/detail29.png) top center no-repeat;
	background-size: 100% 100%;
	margin-right: 0.12rem;
	float: right;
	margin-top: 0.45rem;
}
#marquee-box{
    overflow: hidden;
    margin: 0 auto;
}
#marquee-con{
    width: 500%;
    height: 4.31rem;
    float: left;
    overflow: hidden;
}
#marquee,#marquee-1{
    float:
    left;
    height: 4.31rem;
    width: 500%;
}
.marquee-img{
    float:
    left;
    margin-left: 0.31rem;
    width: 5.75rem;
    height: 4.31rem;
    display: inline-block;
}
.marqueek{
	padding: 0.27rem 0.16rem;
	/* background: linear-gradient(0deg, #FFFFFF 0%, #E4F8FF 100%); */
	width: 8.99rem;
	margin: 0 auto;
	margin-top: -0.2rem;
	height: 4.85rem;
}

/* ÃƒÂ¥Ã‚Â¼Ã‚Â¹ÃƒÂ¥Ã‚Â¹Ã¢â‚¬Â¢  start*/
 .bulletChats{
    /* position: absolute; */
    top: 3.76rem;
    overflow: hidden;
    position: absolute;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    left: 0;
    top: 9.2rem;
    z-index: 9;
    width: 100%;
    font-size: 12px;
    z-index: 999;
 }
 .bulletChats marquee>span>span{
     height: 0.8rem;
     padding-left: 0.5rem;
     padding-right: 0.5rem;
     width: auto;
     margin-bottom: 0.2rem;
     display: inline-block;
     margin-right: 0.3rem;
     border-radius: 1.28rem 1.28rem 1.28rem 1.28rem;
     color: #fff;
     background: hsl(0deg 0% 0% / 60%);
 }
 .bulletChats marquee>span.hover span{
	 background: linear-gradient( 180deg, #851C22 0%, #A93C23 30%);
 }
 .bulletChats span img{
     width: 0.67rem;
     height: 0.67rem;
     border-radius: 50%;
     float: left;
     margin-top: 0.145rem;
     margin-left: 0.12rem;
     margin-right: 0.28rem;
 }
 .bulletChats span span{
     height: 0.8rem;
     line-height: 0.8rem;
     text-align: left;
     font-family: Alibaba PuHuiTi;
     font-weight: 500;
     font-size: 0.32rem;
     color: #fff;
 }
 .block{
    position:absolute;
 }
 
 
 .bulletChats .marquee1{
  	margin-left: -1rem;
  }
  .bulletChats .marquee2{
  	margin-left: -0.6rem;
  	margin-top: 0.2rem;
  }
  .bulletChats .marquee3{
  	margin-left: -0.1rem;
  	margin-top: 0;
  }
  .bulletChats .marquee4{
  	margin-left: 1rem;
  	margin-top: 0.2rem;
  }
  .bulletChats .marquee5{
  	margin-left: -0.91rem;
  	margin-top: 0.2rem;
  }
  .bulletChats .marquee2>span:nth-child(2n){
     
      margin-left: 0.5rem;
  }
  .bulletChats .marquee3>span:nth-child(2n){
  
      margin-left: 0.1rem;
  }
  .bulletChats .marquee2>span{margin-right: 15px;margin-left: 60px;}
  .bulletChats .marquee2 div+div{
  	margin-left: 0;
  }
/* ÃƒÂ¥Ã‚Â¼Ã‚Â¹ÃƒÂ¥Ã‚Â¹Ã¢â‚¬Â¢ end */
/* ÃƒÂ§Ã†â€™Ã‚Â­ÃƒÂ¥Ã‚ÂºÃ‚Â¦ÃƒÂ¦Ã‚ÂµÃ‚ÂÃƒÂ¨Ã‚Â§Ã‹â€ ÃƒÂ©Ã¢â‚¬Â¡Ã‚Â+ÃƒÂ¦Ã¢â‚¬â€Ã‚Â¶ÃƒÂ©Ã¢â‚¬â€Ã‚Â´ÃƒÂ¦Ã…Â½Ã‚Â§ÃƒÂ¤Ã‚Â»Ã‚Â¶ start*/
.msg-time-pageview {
    width: 8.58rem;
}
.msg-time-pageview {
	background: url(../img/new/5.png) top center;
	background-size: 100% 100%;
	padding: 0.31rem;
	border-radius: 0.21rem;
	z-index: 99;
	position: relative;
	margin-top: 0.4rem;
}

.msg-time-pageview-cont1 {
	overflow: hidden;
	border-bottom: 0.03rem solid #fff;
	padding-bottom: 0.26rem;
	margin-bottom: 0.26rem;
}

.msg-time-pageview-title {
	background: url(../img/heat2.png) top center;
	background-size: 100% 100%;
	width: 2.72rem;
	height: 0.74rem;
	float: left;
}

.msg-time-pageview-time {
	float: right;
	margin-top: 0.18rem;
	font-size: 0.31rem;
}

.msg-time-pageview-cont2 {
	display: flex;
	/* flex-wrap: wrap; */
	/* justify-content: space-evenly; */
	width: 100%;
}

.msg-time-pageview-cont2-2 {
	text-align: left;
	width: 2.9rem;
	height: 1.28rem;
	margin: 0 0.11rem;
	background: url(../img/new/2.png) top center;
	background-size: 100% 100%;
	padding-left: 0.44rem;
	padding-top: 0.26rem;
}
.msg1{
    background: url(../img/new/2.png) top center;
    background-size: 100% 100%;
}
.msg2{
    background: url(../img/new/3.png) top center;
    background-size: 100% 100%;
}
.msg3{
    background: url(../img/new/4.png) top center;
    background-size: 100% 100%;
}

.msg-time-pageview-text {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.31rem;
	line-height: 0.31rem;
	margin-bottom: 0.2rem;
	color: #882A00;
}

.msg-time-pageview-num {
	height: 0.41rem;
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: bold;
	font-size: 0.41rem;
	line-height: 0.41rem;
	color: #722300;
}
/* ÃƒÂ§Ã†â€™Ã‚Â­ÃƒÂ¥Ã‚ÂºÃ‚Â¦ÃƒÂ¦Ã‚ÂµÃ‚ÂÃƒÂ¨Ã‚Â§Ã‹â€ ÃƒÂ©Ã¢â‚¬Â¡Ã‚Â+ÃƒÂ¦Ã¢â‚¬â€Ã‚Â¶ÃƒÂ©Ã¢â‚¬â€Ã‚Â´ÃƒÂ¦Ã…Â½Ã‚Â§ÃƒÂ¤Ã‚Â»Ã‚Â¶  end*/

/* ÃƒÂ¦Ã‚ÂµÃ‚ÂÃƒÂ¨Ã‚Â§Ã‹â€ ÃƒÂ©Ã¢â‚¬Â¡Ã‚Â start*/
#container{
	height: 5.5rem !important;
	margin-top: -0.3rem;
}
#container>div {/* height: 5.18rem !important; */}
#container canvas{
}
/* ÃƒÂ¦Ã‚ÂµÃ‚ÂÃƒÂ¨Ã‚Â§Ã‹â€ ÃƒÂ©Ã¢â‚¬Â¡Ã‚Â end*/

/* ÃƒÂ¨Ã‚Â§Ã¢â‚¬Â ÃƒÂ©Ã‚Â¢Ã¢â‚¬ËœÃƒÂ¦Ã¢â‚¬â„¢Ã‚Â­ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¾  start*/
.cont4-video{position: relative;height: 6.13rem;}
.cont4-video i{
    display: block;
    width: 0.85rem;
    height: 0.85rem;
    background: url(../img/play.png) top center;
    position: absolute;
    left: 50%;
    top: 50%;
    z-index: 99;
    background-size: 100%;
    margin-left: -0.425rem;
    margin-top: -0.425rem;
}
.cont4-video img{}

/* ÃƒÂ¨Ã‚Â§Ã¢â‚¬Â ÃƒÂ©Ã‚Â¢Ã¢â‚¬ËœÃƒÂ¦Ã¢â‚¬â„¢Ã‚Â­ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¾  end*/


/* ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¿ÃƒÂ§Ã‚Â­Ã¢â‚¬â€œ start */
.image-full {
	flex-grow: 1;
	width: 100%;
	height: 100%;
}

.module-bgContainer-list1 {
	display: flex;
	height: auto;
	flex-direction: row;
	background-color: white;
	border-radius: 0.21rem;
	padding: 0.31rem;
	width: 8.58rem;
	cursor: pointer;
}

.list-item-container1 {
	margin-top: 24px;
	margin-bottom: 24px;
	margin-left: 24px;
	display: flex;
	height: auto;
	background: #FFFFFF;
	width: auto;
}

.list-item-image1 {
	width: 3.59rem;
	height: 2.69rem;
	overflow: hidden;
	border-radius: 4px;
	float: left;
}

.list-item-right1 {
	/* margin-top: 5px; */
	flex-direction: column;
	margin-left: 0.31rem;
	float: left;
	width: 4.6rem;
}

.list-item-right-title1 {
	font-weight: bold;
	font-size: 0.41rem;
	color: #303030;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.list-item-right-subtitle1 {
	margin-top: 12px;
	font-weight: 500;
	font-size: 0.31rem;
	color: #303030;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	width: 95%;
}

/* ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¿ÃƒÂ§Ã‚Â­Ã¢â‚¬â€œ end */


/* ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ÃƒÂ§Ã¢â‚¬Â°Ã¢â‚¬Â¡ÃƒÂ¥Ã‹â€ Ã¢â‚¬Â¡ÃƒÂ¦Ã‚ÂÃ‚Â¢ÃƒÂ¦Ã…Â½Ã‚Â§ÃƒÂ¤Ã‚Â»Ã‚Â¶--1ÃƒÂ¥Ã‚Â¤Ã‚Â§ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾+4ÃƒÂ§Ã‚Â¼Ã‚Â©ÃƒÂ§Ã¢â‚¬Â¢Ã‚Â¥ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ start*/
.img-swiper-thumbnail-4small-swiper1 {
	overflow: hidden;
	position: relative;
	padding: 0.31rem;
	width: 8.58rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
	background: #fff;
}

.img-swiper-4-big {
	height: 6.8rem;
	margin-bottom: 0.3rem;
}

.img-swiper-4-small {
	height: 1.44rem;
}

.img-swiper-4-small .swiper-slide {
	height: 94%;
}

.swiper-button-next:after,
.swiper-button-prev:after {
	content: '' !important;
}

.img-swiper-thumbnail-4small-swiper1 .swiper-wrapper {
	margin-bottom: 0.13rem;
}

.img-swiper-thumbnail-4small-swiper2 {}

.img-swiper-thumbnail-4small-swiper2 .swiper-wrapper {
	/* width: 102%; */
	overflow: hidden;
}

.img-swiper-thumbnail-4small-swiper2 .swiper-wrapper .swiper-slide {
	height: 1.9rem;
	width: 25%;
}

.img-swiper-thumbnail-4small-swiper2 .swiper-wrapper .swiper-slide img {}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-next {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-r.png) top center;
	background-size: 100% 100%;
	right: 20px;
	cursor: pointer;
	top: 56%;
}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-prev {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-l.png) top center;
	background-size: 100% 100%;
	left: 20px;
	cursor: pointer;
	top: 56%;
}

.img-swiper-4-big-p {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: bold;
	font-size: 0.36rem;
	color: #303030;
	line-height: 0.36rem;
	/*margin-bottom: 0.2rem;*/
}

.img-swiper-thumbnail-4small .mySwiper {
	width: 100%;
	height: 1.9rem;
	margin-left: auto;
	margin-right: auto;
}

.img-swiper-thumbnail-4small .img-swiper-thumbnail-4small-swiper1 {
	height: 80%;
	width: 100%;
}

.img-swiper-thumbnail-4small .mySwiper .swiper-slide {
	width: 25%;
	height: 100%;
}

.img-swiper-thumbnail-4small .mySwiper .swiper-slide-thumb-active {
	opacity: 1;
}

.img-swiper-thumbnail-4small .swiper-slide img {
	display: block;
	width: 100%;
	height: 100%;
	object-fit: cover;
}
.swiper-slide-thumb-active {
	border: 2px solid #ff5200;
}
/* ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ÃƒÂ§Ã¢â‚¬Â°Ã¢â‚¬Â¡ÃƒÂ¥Ã‹â€ Ã¢â‚¬Â¡ÃƒÂ¦Ã‚ÂÃ‚Â¢ÃƒÂ¦Ã…Â½Ã‚Â§ÃƒÂ¤Ã‚Â»Ã‚Â¶--1ÃƒÂ¥Ã‚Â¤Ã‚Â§ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾+4ÃƒÂ§Ã‚Â¼Ã‚Â©ÃƒÂ§Ã¢â‚¬Â¢Ã‚Â¥ÃƒÂ¥Ã¢â‚¬ÂºÃ‚Â¾ end*/


/* cont-14 start*/
.cont14 a{
    display: block;
    width: 100%;
    height: 100%;
}
.cont14{
    width: 8.86rem;
    background: linear-gradient( 134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
    border-radius: 0.21rem 0.21rem 0.21rem 0.21rem;
    padding: 0.31rem;
    display: flex;
    justify-content: space-between;
}
.cont14-l {
    width: 3.7rem;
    height: 5.18rem;
}
.cont14-r{
    width: 4.36rem;
}
.cont14-r-top{display: flex;justify-content: space-between;margin-bottom: 0.33rem;}
.cont14-r-top a {
    width: 2.13rem;
    height: 1.59rem;
}
.cont14-r-bottom {
    width: 4.36rem;
    height: 3.26rem;
}
/* cont-14 end*/

.cont15{
	width: 8.56rem;
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
	border-radius: 0.21rem 0.21rem 0.21rem 0.21rem;
	padding: 0.31rem;
	/* display: flex; */
	/* justify-content: space-between; */
}
.cont15Ul {
    overflow: hidden;
}
.cont15Ul  li{
    width: 3.85rem;
    height: 1.64rem;
    float: left;
}
.cont15Ul  li+li{margin-left: 0.22rem;}
.cont15Ul  li a{
    display: block;
    width: 100%;
    height: 100%;

}
/* cont-8 :start */
.leader-speak-video-list {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 0.31rem;
}

.leader-speak-item-describe {
	margin-top: 0.15rem;
	color: #303030;
	font-size: 0.31rem;
	overflow: hidden;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	text-overflow: ellipsis;
	white-space: normal;
}

.leader-speak-video-container {
	position: relative;
	border-radius: 4px;
	overflow: hidden;
	margin-bottom: 10px;
}

.leader-speak-item-img {
	display: block;
	width: 100%;
	height: 2.74rem;
	object-fit: cover;
}

.leader-speak-item-play {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 0.79rem;
	height: 0.79rem;
	margin: auto;
}

/* ÃƒÂ©Ã‚Â¢Ã¢â‚¬Â ÃƒÂ¥Ã‚Â¯Ã‚Â¼ÃƒÂ¨Ã‚Â®Ã‚Â²ÃƒÂ¨Ã‚Â¯Ã‚Â  end*/

/* ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¿ÃƒÂ§Ã‚Â­Ã¢â‚¬â€œÃƒÂ¥Ã¢â‚¬â„¢Ã‚Â¨ÃƒÂ¨Ã‚Â¯Ã‚Â¢  start*/
.policy-item-box {
	position: relative;
}

.policy-item-img {
	display: block;
	width: 100%;
}

.policy-action-view {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 1rem;
	display: flex;
}

.policy-action-view-call {
	width: 50%;
	position: relative;
}
.policy-action-view-call:hover .showTel {display: block;}
.showTel {
	display: none;
	font-size: 0.3rem;
	position: absolute;
	left: 20%;
	top: 100%;
	background-color: #333;
	color: #fff;
	padding: 8px;
	white-space: nowrap;
}

.policy-action-view-preview {
	width: 50%;
}

.full-corver {
	display: block;
	width: 100%;
	height: 100%;
}
/* cont-8 :end */


/* cont-9 start*/
.module-6-container {
	display: flex;
	flex-direction: column;
	background-color: white;
	height: auto;
	border-radius: 0.21rem;
	width: 8.58rem;
	padding: 0.31rem;
}

.module-gradient {
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
}

.module-6-content {
	border-radius: 0.21rem;
	background-color: white;
	height: auto;
	padding-bottom: 0.1rem;
}

.module-6-grid-content {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.31rem;
	height: auto;
}

.module-6-page>div:nth-child(n+7) {
	display: none;
}
.module-6-morebtn {}

.module-8-page>li:nth-child(n+7) {
	display: none;
}
.module-8-morebtn {}

.module-6-grid-item {
	display: flex;
	flex-direction: column;
	align-items: baseline;
	justify-content: space-evenly;
	height: auto;
	overflow: hidden;
}

.xc-container-item-img {
	width: 100%;
	max-height: 3.1rem;
	overflow: hidden;
	border-radius: 0.1rem;
	flex-shrink: 1;
	object-fit: cover;
}

.xc-container-item-title {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	font-weight: 400;
	font-size: 0.31rem;
	font-family: Microsoft YaHei, Microsoft YaHei;
	color: #303030;
	margin-top: 0.1rem;
	flex: 1;
}
/* cont-9 end */


/* cont-10 start */
.new-house-swiper,
.second-house,.cont16,.cont20{
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
	padding: 0.31rem;
	width: 8.58rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
	overflow: hidden;
}
/* cont-10 end */

/* TabÃƒÂ¦Ã…Â½Ã‚Â§ÃƒÂ¤Ã‚Â»Ã‚Â¶ÃƒÂ¯Ã‚Â¼Ã‹â€ ÃƒÂ¦Ã¢â‚¬â€œÃ‚Â°ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‚Â¥Ã‚Â¼ÃƒÂ§Ã¢â‚¬ÂºÃ‹Å“ÃƒÂ¥Ã‹â€ Ã¢â‚¬â€ÃƒÂ¨Ã‚Â¡Ã‚Â¨+tabÃƒÂ¯Ã‚Â¼Ã¢â‚¬Â° start*/
.new-house-swiper {
	width: 100%;
	margin: 0 auto;
}

.new-house-swiper-list {}

.new-house-swiper-list li {
	padding: 0.26rem 0;
	border: hidden;
	border-bottom: 1px solid #ededed;
	width: 96%;
	margin: 0 auto;
}

.new-house-swiper-list li:last-child {
	border-bottom: none
}

.new-house-swiper-list li .newhouse_list {
	width: 100%;
	overflow: hidden;
	text-decoration: none;
	display: block;
}

.new-house-swiper-list li .newhouse_list .newhouse_left {
	position: relative;
	width: 2.87rem;
	height: 2.15rem;
	background: #434343;
	border-radius: 0.05rem;
	float: left;
}

.new-house-swiper-list li .newhouse_list .newhouse_right {
	margin-left: 0.38rem;
	width: 5.3rem;
	position: relative;
	top: 0.05rem;
	float: left;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_right_top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_title {
	font-size: 0.4rem;
	font-weight: 700;
	color: #222222;
	line-height: 0.4rem;
	margin-bottom: 0.15rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_address {
	font-size: 0.29rem;
	color: #222222;
	line-height: 0.31rem;
	margin-bottom: 0.18rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_address span {}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_address span+span {}

.new-house-swiper-list li .newhouse_list .newhouse_right .unique_list {
	margin-bottom: 0.2rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .unique_list .unique_item {
	font-size: 0.29rem;
	padding: 0.02rem 0.11rem;
	border-radius: 2px;
	text-align: center;
	margin-right: 0.21rem;
	height: 0.36rem;
	line-height: 0.36rem;
	display: inline-block;
	color: #555555;
	background: #F4F4F4;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_price .rise {
	color: #B0B0B0;
	font-size: 0.31rem;
	font-weight: normal;
	margin-left: 0.1rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_price .newhouse_unit {
	font-weight: normal;
	font-size: 0.31rem;
}

.new-house-swiper-nav {
	/* position: absolute; */
	/* left: 0; */
	/* width: 100%; */
	/* top: 1.1rem; */
	/* overflow: hidden; */
	margin-left: -0.3rem !important;
}

.new-house-swiper-nav .new-house-swiper-cont {
	overflow-x: scroll;
	white-space: nowrap;
	padding-right: 0.5rem;
}

.new-house-swiper-nav  .swiper-slide{
	text-align: center;
	height: 0.77rem;
	background: #EBEBEB;
	padding: 0 0.26rem;
	width: auto !important;
	line-height: 0.77rem;
	border-radius: 0.05rem;
	margin-left: 0.31rem;
	margin-right: 0 !important;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.41rem;
	color: #555555;
	display: inline-block;
	cursor: pointer;
}

/* TabÃƒÂ¦Ã…Â½Ã‚Â§ÃƒÂ¤Ã‚Â»Ã‚Â¶ÃƒÂ¯Ã‚Â¼Ã‹â€ ÃƒÂ¦Ã¢â‚¬â€œÃ‚Â°ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‚Â¥Ã‚Â¼ÃƒÂ§Ã¢â‚¬ÂºÃ‹Å“ÃƒÂ¥Ã‹â€ Ã¢â‚¬â€ÃƒÂ¨Ã‚Â¡Ã‚Â¨+tabÃƒÂ¯Ã‚Â¼Ã¢â‚¬Â° end*/


/* ÃƒÂ¦Ã¢â‚¬â€œÃ‚Â°ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‚ÂºÃ‚ÂÃƒÂ¥Ã‹â€ Ã¢â‚¬â€ÃƒÂ¨Ã‚Â¡Ã‚Â¨ */
.new-house-list {
	width: 100%;
	margin: 0 auto;
}
.new-house-swiper-nav div.hover {
	background: #FF8C07;
	color: #fff;
}
.new-house-list li {
	padding: 0.26rem 0;
	border: hidden;
	border-top: 1px solid #ededed;
	width: 100%;
	margin: 0 auto;
}
.new-house-list li:first-child {border-top: none}
.new-house-list>ul+ul{display: none;}
.new-house-list li:last-child {
	
}

.new-house-list li .newhouse_list {
	width: 100%;
	overflow: hidden;
	text-decoration: none;
	display: block;
}

.new-house-list li .newhouse_list .newhouse_left {
	position: relative;
	width: 2.87rem;
	height: 2.15rem;
	background: #434343;
	border-radius: 0.12rem;
	float: left;
	overflow: hidden;
}

.new-house-list li .newhouse_list .newhouse_right {
	margin-left: 0.38rem;
	width: 4.6rem;
	position: relative;
	top: 0.05rem;
	float: left;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_right_top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_title {
	font-size: 0.4rem;
	font-weight: 700;
	color: #222222;
	line-height: 0.45rem;
	margin-bottom: 0.18rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_address {
	font-size: 0.29rem;
	color: #222222;
	line-height: 0.31rem;
	margin-bottom: 0.18rem;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_address span {}

.new-house-list li .newhouse_list .newhouse_right .newhouse_address span+span {}

.new-house-list li .newhouse_list .newhouse_right .unique_list {
	margin-bottom: 0.15rem;
}

.new-house-list li .newhouse_list .newhouse_right .unique_list .unique_item {
	font-size: 0.29rem;
	padding: 0.02rem 0.11rem;
	border-radius: 2px;
	text-align: center;
	margin-right: 0.21rem;
	height: 0.36rem;
	line-height: 0.36rem;
	display: inline-block;
	color: #555555;
	background: #F4F4F4;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_price .rise {
	color: #B0B0B0;
	font-size: 0.31rem;
	font-weight: normal;
	margin-left: 0.1rem;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_price .newhouse_unit {
	font-weight: normal;
	font-size: 0.31rem;
}


.highlight-video-list {
	width: 100%;
	margin: 0 auto;
}

.highlight-video-list li:first-child {
	border-top: none
}

.highlight-line {
	border-top: 1px solid #e8e8e8;
}

.highlight-item-title {
	padding-top: 0.15rem;
	color: #303030;
	font-size: 0.36rem;
	font-weight: bold;
}

.highlight-item-des {
	margin-top: 0.15rem;
	margin-bottom: 0.15rem;
	color: #303030;
	font-size: 0.31rem;
}

.highlight-img-container {
	position: relative;
	border-radius: 0.1rem;
	overflow: hidden;
	margin-bottom: 0.15rem;
}

.highlight-item-img {
	display: block;
	width: 100%;
}

.highlight-item-play {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 0.79rem;
	height: 0.79rem;
	margin: auto;
}

/* ÃƒÂ¤Ã‚ÂºÃ…â€™ÃƒÂ¦Ã¢â‚¬Â°Ã¢â‚¬Â¹ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‚ÂºÃ‚ÂÃƒÂ¥Ã‹â€ Ã¢â‚¬â€ÃƒÂ¨Ã‚Â¡Ã‚Â¨  start*/
.second-house {}

.second-house-list {
	margin-top: -0.5rem;
}

.second-house-list li:first-child a {
	border-top: none
}

.second-house-link {
	display: block;
	color: #000;
	text-decoration: none;
	position: relative;
	padding: 15px 0 !important;
	padding-bottom: 12px !important;
	border-top: 1px solid #e8e8e8;
}

.second-house-img {
	float: left;
	width: 108px;
	height: 82px;
	overflow: hidden;
	position: relative;
	margin-right: 10px;
	border-radius: 5px;
}

.second-house-right {
	height: auto;
	float: left;
	width: 55%;
}

.left_tu {}

.second-house-title {
	font-size: 0.4rem;
	font-weight: 700;
	color: #222222;
	line-height: 0.5rem;
	margin-bottom: 0.18rem;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal;
}

.second-house-area {
	font-size: 0.29rem;
	color: #222222;
	line-height: 0.31rem;
	margin-bottom: 0.18rem;
}

.second-house-charact {
	margin-bottom: 0.15rem;
}

.second-house-charact-li {
	font-size: 0.29rem;
	padding: 0.02rem 0.11rem;
	border-radius: 2px;
	text-align: center;
	margin-right: 0.1rem;
	height: 0.36rem;
	line-height: 0.36rem;
	display: inline-block;
	color: #555555;
	background: #F4F4F4;
}

.second-house-bottom {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.second-house-price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.46rem;
	float: left;
	margin-right: 0.2rem;
}

.second-house-price i {
	font-weight: 400;
	font-style: normal;
	font-size: 12px;
}

.second-house-info {
	font-size: 0.28rem;
	line-height: 0.36rem;
	color: #545454;
	margin-top: 9px;
	float: left;
	font-family: "ÃƒÂ¥Ã‚Â¾Ã‚Â®ÃƒÂ¨Ã‚Â½Ã‚Â¯ÃƒÂ©Ã¢â‚¬ÂºÃ¢â‚¬Â¦ÃƒÂ©Ã‚Â»Ã¢â‚¬Ëœ";
	font-weight: normal;
}

.address_span span {
	float: left;
	font-size: 11px;
}

.cl {
	clear: both;
}

/* ÃƒÂ¤Ã‚ÂºÃ…â€™ÃƒÂ¦Ã¢â‚¬Â°Ã¢â‚¬Â¹ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‹â€ Ã‚Â¿ÃƒÂ¦Ã‚ÂºÃ‚ÂÃƒÂ¥Ã‹â€ Ã¢â‚¬â€ÃƒÂ¨Ã‚Â¡Ã‚Â¨  end*/

/* cont-11 start*/
.canzhan-container {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.33rem;
	/* margin: 0rem 0.31rem 0rem 0.31rem; */
	/* margin-top: -0.31rem; */
}

.canzhan-container-item {
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	/* height: 1.09rem; */
	border-radius: 0.1rem;
	border: 0.03rem solid #EDEDED;
}

.canzhan-container-item-container-text {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	border-radius: 0.1rem;
	border: 0.03rem solid #EDEDED;
	/* min-height: 1rem; */
}

.canzhan-item-container {
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	border-radius: 8rpx;
	border: 2rpx solid #EDEDED;
	min-height: 1.2rem;
}

.canzhan-container-item-text {
	-webkit-line-clamp: 2;
	text-align: center;
	margin: 0.2rem;
}

.canzhan-more-btn {
	height: 0.95rem;
	width: 2.85rem;
	margin: 22rpx 0rpx 0rpx 0rpx;
	cursor: pointer;
}

.canzhan-more-padding {
	margin-top: 0.31rem;
}

.pinpai>div:nth-child(n+16) {
	display: none;
}

.xiangmu>div:nth-child(n+16) {
	/* display: none; */
}
.zhongjie>div:nth-child(n+16) {
	display: none;
}
.cont1-btn{
	width: 3.36rem;
		height: 0.82rem;
		position: absolute;
		background: url(../img/dmBtn.png) top center;
		background-size: 100% 100%;
		right: 0;
		top: 13.7rem;
		cursor: pointer;
		display: none;
		z-index: 9999;
}
.cont1-btn:focus-visible{outline: none;}
.Bullet-switch{
    width: 1.33rem;
    height: 0.38rem;
    background: url(../img/switch2.png) top center;
    background-size: 100% 100%;
    position: absolute;
    left: 0.66rem;
    top: 0.92rem;
    scale: 1.5;
}
.Bullet-switch.close{
	    background: url(../img/switch1.png) top center;
    background-size: 100% 100%;
}
/* cont-11 end*/


/* ÃƒÂ¨Ã‚Â§Ã¢â‚¬Â ÃƒÂ©Ã‚Â¢Ã¢â‚¬ËœÃƒÂ¦Ã¢â‚¬â„¢Ã‚Â­ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¾  start*/
.video-view {
	position: fixed;
	left: 0;
	top: 0;
	background: rgb(0 0 0 / 100%);
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	display: none;
	justify-content: center;
	align-items: center;
}
.top-back {
	width: 100%;
	height: 43px;
	background-color: #fff;
	color: #000;
	text-align: center;
	font-size: 15px;
	line-height: 43px;
	z-index: 99;
	position: fixed;
	top: 0;
	left: 0;
}

.top-back .return {
	background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
	background-size: 100% 100%;
	display: block;
	width: 20px;
	height: 20px;
	float: left;
	cursor: pointer;
	/* margin-top: 0.3rem; */
	/* margin-left: 0.3rem; */
}
.prism-progress-cursor {
	display: none;
}
/* ÃƒÂ¨Ã‚Â§Ã¢â‚¬Â ÃƒÂ©Ã‚Â¢Ã¢â‚¬ËœÃƒÂ¦Ã¢â‚¬â„¢Ã‚Â­ÃƒÂ¦Ã¢â‚¬ÂÃ‚Â¾  end*/


/* ÃƒÂ©Ã¢â€šÂ¬Ã¢â‚¬Å¡ÃƒÂ©Ã¢â‚¬Â¦Ã‚ÂpcÃƒÂ§Ã‚Â«Ã‚Â¯ */

@media (min-width: 750px) {
	.Bullet-switch{
		top: 1.5rem;
	}
	.top-back{
    height: 60px;
}
	.top-back .return{
		    margin-top: 3px;
		    margin-left: 10px;
		    width: 30px;
		    height: 30px;
		}
	.cont21{
    width: 1175px !important;!i;!;
}
	.cont{
        width: 1106px;
        margin-bottom: 48px;
        padding: 35px;
        }
	.cont23Li{
    background: #F5F5F5;
    border-radius: 0.1rem;
    margin: 0 auto;
    margin-bottom: 20px;
    padding-bottom: 20px;
}
.cont23Li span{
    display: block;
    text-align: center;
    color: #fff;
    margin-bottom: 0.26rem;
    width: 226px;
    height: 90px;
    line-height: 90px;
    font-size: 40px;
}
.cont23Li p{
    line-height: 48px;
    margin: 0 auto;
    width: 90%;
    margin-bottom: 0.26rem;
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    font-size: 36px;
    color: #303030;
}
.cont23Li a{
    font-size: 36px;
}
	.cont16{
    width: 1106px;
    margin-bottom: 48px;
    padding: 35px;
}
	.cont16{
}
	.cont15Ul  li{
    width: 504px;
    height: 201px;
    border-radius: 0rem 0rem 0rem 0rem;
    float: left;
    background: #ff5200;
    /* color: #fff; */
    overflow: hidden;
    border-radius: 0.12rem;
}
.cont15Ul  li+li{margin-left: 25px;}
.cont15Ul  li a{
    display: block;
    width: 100%;
    height: 100%;
    line-height: 1.64rem;
    text-align: center;
    color: #fff;
    font-size: 32px;
}
.cont2,.cont3,.cont4,.cont5,.cont6,.cont7,.cont8,.cont9,.cont10,.cont11,.cont12,.cont13,.cont14{
}
	.cont1{
    padding: 0;
}
.cont3{}
	#container{
		height: 400px  !important;
	}
.cont4{
    /* width: 900px; */
    /* aspect-ratio: 16/ 9; */
    /* padding: 0; */
    overflow: hidden;
}
.cont5{
}
	.cont5>a{
    width: 100%;
}
	.list-item-image1{
    width: 300px;
    height: auto;
}
	.list-item-right1{
    width: 780px;
}
	.list-item-right-title1{
    font-size: 32px;
}
	.list-item-right-subtitle1{
    font-size: 22px;
    width: 98%;
}
.cont6{}
.cont7{
}
.cont8{}
.cont9{}
.cont10{}
.cont11{}
.cont12{}
	.cont12Ul li{
    margin-bottom: 27px;
}
.cont13{}
.cont14{
    /* padding-right: 0; */
}
	.cont14{
    display: flex;
    justify-content: space-between;
}
	.cont14-l{
    width: 450px;
    height: auto;
}
.cont14-r-bottom{
    width: 570px;
    height: auto;
}
.cont14-r{
    width: 570px;
}
	.cont14-r-top a{
    width: 270px;
    height: auto;
}
	.cont14-r-top{
    margin-bottom: 60px;
}
    
	.highlight-item-title {
		/* padding-top: 0.15rem; */
		font-size: 43px;
	}

	.highlight-item-des {
		margin-top: 20px;
		margin-bottom: 20px;
		/* margin-bottom: 0.15rem; */
		font-size: 37px;
	}

	.highlight-img-container {
		border-radius: 4px;
		margin-bottom: 0.15rem;
	}

	.highlight-item-img {
		display: block;
		width: 100%;
	}

	.highlight-item-play {
		width: 95px;
		height: 95px;
	}
body,
	.page {
		max-width: 100%;
	}
	.page-cont{
		margin-top:0
	}
	.page .pageW {
		width: 1106px;
		margin-bottom: 48px;
		padding: 35px;
	}

	.cont7{
    background: linear-gradient( 134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
    border-radius: 0.21rem 0.21rem 0.21rem 0.21rem;
}
.cont7-img-1{
    margin-bottom: 26px;
    width: 100%;
}
.cont7-img-1 img{}
.cont7-img-2{
    overflow: hidden;
}
.cont7-img-2 div{
    float: left;
    overflow: hidden;
    width: 49%;
}
.cont7-img-2 div+div{
    margin-left: 0.21rem;
}
.cont7-img-2 div img{
    width: 100%;
    height: auto;
    border-radius: 0.11rem 0.11rem 0.11rem 0.11rem;
    overflow: hidden;
    margin-bottom: 26px;
}
.cont7-img-2 div p{
    font-family: Microsoft YaHei, Microsoft YaHei;
    font-weight: 400;
    color: #303030;
    width: 100%;
    font-size: 22px;
}
	.page-banner-img {
		background: url(https://event.fangxiaoer.com/static/2024/m0820/img/banner2.png) top center;
		background-size: 100% 100%;
		height: 240px;
		min-width: 1170px;
	}

	.img-swiper-4-big {
		height: 828px;
	}

	.img-swiper-4-small {
		height: 153px;
	}

	.img-swiper-4-small .swiper-slide {
		height: 149px;
	}

	.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-next,
	.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-prev {
		width: 94px;
		height: 94px;
	}

	.msg-time-pageview {}

	body .page-title img {
		height: 59px
	}

	.help-cont .help-cont3 {
		font-size: 32px;
	}

	.more-new,
	.more-sec {
		width: 327px !important;
		height: 108px !important;
		margin-top: 20px !important;
	}

	.help-cont .help-cont3 b {
		font-size: 43px;
		color: #F84D6B;
	}

	.page .msg-time-pageview {
		/* margin-top: 0; */
	}

	.msg-time-pageview-cont2>div{
    width: 330px;
    height: 145px;
    margin: 0 15px;
}
	.new-house-swiper-nav  .swiper-slide {
		height: 94px;
		line-height: 94px;
		padding: 0 55px;
		font-size: 44px  !important;
		margin-left: 34px;
	}

	.new-house-list li .newhouse_list .newhouse_left,
	.second-house-img {
		width: 327px;
		height: auto;
		margin-right: 30px;
	}
	.new-house-list li .newhouse_list .newhouse_right{
    width: 600px;
}

	.second-house-link {
		padding: 30px 0 !important;
	}

	.page .new-house-list {
		width: 100%;
	}

	.new-house-swiper-nav {
		/* top: 120px; */
		margin-left: -34px !important;
	}

	.new-house-swiper .page-title {}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_title,
	.second-house-title {
		font-size: 35px;
		line-height: 44px;
		margin-bottom: 20px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_address,
	.second-house-area {
		font-size: 28px;
		line-height: 28px;
		margin-bottom: 20px;
		white-space: nowrap;
	}

	.new-house-list li .newhouse_list .newhouse_right .unique_list .unique_item,
	.second-house-charact-li {
		line-height: 50px;
		height: 50px;
		font-size: 27px;
		padding: 0 19px;
		margin-bottom: 20px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_price,
	.second-house-price {
		font-size: 50px;
		line-height: 54px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_price .newhouse_unit,
	.second-house-price i {
		font-size: 32px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_price .rise {
		font-size: 32px;
		margin-left: 010px;
	}

	.new-house-list liÃƒÂ¯Ã‚Â¼Ã…â€™.second-house-link {
		padding: 32px 0;
		border-bottom: 5px solid #ededed;
	}

	.second-house-info {
		line-height: 58px;
		font-size: 32px;
	}

	.img-swiper-4-big-p {
		font-size: 39px;
		line-height: 39px;
		margin-bottom: 20px;
	}

	.msg-time-pageview-time,
	.msg-time-pageview-text {
		font-size: 32px;
		line-height: 32px;
		margin-bottom: 26px;
	}

	.msg-time-pageview-time {
		margin-bottom: 0;
	}

	.msg-time-pageview-num {
		font-sizeÃƒÂ¯Ã‚Â¼Ã…Â¡44px;
		font-size: 44px;
		line-height: 44px;
	}



	.module-6-container {
		padding-bottom: 1.23rem;
	}


	.xc-container-item-img {
		max-height: 9.57rem;
	}

	.xc-container-item-title {
		font-size: 0.78rem;
		margin-top: 0.2rem;
		font-size: 30px;
	}
	.page-title{
    font-size: 44px;
    line-height: 1.15rem;
    margin-bottom: 0.5rem;
}
	.imgBox{
		width: 530px;
		height: 280px;
	}
	.cont-main li img {
		
		height: auto;
		margin: 0 auto;
		display: block;
	}

	.cont-main li.li1 {
		width: 500px;
		margin-bottom: 47px;
	}
	.cont-video{
		width: 80px;
		height: 80px;
		left: 42%;
		top: 23%;
	}

	.cont-main li .p1 {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		word-break: break-all;
		width: 510px;
		font-family: Microsoft YaHei, Microsoft YaHei;
		font-weight: 400;
		font-size: 36px;
		color: #303030;
		letter-spacing: normal;
		/* padding: 0 8px; */
		margin: 0 auto;
		margin-top: 26px;
	}

	.cont-main {
		width: 1043px;
	}

	.cont-main li .p2 {
		width: 1050px;
		height: 94px;
		font-family: Microsoft YaHei, Microsoft YaHei;
		font-weight: 400;
		font-size: 36px;
		color: #303030;
	}

	.cont-main li.li2 {
		line-height: 96px;
	}

	.cont-icon-r {
		width: 11px;
		height: 21px;
		float: right;
		margin-top: 40px;
	}

	.cont-main1 {
		width: 100%;
	}

	.cont-main2 {}
	.brand-item{
		width: 317px;
		height: 120px;
	}
	
	.canzhan-container {
		gap: 1.05rem;
		/* margin: 0rem 0.92rem 0rem 0.92rem; */
		/* margin-top: -0.92rem; */
	}

	.canzhan-container-item>a {
		height: 100%;
		width: 100%;
	}

	.canzhan-container-item-text {
		margin: 0.4rem;
	}

	.canzhan-more-btn {
		height: 2.9rem;
		width: 8.38rem;
		margin: 22rpx 0rpx 0rpx 0rpx;
	}

	.choujiang-container {
		margin: -0.7rem;
	}

	.choujiang-container-14 {
		margin: -0.7rem;
	}

	.cont-24-qrcode {
		width: auto;
		height: 4.8rem;
		margin-left: 7.6rem;
		margin-top: -4.1rem;
	}

	.code-img-24 {
		position: absolute;
		margin: 0.44rem;
		height: 3rem;
		width: 3rem;
	}

	.cont-14-qrcode {
		width: auto;
		height: 4.8rem;
		margin-left: 7.6rem;
		margin-top: -4.1rem;
	}

	.code-img-14 {
		position: absolute;
		margin: 0.44rem;
		height: 3rem;
		width: 3rem;
	}

	.canzhan-more-padding {
		margin-top: 0.51rem;
	}

	.canzhan-container-item-container-text {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		justify-items: center;
		overflow: hidden;
		border-radius: 0.1rem;
		border: 0.03rem solid #EDEDED;
		/* min-height: 1rem; */
	}

	.canzhan-item-container {
		min-height: 2rem;
	}

	.mini-qrcode-14-text {
		font-size: 1rem;
		line-height: 2.5rem;
		margin-left: 1.05rem;
	}

	.mini-qrcode-14-img {
		margin: 0.95rem 2rem 0.95rem 0.95rem;
		height: 5rem;
		width: 5rem;
	}

	.swiper-container-card-wall {
		width: 18.4rem;
		height: 27.6rem;
		margin: 0 auto;
	}
	.bulletChats{
    top: 10.5rem;
}
.cont1-btn{
    top: 17rem;
}

	.cont4-video{
    /* aspect-ratio: 16 / 9; */
    height: auto;
    width: 900px;
    margin: 0 auto;
    display: block;
}
.cont7-l{
    width: 340px;
    height: auto;
}
.cont7-r-bottom{
    width: 380px;
    height: auto;
}
.cont7-r{
    width: 380px;
}
	.cont7-r-top a{
    width: 183px;
    height: auto;
}
	.cont7-r-top{
    margin-bottom: 30px;
}
.leader-speak-item-img{
    height: auto;
}
	.leader-speak-item-describe{
    font-size: 30px;
}
}

.loader-container {
	height: 100%;
	width: 100%;
	position: relative;
}

.loader {
	left: 0;
	top: 0;
	position: fixed;
	width: 100vw;
	height: 100vh;
	background-color: #FFE3A5;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	z-index:99999;
}

#loader-ld {
	display: flex;
	flex-direction: row;
}

#loader-ld div {
	height: 20px;
	width: 5px;
	background: #FE4A49;
	margin: 3px;
	border-radius: 25px;
}

#loader-ld div:nth-child(1) {
	animation: loader-ld 1s ease-in-out infinite 0s;
}

#loader-ld div:nth-child(2) {
	animation: loader-ld 1s ease-in-out infinite 0.1s;
}

#loader-ld div:nth-child(3) {
	animation: loader-ld 1s ease-in-out infinite 0.2s;
}

#loader-ld div:nth-child(4) {
	animation: loader-ld 1s ease-in-out infinite 0.3s;
}

#loader-ld div:nth-child(5) {
	animation: loader-ld 1s ease-in-out infinite 0.4s;
}

#loader-ld div:nth-child(6) {
	animation: loader-ld 1s ease-in-out infinite 0.5s;
}

#loader-ld div:nth-child(7) {
	animation: loader-ld 1s ease-in-out infinite 0.6s;
}
@keyframes loader-ld {
	0% {
		transform: scaleY(1);
		background: #FED766;
	}

	25% {
		background: #009FB7;
	}

	50% { -
		transform: scaleY(2);
		background: #59CD90;
	}

	75% {
		background: #FE4A49;
	}

	100% {
		transform: scaleY(1);
		background: #D91E36;
	}
	
}
