



var ltapi = "https://ltapi.fangxiaoer.com/"
// var ltapi = "http://192.168.6.184:8083/"
var activityId = 101

var pageWidth = window.innerWidth;
var pageUrl ;
if (pageWidth < 750) {
	pageUrl = "https://m.fangxiaoer.com"
} else {
	pageUrl = "https://sy.fangxiaoer.com"
}
			

$(function() {	
	var currentDate = new Date();
				
	function formatLocalDate(date) {
		return (
			date.getFullYear() + '-' +
			(date.getMonth() + 1).toString().padStart(2, '0') + '-' +
			date.getDate().toString().padStart(2, '0')
		);
	}
	var formattedDate = formatLocalDate(currentDate);
	$(".msg-time-pageview-time").html("更新时间：" + formattedDate)
	
	// 访问量
	$.ajax({
		type: "POST",
		url: ltapi + "apiv1/wechat/housingFairTopicNewPv",
		data: {
			activityId: activityId
		},
		success: function(data) {
			console.log(data)	
			$(".msg-time-pageview-num3").text(data.content.pvTotal)
		},
	})
	// 页面数据
	$.ajax({
		type: "POST",
		url: ltapi + "apiv1/wechat/housingFairTopicIndex_2025-1",
		data: {
			activityId: activityId
		},
		success: function(data) {
			$(".loader-container").hide()
			data = data.content
			console.log(data)
				
			// 判断每个模块的显隐+标题
			tabName = data.plateStatus
			for (const key in tabName) {
				keytext = key.replace(/^tab(\d+)/, '$1');
				var keyNum = keytext.replace(/\D/g, '');
				if (tabName[key].plateStatus === '1') {
					$(".con" + keyNum).show()
				} else {
					$(".cont" + keyNum).hide()
				}
				$(".page-title-" + keytext).html(tabName[key].plateName)
				
			}
			
			
			if (pageWidth < 750) {
				$(".cont1 img").attr('src',data.plate1.m_backgroundPic)
			} else {
				$(".cont1 img").attr('src',data.plate1.web_backgroundPic)
			}
			
			$(".msg-time-pageview-num1").text(data.plate2.companyCount)
			$(".msg-time-pageview-num2").text(data.plate2.projectCount)
			
			// cont3
		
			plate3 = data.plate3.list
			// 随机选择一张图片
			function getRandomImage(plate3) {
			    // 生成一个随机索引
			    const randomIndex = Math.floor(Math.random() * plate3.length);
			    // 返回随机选择的图片
			    return plate3[randomIndex];
			}

			const randomImage = getRandomImage(plate3);
			$(".cont3 img").attr('src',randomImage)	
				
			// cont4
			if (data.plate4.title != '' && data.plate4.pic != '') {
				$(".cont4").append(`<a href="` + pageUrl + `/news/` + data.plate4.newsId + `.htm">
						<div class="list-item-image1">
							<img class="img-policy" src="` + data.plate4.pic + `" />
						</div>
						<div class="list-item-right1">
							<div class="list-item-right-title1">` + data.plate4.title + `</div>
							<div class="list-item-right-subtitle1">` + data.plate4.description + `</div>
						</div>
					</a>	
				`)
			}
			
			// cont5
			plate5 = data.plate5.list
			if (data.plate5.length != 0) {
				for (var i = 0; i < plate5.length; i++) {
					biglist = `<div class="swiper-slide"><p class="img-swiper-4-big-p">` + plate5[i].title +
						`</p><img loading="lazy" src="` + plate5[i].pic + `" /></div>`
					smalllist = `<div class="swiper-slide"><img loading="lazy" src="` + plate5[i].pic + `" /></div>`
					$(".img-swiper-4-big .swiper-wrapper").append(biglist)
					$(".img-swiper-4-small .swiper-wrapper").append(smalllist)
				}
				var swiper4Small = new Swiper(".img-swiper-4-small", {
					loop: true,
					spaceBetween: 10,
					slidesPerView: 4,
					freeMode: true,
					watchSlidesProgress: true,
				});
				var swiper4Big = new Swiper(".img-swiper-4-big", {
					loop: true,
					spaceBetween: 10,
					navigation: {
						nextEl: ".img-swiper-thumbnail-4small-swiper1-next",
						prevEl: ".img-swiper-thumbnail-4small-swiper1-prev",
					},
					thumbs: {
						swiper: swiper4Small,
					},
				});
			} else {
				$(".cont5").hide()
			}
			
			// cont6
			plate6 = data.plate6
			if (plate6.length != 0) {
				$(".cont6").append(`<div class="page-title module-6-title-padding page-title-6">` + plate6.title +`</div>
				<div class="zhiji-padding">
					<img src="` + plate6.pic +`" alt="" />
				</div>`)
			}
			
			// cont7
			plate7 = data.plate7
			if (plate7.length != 0) {
				$(".cont7").append(`<div class="page-title page-title-plate7">` + plate7.title +`</div>
				<div class="cont7-img-1"><img src="` + plate7.image +`" alt="" /></div>
				<div class="cont7-img-2"><div><img src="` + plate7.lives[0].pic +`" alt="" /><p>` + plate7.lives[0].title +`</p></div><div><img src="` + plate7.lives[0].pic +`" alt="" /><p>` + plate7.lives[0].title +`</p></div></div>`)
			}
			
			// cont8
			plate8 = data.plate8.list
			for (var i = 0; i < plate8.length; i++) {
				let item = plate8[i]
				imgList = `<div class="module-6-grid-item ">
								<div class="xc-container-item-img"><img
									src="` + item.pic + `"
									alt="" /></div>
								<div class="xc-container-item-title">` + item.title + `</div>
							</div>`
				$(".module-6-grid-content").append(imgList)
			}
			if (plate8.length > 6) {
				$(".morebtn-9").show()
			}
			$(".morebtn-8").click(function() { // 领导讲话更多按钮
				$(".module-6-grid-content>div").show()
				$(".morebtn-8").hide()
			}) 
			
			// cont9
			plate9 = data.plate9
			for (var i = 0; i < plate9.length; i++) {
				var itemShow = `<li class="leader-speak-item" data="` + plate9[i].video + `" data-img="` + plate9[i]
					.pic + `">
								<div class="leader-speak-video-container">
								<img loading="lazy" class="leader-speak-item-img" src="` + plate9[i].pic + `" alt="">
								<img loading="lazy" class="leader-speak-item-play" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/play.png" alt="">
								</div>
								<div class="leader-speak-item-describe">` + plate9[i].title + `</div>
							</li>`
				$(".leader-speak-video-list").append(itemShow)
			}
			$(".morebtn-9").hide()
			if (plate9.length > 6) {
				$(".morebtn-9").show()
			}
			$(".morebtn-9").click(function() {
				$(".module-8-page>li").show()
				$(".morebtn-9").hide()
			})
			
			plate10 = data.plate10.list
			// cont10
			for (var i = 0; i < plate10.length; i++) {
				var itemShow = `<li class="highlight-line" data-video="` + plate10[i].video + `" data-img="` + plate10[i].pic + `">
											<div  class="highlight-item-title">` + plate10[i].title + `</div>
											<div  class="highlight-item-des">` + plate10[i].description + `</div>
											<div  class="highlight-img-container">
												<img loading="lazy" class="highlight-item-img"
											src="` + plate10[i].pic + `" alt="">
												<img loading="lazy" class="highlight-item-play" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/play.png"
											alt="">
											</div>
										</li>`
				$(".highlight-video-list").append(itemShow)
			}
			
			// cont11
			plate11 = data.plate11.list
			if(plate11 != ''){
				$(".cont11").append('<ul class="cont-main"><div class="cont-main1"></div><div class="cont-main2"></div></ul>')
				plate11.forEach(item => {
					if (item != null) {
						const $container1 = $(".cont11").find('.cont-main1');
						const $container2 = $(".cont11").find('.cont-main2');
						
						var mlink ;
						if (pageWidth < 750) {
							mlink = item.m_herf
						} else {
							mlink = item.pc_herf
						}
						switch (item.type) {
							case 'IMAGE':
								let imageHtml = '<li class="li1">';
								if (mlink) {
									imageHtml += `<a href="${mlink}">`;
								}
								imageHtml +=
									`<img src="${item.pic}" alt="" /><p class="p1">${item.title}</p>`;
								if (mlink) {
									imageHtml += '</a>';
								}
								imageHtml += '</li>';
								$container1.append(imageHtml);
								break;
				
							case 'TEXT':
								const textHtml = `<li class="li2">
													<a href="${mlink}" target="_blank">
														<p class="p2">${item.title}</p>
														<s class="cont-icon-r"></s>
													</a>
												</li>`;
								$container2.append(textHtml);
								break;
				
							case 'VIDEO':
								const videoHtml = `<li class="li1 VIDEOli"  data="${item.video}" data-img="${item.pic}">
														<i class="cont-video"></i>
														<div class="imgBox"><img src="${item.pic}" alt="" /></div>
														<p class="p1">${item.title}</p>
													</li>`;
								$container1.append(videoHtml);
								break;
				
							default:
								console.warn('Unknown mediaType:', item.mediaType);
						}
					}
				});
			}
			
			// cont12
			plate12 = data.plate12.list
			if(plate12 != ''){
				for (var i = 0; i < plate12.length; i++) {
					var cont12Li = `<li><a href="` + plate12[i].href + `" target="_blank"><img src="` + plate12[i].pic + `" alt="" /></a></li>`
					$(".cont12Ul").append(cont12Li)
				}
				
			}
			
			// cont13
			newProjectType = data.newProjectType.list
		/* 	if (pageWidth < 750) {
				$(".morebtn-10").attr("href", "https://m.fangxiaoer.com/fang1l/r" + newProjectType[0].id)
			} else {
				$(".morebtn-10").attr("href", "https://sy.fangxiaoer.com/houses/r" + newProjectType[0].id)
			} */
			for (var i = 0; i < newProjectType.length; i++) {
				newProjectTypeLsit = ` <div class="swiper-slide" data="` + newProjectType[i].id +
					`">` +
					newProjectType[i].name + `</div>`
				$(".new-house-swiper-nav .swiper-wrapper").append(newProjectTypeLsit)
			}
			$(".new-house-swiper-nav>div>div").eq(0).addClass("hover")
			var swiperNav = new Swiper(".new-house-swiper-nav", {
				slidesPerView: 4.3,
				spaceBetween: 10,
				resistanceRatio: 0,
				loop: true,
			});
			var swiperSlides = document.querySelectorAll('.new-house-swiper-nav .swiper-slide');
			swiperSlides.forEach(function(slide) {
				slide.addEventListener('click', function() {
					$(".new-house-swiper-nav .swiper-slide").removeClass("hover")
					var newTypeId = this.getAttribute('data');
					$(this).addClass("hover")
					/* if ($(".new-house-list-" + newTypeId).find("li").length > 9) {
						$(".new-house-list-" + newTypeId).find("li").slice(10)
			
						$(".morebtn-10").css("display", "block")
					} else {
						$(".morebtn-10").hide()
					} */
					/* if (pageWidth < 750) {
						$(".morebtn-10").attr("href", "https://m.fangxiaoer.com/fang1l/r" + newTypeId)
					} else {
						$(".morebtn-10").attr("href", "https://sy.fangxiaoer.com/houses/r" + newTypeId)
					} */
					$(".new-house-list ul").hide()
					$(".new-house-list-" + newTypeId).show()
			
					$(".morebtn-10").attr("href", "https://m.fangxiaoer.com/fang1l/r" + newTypeId)
			
			
				});
			});
			
			for (const index in newProjectType) {
				let parttext = newProjectType[index].id
				let partBum = 'part' + parttext
				var newProjectLsitChange = data.newProject[partBum]
				newProjectLsitFun(newProjectLsitChange, parttext)
			}			
			function newProjectLsitFun(data, key) {
				$(".new-house-list").append(`<ul class="new-house-list-${key}"></ul>`)
				for (var i = 0; i < data.length; i++) {
					if (data[i].projectName != undefined) {
						var priceTypeShow;
						var priceMoney;
						var newHref;
						var priceTypeNew = data[i].mPrice;
						let Txt1 = '',
							Txt2 = '',
							Txt3 = '',
							Txt4 = '',
							Txt5 = '',
							Txt6 = '';
						if (data[i].isbs == 1) {
							Txt1 = '<div class="unique_item">别墅</div>'
						}
						if (data[i].isgy == 1) {
							Txt2 = '<div class="unique_item">公寓</div>'
						}
						if (data[i].ispz == 1) {
							Txt3 = '<div class="unique_item">普宅</div>'
						}
						if (data[i].issp == 1) {
							Txt4 = '<div class="unique_item">商铺</div>'
						}
						if (data[i].isxzj == 1) {
							Txt5 = '<div class="unique_item">写字间</div>'
						}
						if (data[i].isyf == 1) {
							Txt6 = '<div class="unique_item">洋房</div>'
						}
			
						if (pageWidth < 750) {
							newHref = `https://m.fangxiaoer.com/fang1/` + data[i].projectId + `-` +
								data[i].type + `.htm`
						} else {
							newHref = `https://sy.fangxiaoer.com/house/` + data[i].projectId +
								`-` + data[i].type +
								`.htm`
						}
			
						if (priceTypeNew != null) {
							if (priceTypeNew.priceType == '均价') {
								priceTypeShow = '<span class="rise">均</span>'
							} else {
								priceTypeShow = '<span class="rise">起</span>'
							}
							priceMoney = data[i].mPrice.priceMoney +
								'<span class="newhouse_unit">元/㎡</span>'
						} else {
							priceTypeShow = ''
							priceMoney = '待定'
						}
			
						let showArea = '';
						if (data[i].area != null) {
							showArea = '丨' + data[i].area.minArea + '~' + data[i].area.maxArea +
								'm²'
						}
			
						newList = `<li>
										<a class="newhouse_list" href="` + newHref + `" target="_blank">
											<div class="newhouse_left">
												<img loading="lazy"  src="` + data[i].indexPic + `" class="cover_pic">
											</div>
											<div class="newhouse_right">
												<div class="newhouse_right_top">
													<div class="newhouse_title">` + data[i].projectName + `</div>
												</div>
												<div class="newhouse_address">
													<span>` + data[i].regionName + `·` + data[i].showPlate + showArea + `</span>
												</div>
												<div class="unique_list">` + Txt1 + Txt2 + Txt3 + Txt4 + Txt5 + Txt6 + `</div>
												<div class="newhouse_price">` + priceMoney + priceTypeShow + `</div>
											</div>
										</a>
									</li>`
			
						$(".new-house-list-" + key).append(newList)
					}
				}
				if ($(".new-house-list-" + key).find("li").length > 9) {
					$(".new-house-list-" + key).find("li").slice(10).hide();
					$(".new-house-list-" + key).append('<a class="more-btn morebtn-10">查看更多</a>')
					$(".new-house-list-" + key).find(".morebtn-10").attr("href", "https://m.fangxiaoer.com/fang1l/r" + key)
			
				}
			}
		
			// cont14
			plate14 = data.plate14.list
			if (plate14.length != 0) {
				$(".cont14").append(`<div class="cont14-l"><a href="` + plate14[0].url + `" target="_blank"><img src="` + plate14[0].pic + `" alt="" /></a></div>
					<div class="cont14-r">
						<div class="cont14-r-top">
							<a href="` + plate14[1].url + `" target="_blank"><img src="` + plate14[1].pic + `" alt="" /></a>
							<a href="` + plate14[2].url + `" target="_blank"><img src="` + plate14[2].pic + `" alt="" /></a>
						</div>
						<div class="cont14-r-bottom"><a href="` + plate14[3].url + `" target="_blank"><img src="` + plate14[3].pic + `" alt="" /></a></div>
					</div>	
				`)
			}
			
			// cont15
			plate15 = data.plate15.list
			if(plate15 != ''){
				for (var i = 0; i < plate15.length; i++) {
					var Ahref = ''
					if (pageWidth < 750) {
						Ahref = plate15[i].m_herf
					} else {
						Ahref = plate15[i].pc_herf
					}
					var cont15Li = `<li><a href="` + Ahref + `" target="_blank"><img src="` + plate15[i].pic + `" alt=""></a></li>`
					$(".cont15Ul").append(cont15Li)
				}
				
			}
			
			// cont16
			saleHouse = data.saleHouse
			secProjectLsitFun(saleHouse)
			function secProjectLsitFun(data) {
				$(".second-house-list").html("")
				data = data["house-list"]
				total= saleHouse["house-total"]
			
				if (total > 9) {
					$(".morebtn-16").css("display","block")
					if (pageWidth < 750) {
						$(".morebtn-16").attr("href","https://m.fangxiaoer.com/fang2")
					} else {
						$(".morebtn-16").attr("href","https://sy.fangxiaoer.com/saleHouses/")
					}
				}
				var priceMoney;
				var secHref;
				for (var i = 0; i < data.length; i++) {
					if(data[i].title != undefined && data[i].title != "" ){
			
						if (data[i].price != null) {
							priceMoney = data[i].price + '<i>万</i>'
						} else {
							priceMoney = '待定'
						}
			
						var houseTrait = (data[i].houseTrait).split(",");
						var sec_charact = '';
						for (var j = 0; j < 2; j++) {
							if (houseTrait[j] != "" && houseTrait[j] != null) {
								sec_charact += '<div class="second-house-charact-li">' + houseTrait[j] + '</div>';
							}
						}
			
						if (pageWidth < 750) {
							secHref = `https://m.fangxiaoer.com/fang2/` + data[i].houseId + `.htm`
						} else {
							secHref = `https://sy.fangxiaoer.com/salehouse/` + data[i].houseId + `.htm`
						}
			
						secList = `<li class="">
											<a href="` + secHref + `" class="second-house-link" target="_blank">
												<div class="second-house-img">
													<img loading="lazy" src="` + data[i].pic + `" class="left_tu">
												</div>
												<div class="second-house-right">
													<div class="second-house-title">` + data[i].title + `</div>
													<div class="second-house-area">` + data[i].regionName + `·` + data[i].plantName + `</div>
													<ul class="second-house-charact">` + sec_charact + `</ul>
													<div class="second-house-bottom">
														<span class="second-house-price">` + priceMoney + `</span>
														<div class="second-house-info">
															<span>` + data[i].room + `室` + data[i].hall + `厅 </span>
															<span>` + data[i].area + `㎡ </span>
															<span>` + data[i].forward + `</span>
														</div>
													</div>
												</div>
												<div class="cl"></div>
											</a>
										</li>`
						$(".second-house-list").append(secList)
					}else{
						// $(".cont16").hide()
					}
				}
			}	
			

			// cont17
			plate17 = data.plate17
			for (var i = 0; i < plate17.length; i++) {
				let item = plate17[i]
				var AHrefStart = ''
				var AHrefEnd = ''
				if (item.id != '' && item.id != undefined) { //去品牌馆
					if (pageWidth > 750) { // 品牌展馆 sy站
						AHrefStart = `<a href='https://sy.fangxiaoer.com/brandCompany/` + item.id + `.htm'' target="_blank">`
						AHrefEnd = `</a>`
			
					} else { // 品牌展馆 m站
						AHrefStart = `<a href='https://m.fangxiaoer.com/brandCompany/` + item.id + `.htm''>`
						AHrefEnd = `</a>`
					}
				} else if (item.projectInfo != '' && item.projectInfo != undefined) {
					if (pageWidth > 750) { // 品牌展馆 sy站
						AHrefStart = `<a href='https://sy.fangxiaoer.com/house/` + item.projectInfo + `.htm'' target="_blank">`
						AHrefEnd = `</a>`
			
					} else { // 品牌展馆 m站
						AHrefStart = `<a href='https://m.fangxiaoer.com/fang1/` + item.projectInfo + `.htm''>`
						AHrefEnd = `</a>`
					}
				}
				imgList = `<div class="canzhan-container-item pinpai-item" >` + AHrefStart +
					`<img loading="lazy" style="object-fit: contain;" src="` + item.pic + `" alt="" />` + AHrefEnd + `
										</div>`
				$(".pinpai").append(imgList)
			}
			if (plate17.length > 15) {
				$(".morebtn-17").show()
			}
			$(".morebtn-17").click(function() { // 参展品牌更多按钮
				$(".pinpai>div").show()
				$(".morebtn-17").hide()
			})
			
			// cont18
			plate18 = data.plate18
			for (var i = 0; i < plate18.length; i++) {
				let item = plate18[i]	
				imgList =
					`<div class="canzhan-container-item-container-text xiangmu-item" data="` + item.id + '-' + item.type + `">
										<div class="canzhan-item-container">
											<div class="xc-container-item-title canzhan-container-item-text">
												` + item.title + `
											</div>
										</div>
									</div>
									</div>`
				$(".xiangmu").append(imgList)
			}
			/* if (plate18.length > 15) {
				$(".morebtn-18").show()
			} */
			$(".morebtn-18").click(function() { // 参展项目更多按钮
				$(".xiangmu>div").show()
				$(".morebtn-18").hide()
			})
			/* $(".xiangmu-item").click(function() { // 参展项目点击
				data = $(this).attr('data');
				brandId = data.split('-')[0];
				type = data.split('-')[1];
				if (brandId != '' && type != '') {
					var pageWidth = window.screen.width;
					if (brandId != '') {
						if (type != "" && type != null && type != undefined) {
							brandId = brandId + "-" + type
						}
						let url = '';
						if (pageWidth > 750) {
							url = 'https://sy.fangxiaoer.com/house/' + brandId +
								'.htm'; //新房项目 sy站
						} else {
							url = 'https://m.fangxiaoer.com/fang1/' + brandId +
								'.htm'; //新房项目 m站
						}
						window.open(url, '_blank');
					}
				}
			
			}) */
			
			// cont19
			plate19 = data.plate19
			for (var i = 0; i < plate19.length; i++) {
				let item = plate19[i]
				imgList = `<div class="canzhan-container-item-container-text">
								<div class="canzhan-item-container">
									<div class="xc-container-item-title canzhan-container-item-text">
										` + item.title + `
									</div>
								</div>
							</div>`
				$(".zhongjie").append(imgList)
			}
			if (plate19.length > 15) {
				$(".morebtn-19").show()
			}
			$(".morebtn-19").click(function() { // 参展品牌更多按钮
				$(".zhongjie>div").show()
				$(".morebtn-19").hide()
			})
			
			
			
			// cont20
			plate20 = data.plate20.list
			if (pageWidth < 750) {
				if (plate20.length != 0) {
					for (var i = 0; i < plate20.length; i++) {
						let item = plate20[i]
						imgList20 = `<a class="marquee-img" ><img src="` + plate20[i] + `" /></a>`
						$("#marquee").append(imgList20)
					}
					
					var marqueeBox = document.getElementById("marquee-box");
					var marquee = document.getElementById("marquee");
					var marqueeCopy = document.getElementById("marquee-1");
					marqueeCopy.innerHTML = marquee.innerHTML;
					function fun() {
						//从右向左
						if (marqueeBox.scrollLeft >= 800) {
							marqueeBox.scrollLeft = 0;
						} else {
							marqueeBox.scrollLeft++;
						}
								
					}
					var fun1 = setInterval(fun, 30);
				}
			}else{
				$(".cont20").hide()
			}
			
		
			
			
			
			// cont21
			plate21 = data.plate21
			var plate21Img ;
			if (pageWidth < 750) {
				plate21Img = plate21.m_pic
			} else {
				plate21Img = plate21.pc_pic
			}
			$(".cont21 img").attr('src',plate21Img)
			
			
			// cont22
			plate22 = data.plate22
			if (pageWidth < 750) {
				$(".cont22").append(`<a href="`+plate22.herf+`" target="_blank"><img src="`+plate22.pic+`" alt="" ></a>`)
			}
			
			
			// cont23
			plate23 = data.plate23.list
			for (var i = 0; i < plate23.length; i++) {
				let item = plate23[i]	
				var Ahref = ''
				if (item.m_url != ''){
					if (pageWidth < 750) {
						Ahref = `<a href="`+item.m_url+`" target="_blank">`+item.button+`</a>`
					} else {
						Ahref = `<a href="`+item.pc_url+`" target="_blank">`+item.button+`</a>`
					}
				}
				desc = (item.desc).replaceAll("$$","<br>")
				cont23Li =`<li class="cont23Li">
								<span>`+item.label+`</span>
								<p>`+desc+`</p>`+Ahref+`
							</li>`
				$(".cont23Ul").append(cont23Li)
			}
			
			
			var yuanbaoData
			$.ajax({
				url: `https://static.fangxiaoer.com/js/yuanbao.json?v=`+new Date().getTime(),
				method: 'GET',
				header: {
					'Content-Type': 'application/json'
				},
				success: function (res) {
					yuanbaoData = JSON.parse(res)
					yuanbaoData.show?$(".yuanbao").show():$(".yuanbao").hide()
					$(".yuanbao").css("backgroundImage","url("+yuanbaoData.imageUrlM+")")
			
				}
			})
	
	
	
		
			$(".cont3").click(function(){
				let appid = yuanbaoData.fxr.appId
				let appPageRouting = yuanbaoData.fxr.path
				let appUserName = yuanbaoData.fxr.originalId
				var userAgent = navigator.userAgent
	
				if (userAgent.match(/MicroMessenger/i)) {
					//当前页面在微信小程序中
					window.open('weixin://dl/business/?appid=' + appid +
							'&path=' + appPageRouting+'&query=scene=yuanbao', '_blank')
				} else if (/Mobile|Android|webOS|iPhone|iPad|BlackBerry/i.test(navigator
						.userAgent)) {
					//当前页面在移动端浏览器中
					window.open('weixin://dl/business/?appid=' + appid +
							'&path=' + appPageRouting+'&query=scene=yuanbao', '_blank')
				}
			})
		}
	})
				
	var hoveredSpans = []; // 用于存储已经添加过Hover类的span的索引
	
	$('.cont1-btn').click(function() {
		var spans = $('.bulletChats marquee>span');
		var availableSpans = [];
		spans.each(function(index) {
			if (!hoveredSpans.includes(index)) {
				availableSpans.push(index);
			}
		});
		if (availableSpans.length === 0) {
			return;
		}
		var randomIndex = Math.floor(Math.random() * availableSpans.length);
		var selectedIndex = availableSpans[randomIndex];
		$(spans[selectedIndex]).addClass('hover');
		hoveredSpans.push(selectedIndex);
	});
				
		// 视频播放
		
		$("body").on('click', '.leader-speak-item', function(event) {
			$(".video-view").append('<div id="video-view" style="width: 100%; height: 100%;"></div>')
			var videoSrc = $(this).attr('data')
			var videoPic = $(this).attr('data-img')
			aliPlayer('video-view', 'video-view', videoSrc, videoPic)
			$(".video-view").css('display', 'flex')
		})
		$("body").on('click', '.VIDEOli', function(event) {
			$(".video-view").append('<div id="video-view" style="width: 100%; height: 100%;"></div>')
			var videoSrc = $(this).attr('data')
			var videoPic = $(this).attr('data-img')
			aliPlayer('video-view', 'video-view', videoSrc, videoPic)
			$(".video-view").css('display', 'flex')
		})
		
		$("body").on('click', '.return', function(event) {
			$(".video-view #video-view").remove()
			$(".video-view").css('display', 'none')
		})
		
		function aliPlayer(vm, dm, vurl, img) {
			var vm = new Aliplayer({
				id: dm,
				width: '100%',
				height: '100%',
				autoplay: true,
				source: vurl,
				cover: img,
				playsinline: false, //是否全屏
				controlBarVisibility: "always",
				useH5Prism: true,
			}, function(player) {
			});
		}		
	



})