.pageW {
	width: 9.18rem;
	margin: 0 auto 0.41rem auto;
	padding: 0.31rem;
	background: #fff;
	border-right: 0.11rem solid #52C5FF;
	border-left: 0.11rem solid #52C5FF;
	border-bottom: 0.27rem solid #52C5FF;

}

.pageMa {
	width: 100%;
	position: relative;
	z-index: 99;
	margin-top: -1rem;
}

.pagebgs {
	width: 9.18rem;
	margin: 0 auto;
	height: 0.65rem;
	background: #299CFF;
	border-radius: 0.27rem 0.27rem 0rem 0rem;
}

.listMM {
	margin-top: 0.4rem;
}

.listMM a {
	position: relative;
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.36rem;
	color: #303030;
	line-height: 1rem;
	border-top: 0.03rem solid #EDEDED;
	display: block;
	text-overflow: ellipsis;
	white-space: pre;
	overflow: hidden;
	padding-right: 1rem;
}

.listMM a i {
	display: block;
	position: absolute;
	top: 0;
	right: 0;
	font-size: 0.33rem;
	width: 0.24rem;
	height: 0.43rem;
	color: #303030;
	font-weight: 400;
}

.hua {
	width: 2.59rem;
	height: 3.24rem;
	background-image: url(../img/b/img.png);
	background-size: 100%;
	background-repeat: no-repeat;
	background-position: center;
	position: fixed;
	bottom: -0.3rem;
	right: -1rem;
	z-index: 0;
}

.finfo {
	width: 100%;
	display: flex;
	padding-left: 0.8rem;
	box-sizing: border-box;
	margin-top: 0.5rem;
}

.finfo img {
	width: 1.76rem;
	height: 1.76rem;
	display: inline-block;
	border-radius: 50%;
	margin-right: 0.41rem;
}

.finfo p {
	font-weight: bold;
	font-size: 0.32rem;
	color: #333;
	line-height: 0.51rem;
	padding-top: 0.41rem;
}

.nhinfo {
	font-weight: 400;
	font-size: 0.32rem;
	color: #303030;
	margin-bottom: 0.11rem;
}








i,
em,
b {
	font-style: normal;
}

.page {
	padding-bottom: 0.6rem;
	background-image: url('../img/b/bg.png');
	background-size: 100% 100%;
	background-repeat: no-repeat;
	background-position: center 9rem;
        background-size: contain;
        background-repeat: repeat-y;
}

.pagec {
	padding-bottom: 0.6rem;
	background-image: url('../img/b/bg-pc.png');
	background-size: 100% 106%;
	background-repeat: no-repeat;
	background-position: center 0;
}

a {
	text-decoration: none;
	color:#333
}

img {
	width: 100%;
	height: 100%;
}

li {
	list-style: none
}

.cont-1 {
	/*margin-bottom: 0.82rem;*/
}

.page-title {
	margin-bottom: 0.31rem;
	font-size: 0.51rem;
	font-weight: bold;
	line-height: 0.52rem;
	height: 0.52rem;
	position: relative;
}

.page-title i {
	display: block;
	width: 105%;
	height: 0.3rem;
	background-color: #B1E8E0;
	position: absolute;
	bottom: 0;
	left: -0.15rem;
	z-index: 0;
	border-radius: 0.35rem 0.35rem 0.35rem 0.35rem;
}

.page-title span {
	display: block;
	position: absolute;
	left: 0;
	bottom: 0;
	z-index: 1;
}

.img-swiper-4-big {
	height: 6.44rem;
	margin-bottom: 0.3rem;
}

.page .img-swiper-thumbnail-4small-swiper1 {
	overflow: hidden;
	position: relative;
	padding: 0.31rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
	background: #fff;
}

.img-swiper-4-big-p {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: bold;
	font-size: 0.36rem;
	color: #303030;
	line-height: 0.36rem;
	margin-bottom: 0.2rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.img-swiper-4-small {
	height: 1.44rem;
}

.img-swiper-4-small .swiper-slide {
	height: 94%;
}

.swiper-slide-thumb-active {
	border: 2px solid #FF8924;
}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-prev {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-l.png) top center;
	background-size: 100% 100%;
	left: 20px;
	cursor: pointer;
	top: 56%;
}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-next {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-r.png) top center;
	background-size: 100% 100%;
	right: 20px;
	cursor: pointer;
	top: 56%;
}

.swiper-button-prev:after,
.swiper-button-next:after {
	content: '' !important
}


.leader-speak-video-list {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 0.31rem;
}

.leader-speak-item-describe {
	margin-top: 0.15rem;
	color: #303030;
	font-size: 0.31rem;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal;
}

.leader-speak-video-container {
	position: relative;
	border-radius: 4px;
	overflow: hidden;
	margin-bottom: 10px;
}

.leader-speak-item-img {
	display: block;
	width: 100%;
	height: 2.74rem;
	object-fit: cover;
}

.leader-speak-item-play {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 0.79rem;
	height: 0.79rem;
	margin: auto;
	cursor: pointer;
}

.module-8-page>li:nth-child(n+5) {
	display: none;
}

.canzhan-more-btn {
	height: 0.95rem;
	width: 2.85rem;
	margin: auto;
	margin-top: 10px;
	cursor: pointer;
}

.cont-4 {}

.cont-4 .cont-4-ul {}

.cont-4 .cont-4-li {}

.cont-4 li:first-child a {
	/* border-top:none; */
}

.cont-4 .cont-4-li a {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.36rem;
	color: #303030;
	line-height: 1rem;
	border-bottom: 0.03rem solid #EDEDED;
	display: block;
	text-overflow: ellipsis;
	white-space: pre;
	overflow: hidden;
}

.canzhan-more-padding {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
}

.cont-6 {}

.cont-6-ul {
	overflow: hidden;
}

.cont-6-ul li:nth-child(5n) {
	margin: 0
}

.cont-6-li {
	float: left;
	width: 1.44rem;
	height: 0.77rem;
	background: #EBEBEB;
	border-radius: 0.05rem 0.05rem 0.05rem 0.05rem;
	text-align: center;
	line-height: 0.77rem;
	margin: 0 0.34rem 0.26rem 0;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.41rem;
	color: #555555;
	cursor: pointer;
}

.cont-6-li.hover {
	background: #FF8C07;
	color: #fff;
}

.cont-6-ul-item li:first-child {
	border-top: none;
}

.cont-6-item {
	padding: 0.26rem 0;
	border-top: 1px solid #ededed;
	width: 100%;
	margin: 0 auto;
	overflow: hidden;
}

.yhf {
	margin-top: 0.2rem;
}

.yhf p {
	font-size: 0.32rem;
	line-height: 0.38rem;
}
.yhli div{
	float: left;
	width: 82%;
}
.yhli {
	/* display: flex; */
	margin-bottom: 0.2rem;
	overflow: hidden;
}
.yhli div p{
    margin-bottom: 0.1rem;
    display: block;
}
.yhli img {
	width: 1.03rem;
	height: 0.43rem;
	margin-right: 0.22rem;
	float: left;
}
.newhouse-list li {
	width: 103%;
	overflow: hidden;
	text-decoration: none;
	display: block;
	margin-bottom: 0.3rem;
}


.newhouse_left {
	position: relative;
	width: 2.87rem;
	height: 2.15rem;
	background: #434343;
	border-radius: 0.12rem;
	overflow: hidden;
	float: left;
}

.newhouse_right {
	margin-left: 0.28rem;
	width: 5rem;
	position: relative;
	top: 0.05rem;
	/*height: 2.15rem;*/
	float: left;
}

.newhouse_right a {
	position: absolute;
	left: 0;
	bottom: 0.02rem;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
}

.newhouse_title {
	font-weight: 700;
	color: #222222;
	line-height: 0.41rem;
	margin-bottom: 0.18rem;
	text-overflow: ellipsis;
	/* overflow: hidden; */
	white-space: nowrap;
	font-size: 0.41rem;
	position: relative;
}
 .unique_item {
	font-size: 0.29rem;
	padding: 0.02rem 0.11rem;
	border-radius: 2px;
	text-align: center;
	margin-right: 0.21rem;
	height: 0.36rem;
	line-height: 0.36rem;
	display: inline-block;
	color: #555555;
	background: #F4F4F4;
}
.newhouse_price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: PingFang SC-Bold;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.newhouse_price .rise {
	color: #B0B0B0;
	font-size: 0.31rem;
	font-weight: normal;
	margin-left: 0.1rem;
}

.newhouse_unit {
	font-weight: normal;
	font-size: 0.31rem;
}

.cont-7-ul {
	overflow: hidden;
}

.cont-7-ul li {
	float: left;
	padding: 0.31rem 0.29rem;
	border-bottom: 1px solid #ededed;
	position: relative;
	height: 8.26rem;
	width: 50%;
}

.cont-7-ul li:nth-child(2n+1):before {
	content: '';
	position: absolute;
	right: 0;
	top: 0.6rem;
	bottom: auto;
	height: 84%;
	width: 1px;
	background-color: #ededed;
}

.cont-7-ul li .cover_pic {
	width: 3.97rem;
	height: 5.28rem;
	border-radius: 0.1rem 0.1rem 0.1rem 0.1rem;
	margin-bottom: 0.49rem;
}

.cont-7-title {
	font-family: PingFang SC, PingFang SC;
	font-weight: bold;
	font-size: 0.41rem;
	color: #303030;
	line-height: 0.43rem;
	margin-bottom: 0.25rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: pre;
	width: 100%;
}

.cont-7-price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: PingFang SC-Bold;
	margin-top: 0.21rem;
	line-height: 0.55rem;
}

.cont-7-price .cont-7-unit {
	font-weight: normal;
	font-size: 0.31rem;
}

.cont-7-a {
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
	line-height: 0.31rem;
	margin-top: 0.2rem;
	display: block;
}

.cont-7 {
	padding: 0 !important;
	padding-bottom: 0.3rem !important;
}

.page-title-7 {
	padding: 0.31rem 0 0 0.31rem;
}

.cont-5 {
	padding: 0 !important;
}

.cont-4-ul li:nth-child(n+8) {
	display: none
}

.cont-7-ul li:nth-child(n+7) {
	display: none
}

.cont-6-ul-item li:nth-child(n+11) {
	display: none
}

.cont-6-p {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
	line-height: 0.35rem;
	margin-bottom: 0.32rem;
}

.video-view {
	position: fixed;
	left: 0;
	top: 0;
	background: rgb(0 0 0 / 100%);
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	display: none;
	justify-content: center;
	align-items: center;
}

#video-view {}

.top-back {
	width: 100%;
	height: 43px;
	background-color: #fff;
	color: #000;
	text-align: center;
	font-size: 15px;
	line-height: 43px;
	z-index: 99;
	position: fixed;
	top: 0;
}

.top-back .return {
	background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
	background-size: 100% 100%;
	;
	display: block;
	width: 20px;
	height: 20px;
	float: left;
	cursor: pointer;
}

.itemIconshow li {
	display: flex;
	align-items: center;
	/* åž‚ç›´å±…ä¸­ */
	margin-bottom: 0.11rem;
}

.itemIconshow li+li+li {
	display: none;
}

.itemIconshow li span {
	display: inline-block;
	font-family: PingFang SC, PingFang SC;
	font-size: 0.31rem;
	color: #FF5200;
}

.itemIconshow .itemIconTxt {}

.itemIconshow li:nth-child(2n) {}

.itemIconshow .itemIcon {
	width: 0.51rem;
	height: 0.33rem;
	float: left;
	margin-right: 0.1rem;
	display: inline-block;
}

.itemIconshow .itemIcon1 {
	background: url(../img/icon1.png) top center;
	background-size: 100% 100%;
}

.itemIconshow .itemIcon2 {
	background: url(../img/icon2.png) top center;
	background-size: 100% 100%;
}

.itemIconshow .itemIcon3 {
	background: url(../img/icon3.png) top center;
	background-size: 100% 100%;
}

.itemIconshow .itemIcon4 {
	background: url(../img/icon4.png) top center;
	background-size: 100% 100%;
}

.itemIconshow i {
	font-weight: 400;
	font-size: 0.27rem;
	color: #555555;
	line-height: 0.51rem;
	background: #F4F4F4;
	border-radius: 0.05rem 0.05rem 0.05rem 0.05rem;
	padding: 0 0.05rem;
	margin-right: 0.2rem;
}

.itemIconshow b {
	font-weight: 400;
	font-size: 0.27rem;
	color: #9D9D9D;
	margin-left: 0.11rem;
}

.itemIconTxt {
	font-family: PingFang SC, PingFang SC;
	font-weight: 650;
	font-size: 0.41rem;
	color: #FF5200;
	line-height: 0.33rem;
	text-align: left;
	font-style: normal;
	text-transform: none;
	display: inline-block;
}

.itemIconTxt1 {}

.itemIconTxt2 {}

.itemIconTxt3 {}

.itemIconTxt4 {}

.itemIconshow {}

.cont-7-title2 {
	font-family: PingFang SC, PingFang SC;
	/* font-weight: bold; */
	font-size: 0.36rem;
	color: #303030;
	line-height: 0.34rem;
}

.cont-8 {}

.cont-8 a,
.cont-9 a {
	display: block;
	width: 9.18rem;
	margin: 0 auto;
	margin-bottom: 0.41rem;
	border-radius: 0.21rem;
}

.cont-8 img {}

.cont-9 {}

.cont-9 a {}

.cont-9 img {}

/* é€‚é…pcç«¯ */
@media (min-width: 750px) {
	body {
		max-width: max-content;
	}

	.pageW {
		width: 1024px;
		padding: 35px;
		margin-bottom: 48px;
	}

	.page .img-swiper-thumbnail-4small-swiper1 {
		padding: 35px
	}

	.page-title {
		font-family: Microsoft YaHei, Microsoft YaHei;
		font-weight: bold;
		font-size: 36px;
		color: #303030;
		line-height: 48px;
		text-align: left;
		margin-bottom: 35px;
	}

	.cont-1 {
		margin-bottom: 48px;
		position: relative;
	}

	.cont-1 i {
		display: block;
		position: absolute;
		background-image: url('../img/b/n.png');
		background-size: 100%;
		background-repeat: no-repeat;
		background-position: center;

		bottom: -54px;
		width: 323px;
		height: 368px;
		right: -17px;
	}


	.img-swiper-4-big {
		height: 730px;
	}

	.img-swiper-4-big-p {
		font-size: 40px;
		line-height: 42px;
		margin-bottom: 21px;
	}

	.img-swiper-4-small {
		height: 162px;
	}

	.leader-speak-item-describe {
		margin-top: 17px;
		font-weight: 400;
		font-size: 0.36rem;
		line-height: 45px;
	}

	.leader-speak-video-container {}

	.leader-speak-item-img {
		width: 469px;
		height: 312px;
		border-radius: 4px 4px 4px 4px;
	}

	.leader-speak-item-play {
		width: 90px;
		height: 90px;
	}

	.canzhan-more-btn {
		width: 308px;
		height: 105px;
		background: #FF8C07;
		border-radius: 4px 4px 4px 4px;
		margin-top: 35px;
	}

	.cont-4 .cont-4-li a {
		line-height: 114px;
		font-size: 40px;
	}

	.cont-6-li {
		width: 163px;
		height: 88px;
		border-radius: 2px 2px 2px 2px;
		font-weight: 400;
		font-size: 45px;
		line-height: 88px;
		margin: 0 34px 29px 0;
	}

	.cont-6-item {
		padding: 30px 0;
	}

	.newhouse_left {
		width: 300px;
		height: 188px;
		border-radius: 4px 4px 4px 4px;
	}

	.newhouse_right {
		margin-left: 35px;
		/*height: 188px;*/
		width: 550px;
	}

	.newhouse_right a {
		font-size: 35px;
	}

	.newhouse_title {
		font-family: PingFang SC, PingFang SC;
		font-weight: bold;
		font-size: 36px;
		line-height: 48px;
		margin-bottom: 20px;
	}

	.newhouse_price {
		font-size: 34px;
		line-height: 36px;
	}

	.newhouse_price .rise {
		font-size: 24px;
	}

	.newhouse_unit {
		font-size: 34px;
	}

	.cont-7-ul li {
		padding: 30px;
		height: 900px;
	}

	.cont-7-ul li .cover_pic {
		width: 451px;
		height: 602px;
		border-radius: 4px 4px 4px 4px;
		margin-bottom: 53px;
	}

	.cont-7-title {
		font-size: 45px;
		margin-bottom: 28px;
		line-height: 45px;
	}

	.cont-7-price {
		font-size: 52px;
		line-height: 54px;
		margin-top: 25px;
	}

	.cont-7-price .cont-7-unit {
		font-size: 35px;
	}

	.cont-7-a {
		font-size: 35px;
		line-height: 37px;
		margin-top: 12px;
	}

	.cont-7 {
		padding-bottom: 35px !important;

	}

	.page-title-7 {
		padding: 35px;
		padding-bottom: 0;
		margin-bottom: 0;
	}

	.cont-6-p {
		font-size: 34px;
		line-height: 36px;
		margin-bottom: 35px;
	}


	.top-back {
		width: 100%;
		height: 43px;
		color: #000;/ text-align: center;
		font-size: 15px;
		line-height: 43px;
		z-index: 99;
		position: fixed;
		top: 0;
		background: none;
	}

	.top-back .return {
		background: url(https://static.fangxiaoer.com/web/images/ico/sign/kpTc_07.png) top center;
		background-size: 100% 100%;
		display: block;
		width: 50px;
		height: 50px;
		float: right;
		cursor: pointer;
		position: fixed;
		right: 40px;
		top: 40px;
	}

	.itemIconshow li {}

	.itemIconshow li+li+li {
		display: none;
	}

	.itemIconshow li span {
		font-size: 33px;
	}

	.itemIconshow .itemIconTxt {}

	.itemIconshow li:nth-child(2n) {}

	.itemIconshow .itemIcon {
		width: 53px;
		height: 33px;
		margin-right: 10px;
	}

	.finfo {
		width: 1000px;
		margin: auto;
		margin-top: 30px;
	}
	.finfo img{
    width: 176px;
    height: 176px;
}
.finfo p{
    font-size: 32px;
    line-height: 50px;
}
	.itemIconshow b {
		font-size: 35px;
		margin-left: 15px;
	}

	.yhf p {
		font-size: 16px;
	}

	.itemIconTxt {
		font-size: 48px;
		line-height: 48px;
	}

	.cont-7-title2 {
		font-size: 36px;
		line-height: 38px;
	}

	.cont-8 {}

	.cont-8 a,
	.cont-9 a {
		width: 1024px;
		margin-bottom: 48px;
	}

	.cont-8 img {}

	.cont-9 {}

	.cont-9 a {}

	.cont-9 img {}
}

.cont {
	width: 8.77rem;
	margin: 0 auto;
	margin-top: 0.41rem;
	background: #52C5FF;
	border-radius: 0.26rem 0.26rem 0rem 0rem;
	overflow: hidden;
	padding-bottom: 0.1rem;
}

.cont-line {
	width: 8.77rem;
	height: 0.62rem;
	background: #299CFF;
	border-radius: 0.26rem 0.26rem 0rem 0rem;
	display: block;
}

.cont-title {
	height: 0.54rem;
	width: auto;
	margin-bottom: 0.26rem;
}

.cont-title img {
	width: auto;
}

.cont-main {
	overflow: hidden;
	position: relative;
	width: 8.22rem;

}
.cont-main {
	overflow: hidden;
	position: relative;
	width: 8.22rem;

}

.cont-main1 {
	width: 8.22rem;
	display: grid;
	grid-template-columns: repeat(2, 1fr);
}

.cont-main2 {}

.cont-box {
	background: #fff;
	width: 96%;
	margin: 0 auto;
	padding: 0.21rem;
}

.cont-main li {}

.cont-main li.li1 {
	position: relative;
	width: 3.92rem;
	margin-bottom: 0.31rem;
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-evenly;
}

.cont-main li.li2 {
	border-top: 1px solid #EDEDED;
	line-height: 1.03rem;
	overflow: hidden;
}

.cont-main li a {
	text-decoration: none;
	color: #333;
	cursor: pointer;
}

.cont-main li img {
	width: 3.92rem;
	height: 2.95rem;
	border-radius: 0.1rem;
	margin-bottom: 0.21rem;
}

.cont-main li p {}



.cont-main li .p2 {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 1;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	width: 90%;
	float: left;
}

.cont-video {
	position: absolute;
	left: 50%;
	top: 25%;
	width: 0.79rem;
	height: 0.79rem;
	margin: auto;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/play.png) no-repeat;
	background-size: 100% 100%;
	margin-left: -0.385rem;
}

.cont-icon-r {
	display: inline-block;
	width: 0.11rem;
	height: 0.21rem;
	background: url(https://static.fangxiaoer.com/m/static/images/projectDetailNew/detail29.png) top center no-repeat;
	background-size: 100% 100%;
	margin-right: 0.12rem;
	float: right;
	margin-top: 0.45rem;
}
@media (min-width: 750px) {

	.page {
		max-width: 100%;
		background-image: url(../img/b/bg.png);
		background-size: contain;
		background-repeat: repeat-y;
		background-position: center 8rem;
	}

	.cont {
		width: 1093px;
		margin-top: 85px;
	}

	.cont-line {
		width: 100%;
		height: 76px;
	}

	.cont-box {
		width: 99%;
		padding: 22px;
	}

	.cont-title {
		height: 63px;
		margin-bottom: 32px;
	}

	.cont-main li img {
		width: 510px;
		height: 367px;
		margin: 0 auto;
		display: block;
	}

	.cont-main li.li1 {
		width: 525px;
		margin-bottom: 47px;
	}

	.cont-main li .p1 {
		display: -webkit-box;
		-webkit-box-orient: vertical;
		-webkit-line-clamp: 2;
		overflow: hidden;
		text-overflow: ellipsis;
		word-break: break-all;
		width: 510px;
		font-family: Microsoft YaHei, Microsoft YaHei;
		font-weight: 400;
		font-size: 36px;
		color: #303030;
		letter-spacing: normal;
		/* padding: 0 8px; */
		margin: 0 auto;
		margin-top: 26px;
	}

	.cont-main {
		width: 1060px;
	}

	.cont-main li .p2 {
		width: 960px;
		height: 94px;
		font-family: Microsoft YaHei, Microsoft YaHei;
		font-weight: 400;
		font-size: 36px;
		color: #303030;
	}

	.cont-main li.li2 {
		line-height: 96px;
	}

	.cont-icon-r {
		width: 11px;
		height: 21px;
		float: right;
		margin-top: 40px;
		margin-right: 30px;
	}

	.cont-main1 {
		width: 100%;
	}

	.cont-main2 {}
	.brand-item{
		width: 317px;
		height: 120px;
	}

	.brand-more-btn{
		width: 326px;
		height: 111px;
		margin-top: 35px;
	}

	.rulesHtml p b{
    font-size: 37px;
    margin-bottom: 18px;
}
	.rulesHtml li{
    font-size: 30px;
    line-height: 40px;
}
	.rulesHtml p{
		font-size: 30px;
		line-height: 40px;
	}
	.cont6 .cont-box{
		width: 96.8%;
	}
	.newhouse_left{
		width: 358px;
		height: 269px;
		border-radius: 4px 4px 4px 4px;
	}
	.newhouse_title{
    font-size: 50px;
    line-height: 52px;
    margin-bottom: 24px;
}
	.newhouse_right{
		width: 666px;
	}
	.newhouse_address{
		font-size: 32px;
		line-height: 32px;
		margin-bottom: 25px;
		white-space: nowrap;
	}
	.newhouse_price{
		font-size: 50px;
		line-height: 52px;
	}
	.newhouse_unit{
		font-size: 32px;
	}
	.newhouse_price .rise{
		font-size: 32px;
		margin-left: 10px;
	}
	.cont-video{
		width: 100px;
		height: 100px;
		margin-left: -50px;
	}
	.unique_item{
	font-size: 32px;
	height: 50px;
	line-height: 50px;
	padding: 0 10px;
	}
	.cont8{width: 600px;margin-top: 50px;}
	.cont8 img{
    width: 250px;
    height: 250px;
}
	.cont8 p{
    line-height: 250px;
    font-size: 38px;
}
	.playGame{
		width: 272px;
		height: 92px;
	}
	.playGame2{
		margin: 0 auto;
		background: url(../img/play.png) top center;
		background-size: 100% 100%;
		margin-top: 0.33rem;
		cursor: pointer;
		width: 272px;
		height: 92px;
		position: relative;
		}
		.playGame2:hover .mEvent{display:block}-
		.mEvent{
	    padding: 10px;
	    background: #fff;
	    box-shadow: 1px 2px 5px #000000b8;
	    position: absolute;
	    left: 23px;
	    bottom: 98px;
	    display: none;
		
	}
		.mEvent i{
	    width: 212px;
	    height: 212px;
	    display: block;
	    background: url(../img/m.png);
	    background-size: 100% 100%;
	}
	.yhli img{
    width: auto;
    height: 40px;
}
	.yhli div p{
    line-height: 40px;
    font-size: 32px;
}
				.mEvent p{font-size: 26px;text-align: center;margin-top: 10px;}

}