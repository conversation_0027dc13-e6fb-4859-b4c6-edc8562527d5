


$(function (){
	var ltapi = "https://ltapi.fangxiaoer.com/syfangpiao"
	// var ltapi = "http://10.7.0.146:8081";
	var pageWidth = window.innerWidth;
	if (pageWidth < 750) {
		$(".plate1-img").attr("src",'../static/2025/m0304/img/b/top.png')
		
	} else {
		$(".plate1-img").attr("src",'../static/2025/m0304/img/b/top-pc.png')
	}
	
	$.ajax({
		type: "GET",
		url: ltapi + "/api/v1/layouts/school-house",
		success: function(data) {
			
			console.log(data);
			blocks = data.blocks
			$(".finfo img").attr("src",data.miniAppQrImageUrl)
			$(".finfo p").html(data.miniAppQrTitle)
			blocks.forEach(item => {
				if (item.type == "MEDIA_TEXT" && item.hidden == false) {//资讯展示
					titleImageUrl = item.titleImageUrl 
					itemId = (item.id).slice(0, 6)
					$(".pageMa").append('<div class="cont cont' + itemId + '"></div>');
					
					$(".cont" + itemId).append('<i class="cont-line"></i>');
					$(".cont" + itemId).append(`<div class="cont-box">
													<div class="cont-title"><img src="` + item.titleImageUrl + `" alt="" /></div>
														<ul class="cont-main">
															<div class="cont-main1"></div>
															<div class="cont-main2"></div>
														</ul>
													</div>`);
						dataA = item.data;
						dataA.forEach(item => {
				 	    if (item.content != null) {
				 	        const $container1 = $(".cont" + itemId).find('.cont-main1');
				 	        const $container2 = $(".cont" + itemId).find('.cont-main2');
				 	        switch (item.content.mediaType) {
				 	            case 'IMAGE':
				 	                let imageHtml = '<li class="li1">';
				 	                if (item.content.mlink) {
				 	                    imageHtml += `<a href="${item.content.mlink}">`;
				 	                }
				 	                imageHtml += `<img src="${item.content.mediaUrl}" alt="" /><p class="p1">${item.content.title}</p>`;
				 	                if (item.content.mlink) {
				 	                    imageHtml += '</a>';
				 	                }
				 	                imageHtml += '</li>';
				 	                $container1.append(imageHtml);
				 	                break;
				 	
				 	            case 'TEXT':
				 	                const textHtml = `
				 	                    <li class="li2">
				 	                        <a href="${item.content.mlink}" target="_blank">
				 	                            <p class="p2">${item.content.title}</p>
				 	                            <s class="cont-icon-r"></s>
				 	                        </a>
				 	                    </li>`;
				 	                $container2.append(textHtml);
				 	                break;
				 	
				 	            case 'VIDEO':
				 	                const videoHtml = `
				 	                    <li class="li1 VIDEOli"  data="${item.content.mediaUrl}" data-img="${item.content.coverImgUrl}">
				 	                        <i class="cont-video"></i>
				 	                        <img src="${item.content.coverImgUrl}" alt="" />
				 	                        <p class="p1">${item.content.title}</p>
				 	                    </li>`;
				 	                $container1.append(videoHtml);
				 	                break;
				 	
				 	            default:
				 	                console.warn('Unknown mediaType:', item.content.mediaType);
				 	        }
				 	    }
				 	});
				 }else if (item.type == "HOUSE_LIST_BRAND" && item.hidden == false){//房源
					$(".pageMa").append('<div class="cont cont6"></div>');
					$(".cont6").append(`<i class="cont-line"></i>
											<div class="cont-box">
												<div class="cont-title"><img src="`+item.titleImageUrl+`" alt="" /></div>
												<ul class="newhouse-list"></ul>	
											</div>`);
					
					newProjectLsitFun(item.data,1)
				 }
			})
		}	
		
	})
	// 视频播放
	$("body").on('click', '.VIDEOli', function(event) {
		$(".music").css("animation-play-state", "paused")
		$(".music").css("-webkit-animation-play-state", "paused")
		
		$(".video-view").append('<div id="video-view" style="width: 100%; height: 100%;"></div>')
		var videoSrc = $(this).attr('data')
		var videoPic = $(this).attr('data-img')
		aliPlayer('video-view', 'video-view', videoSrc, videoPic)
		$(".video-view").css('display', 'flex')
		
	})
	$("body").on('click', '.return', function(event) {
		$(".video-view #video-view").remove()
		$(".video-view").css('display', 'none')
	})
	function aliPlayer(vm, dm, vurl, img) {
		var vm = new Aliplayer({
			id: dm,
			width: '100%',
			height: '100%',
			autoplay: true,
			source: vurl,
			cover: img,
			playsinline: false, //是否全屏
			controlBarVisibility: "always",
			useH5Prism: true,
		}, function(player) {
		});
	}
	function newProjectLsitFun(data,key) {
		console.log(data)	
		$(".newhouse-list").append(`<ul class="new-house-list-${key}"></ul>`)
		for (var i = 0; i < data.length; i++) {
			// console.log(data[i].content)
			var priceTypeShow;
			var priceMoney;
			var newHref;
			var priceTypeNew = data[i].content.price;
		
			if (pageWidth < 750) {
				newHref = `https://m.fangxiaoer.com/fang1/` + data[i].content.projectId + `-1.htm`
			} else {
				newHref = `https://sy.fangxiaoer.com/house/` + data[i].content.projectId + `-1.htm`
			}
			
			
			tagsIocn = data[i].content.tags
			var list = ''
			for (var t = 0; t < tagsIocn.length; t++) {
				list += '<div class="unique_item">'+tagsIocn[t]+'</div>'
			}
			
			schools = data[i].content.schools
			policies = data[i].content.policies
	
			schoolsHtml= ''
			policiesHtml= ''
			if(schools != ''){
				var listSchools = ''
				for (var t = 0; t < schools.length; t++) {
					listSchools += '<p>'+schools[t]+'</p>'
				}
				schoolsHtml = `<div class="yhli">
								<img src="../static/2025/m0304/img/b/tag1.png" alt=""/><div>`+listSchools+`</div>
							</div>`
			}
			if(policies != ''){
				var listPolicies = ''
				for (var t = 0; t < policies.length; t++) {
					listPolicies+= '<p>'+policies[t]+'</p>'
				}
				policiesHtml = `<div class="yhli">
								<img src="../static/2025/m0304/img/b/tag2.png" alt=""/><div>`+listPolicies+`</div>
							</div>`
			}
			
			newList = `<li>
						<a class="newhouse_list" href="` + newHref + `" target="_blank">
							<div style='display: flex'>
								<div class="newhouse_left">
									<img loading="lazy"  src="` + data[i].content.imageUrl + `" class="cover_pic">
								</div>
								<div class="newhouse_right">
									<div class="newhouse_right_top">
										<div class="newhouse_title">` + data[i].content.title + `</div>
									</div>
									<div class="newhouse_address">
										<span>` + data[i].content.location + `丨` + data[i].content.areas  +`</span>
									</div><div class="unique_list">`+list+`</div>
									<div class="newhouse_price">` + data[i].content.price  + `<span class="newhouse_unit">` + data[i].content.priceUnit  + `</span><span class="rise">` + data[i].content.priceType  + `</span></div>
								</div>
							</div>	
							<div class="yhf">`+schoolsHtml+policiesHtml+`</div>
						</a>
					</li>`
					
									
			$(".new-house-list-"+key).append(newList)
		}
		/* if ($(".new-house-list-"+key).find("li").length > 5) {
			$(".new-house-list-"+key).find("li").slice(6).hide();
			$(".more-new").show()
		} */
	}
	
	
})