


var hasMobile = "0"
var itemName = ""
var openId = "";

function getCookie(name) {
	var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
	console.log(arr);
	if(arr != null) return unescape(arr[2]);
	return null;
}



$(function() {
		// var ltapi = "http://192.168.6.250:8081";
		 var ltapi = "http://wxapi.fangxiaoer.com";
		var openId = "okQkzuE78vJWRQL3MiYUnIYeur2s";
	
		
	/*var ltapi = "https://ltapi.fangxiaoer.com";
	openId = getCookie("openId")
	if(openId == "" || openId == null) {
		 window.location = ltapi + "/apiv1/wechat/activity/40.htm"
	}*/
	var status = 0
	window.onload = function() {
		var carGame = {
			stage: new createjs.Stage("gameView"), //舞台创建
			container: new createjs.Container(), //创建容器,
			bgSpeed: 4,
			time: 1500,
			initRed: 0,
			initScore: 0,
			carBody: null,
			queue: null,
			tempImgArray: [],
			tempRedArray: [],
			noCreate: [],
			redText: null,
			redFlag: true,
			image1: new createjs.Bitmap("../static/2022/m0125/img/div2-bg.jpg"), //创建位图
			image2: new createjs.Bitmap("../static/2022/m0125/img/div2-bg.jpg"), //创建位图,
			arrAyUnique: function(arr) { //数组去重
				arr.sort(function(a, b) {
					return a - b;
				});
				var result = [arr[0]];
				for(var i = 1; i < arr.length; i++) {
					if(arr[i] !== result[result.length - 1]) {
						result.push(arr[i]);
					}
				}
				return result;
			},
			handleImg: function(obj) {
				var pt = obj.localToLocal(Math.random() * 100, Math.random() * 100, this.carBody);
				if(this.carBody.hitTest(pt.x, pt.y)) {
					obj.visible = false;
					if(/1.png/.test(obj.image.src) || /2.png/.test(obj.image.src) || /3.png/.test(obj.image.src) || /4.png/.test(obj.image.src)) {
						obj.tickEnabled = false;
						this.tempRedArray.push(obj.id);
						//去重red.id
						this.noCreate.push(obj.id);
						var temp = this.arrAyUnique(this.tempRedArray);
						if(temp.length != this.initRed) {
							this.initScore += 10
							this.createImages();
						}
						this.initRed = temp.length;
						this.redText.text = "得分：" + this.initScore;

					} else if(/5.png/.test(obj.image.src)) {
						obj.tickEnabled = false;
						this.tempRedArray.push(obj.id);
						//去重red.id
						this.noCreate.push(obj.id);
						var temp = this.arrAyUnique(this.tempRedArray);
						if(temp.length != this.initRed) {
							this.initScore += 5
							this.createImages();
						}
						this.initRed = temp.length;
						this.redText.text = "得分：" + this.initScore;
					} else {
						this.container.removeChild(obj);
						createjs.Sound.play("sound");
						createjs.Sound.removeSound("victory");
						var score = this.initScore
						console.log(score)
						$.ajax({
							type: "POST",
							data: {
								openId:openId,
								score:score,
								activityId:39
							},
							url: ltapi + "/apiv1/wechat/normalRecordResult",
							success: function(data) {
								console.log(data)
								$(".numResultSpan").text(score)
								if(data.status == 1) {
									data = data.content.content;
									console.log(data.success)
									hasMobile = data.hasMobile
									if(data.success == 1){ //可以抽奖
										if(data.hasDraw == 0){//未抽过
											$(".popup1").show()
											$(".popup").show()
										}else{//领过了
											$(".popup3").show()
											$(".popup").show()
										}
										
									}else{//不可以抽奖
										if(score<100){
											$(".popup2").show()
											$(".popup").show()
										}else{
											$(".popup3").show()
											$(".popup").show()
										}
									}
								} else {
									if(data.msg == "请勿频繁操作") {
										layer.msg(data.msg)
										setTimeout(function() {
											window.location.reload();
										}, 1000);
									} else {
										$(".popup3").show()
										$(".popup").show()
										
									}
								}
							},
							error:function(data){
								layer.msg("服务器异常")
							}
						})
						createjs.Sound.stop(); //停止背景音乐播放
						createjs.Ticker.removeAllEventListeners();
					}
				}
			}
		};
		//初始化
		carGame.init = function() {
			createjs.Touch.enable(this.stage);
			this.image2.y = -this.stage.canvas.height; //设置位图2y轴位置
			//生成roadDom
			var roadHtml = document.createElement('div');
			roadHtml.id = 'road';
			document.body.appendChild(roadHtml);
			var roadDom = new createjs.DOMElement(roadHtml);
			//红包或者炸弹信息
			var redBitMap = new createjs.Bitmap('../static/2022/m0125/img/1.png');
			var redBitMap1 = new createjs.Bitmap('../static/2022/m0125/img/2.png');
			var redBitMap2 = new createjs.Bitmap('../static/2022/m0125/img/3.png');
			var redBitMap3 = new createjs.Bitmap('../static/2022/m0125/img/4.png');
			var otherBitMap = new createjs.Bitmap('../static/2022/m0125/img/5.png');
			var boomBitMap = new createjs.Bitmap('../static/2022/m0125/img/0.png');
			this.tempImgArray.push(boomBitMap, redBitMap, redBitMap1, redBitMap2, redBitMap3, otherBitMap);

			//显示红包个数文字
			this.redText = new createjs.Text("得分：" + this.initScore, "28px 微软雅黑", "#ffffff");
			this.redText.x = 50;
			this.redText.y = 40;

			//生成小车
			var carImg = '../static/2022/m0125/img/6.png';
			this.carBody = new createjs.Bitmap(carImg);
			//小车大小与设置x轴y轴位置
			this.carBody.scaleX = 1;
			this.carBody.scaleY = 1;
			this.carBody.x = 324; //小车x轴初始位置
			this.carBody.y = this.stage.canvas.height - 150; //小车y轴初始位置

			//文件加载
			var _this = this;
			this.queue = new createjs.LoadQueue();
			this.queue.installPlugin(createjs.Sound);
			this.queue.loadManifest([{
				id: "sound",
				src: "../static/2022/m0125/mp3/boom.mp3"
			}, {
				id: "victory",
				src: "../static/2022/m0125/mp3/viry.mp3"
			}, {
				id: "bigBg",
				src: "../static/2022/m0125/img/bg.jpg"
			}]);
			//文件加载进程
			this.queue.on("progress", function() {
				document.querySelector("#load-msg").innerText = "加载中 " + parseInt(_this.queue.progress * 100) + "%...";
				//加载完成把进度条隐藏
				if(parseInt(_this.queue.progress * 100) === 100) {
					document.querySelector("#load-msg").style.display = "none";
				}
			});
			//文件加载完成执行
			this.queue.addEventListener("complete", function() {
				status = 1
				createjs.Sound.play("victory");
				_this.stage.addChild(_this.container); //将容器添加到舞台
				_this.container.addChild(_this.image1); //将位图1添加到容器
				_this.container.addChild(_this.image2); //将位图2添加到容器
				_this.stage.addChild(roadDom); //将roadDom添加到容器
				_this.container.addChild(_this.redText); //将整体内容处理
				_this.container.addChild(_this.carBody); //将小车的位图添加到容器
				//				$.ajax({
				//					type: "POST",
				//					data: {
				//						activityId: 32,
				//						openId: openId
				//					},
				//					url: ltapi + "/apiv1/wechat/viewPoorPosterInfo",
				//					success: function(data) {
				//						console.log(data)
				//						if(data.status == 1) {
				//							var bitmap1 = new createjs.Bitmap(data.content.headImgUrl);
				//							bitmap1.scaleX = 0.5;
				//							bitmap1.scaleY = 0.5;
				//							bitmap1.x = 28;
				//							bitmap1.y = 26;
				//							var circle = new createjs.Shape();
				//							circle.graphics.beginFill().drawCircle(61, 63, 33);
				//							_this.stage.addChild(bitmap1);
				//							_this.stage.addChild(circle);
				//							bitmap1.mask = circle;
				//						} else {
				//							layer.msg(data.msg)
				//						}
				//					}
				//				})
				//调用生成炸弹或者红包函数
				_this.createImages();
				setTimeout(function() {
					_this.createImages()
				}, 300);
				setTimeout(function() {
					_this.createImages()
				}, 600);
				setTimeout(function() {
					_this.createImages()
				}, 800);
				//小车拖动
				_this.moveCar();
				//背景无限滚动
				createjs.Ticker.addEventListener("tick", function() {
					_this.handleTick(_this.image1, _this.image2);
					_this.stage.update();
				});
				createjs.Ticker.setFPS(60); //设置帧频
			});
		};
		//背景图无限运动
		carGame.handleTick = function(image1, image2) {
			//背景图速度调整***
			image1.y += this.bgSpeed * (1 + 2 * Math.sin(this.initRed / 60));
			image2.y += this.bgSpeed * (1 + 2 * Math.sin(this.initRed / 60));
			if(Math.abs(image1.y) >= this.stage.canvas.height) {
				image1.y = 0;
				image2.y = -this.stage.canvas.height;
			}
		};

		//生成红包或者炸弹
		carGame.createImages = function() {
			var index = 0;
			var result = parseInt(Math.random() * 100)
			if(result <= 30) {
				index = 0
			} else if(result < 40) {
				index = 1
			} else if(result < 50) {
				index = 2
			} else if(result < 60) {
				index = 3
			} else if(result < 70) {
				index = 4
			} else {
				index = 5
			}
			var red = this.tempImgArray[index].clone(); //获取到图片地址
			//设置左边红包或者炸弹x轴位置
			var minX = this.tempImgArray[index].getBounds().width / 2
			var maxX = 750 - this.tempImgArray[index].getBounds().width / 2
			//设置红包或者炸点显示x轴位置
			red.x = (maxX - minX) * Math.random();
			red.y = -160 * (Math.random() < 0.5 ? 0.5 : Math.random());

			this.container.addChild(red);
			//添加一个监听器
			var _this = this;
			//判断红包是否碰撞
			//掉落速度调整***
			var times = _this.time
			if(_this.initRed < 60) {
				times = _this.time * (1 - 0.75 * Math.sin(_this.initRed / 60))
			} else {
				times = _this.time / 40
			}
			createjs.Tween.get(red).to({
				y: _this.stage.canvas.height
			}, times).addEventListener("change", function() {
				_this.handleImg(red);
				if(red.y >= _this.stage.canvas.height) {
					if(_this.noCreate.indexOf(red.id) != -1) {} else {
						_this.createImages();
					}
				}
			});
		};
		//拖动小车
		carGame.moveCar = function() {
			var _this = this;
			var minX = _this.carBody.getBounds().width / 2;
			var maxX = 750 - _this.carBody.getBounds().width / 2;

			_this.carBody.on("pressmove", function(evt) {
				//判断是否拖出了左右上下范围
				if(evt.stageX < minX || evt.stageX > maxX) {
					return
				}
				evt.currentTarget.x = evt.stageX - minX;
			});
		};
		$(document).on("click", "#start",function () { 
			$(".div1").hide();
			$(".div2").show()
			carGame.init();
		})
	};
	
	
	$(document).on("click", ".hongBtn",function () {
		hongBtn()
	})
	
	$(document).on("click", ".toPopup4",function () {
		$(".popup1").hide()
		$(".popup").hide()
		$(".div3").show()
		
	})
	
	// 点击红包
	$(document).on("click", ".itemBag li",function () {
		itemName = $(this).attr("data-name")
		var mobile = $("#phone").val();
		var code = $("#code").val();
		if(itemName == "恒大集团辽宁公司"){
			$(".itemName").text("恒大集团辽宁公司")
		}else{
			$(".itemName").text(itemName+"项目")
		}
		if(hasMobile == 0){
			$(".popup9").show()
			$(".popup").show()
		}else{
			$.ajax({//填表去领红包
					type: "POST",
					data: {
						openId: openId,
						mobile: mobile,
						code: code,
						activityId:39 
					},
					url: ltapi + "/apiv1/wechat/fetchMoneyForGame",
					success: function(data) {
						console.log(data)
						if(data.status == 1) {
							$(".popup5").show()
							$(".popup").show()
						} else {
							layer.msg(data.msg)
						}
					}
				})
			}
	})
	
	$(document).on("click", ".toPopup5",function () {
		$(".popup9").show()
		$(".popup").show()
		var mobile = $("#phone").val();
		var code = $("#code").val();
		if(!mobile) {
			layer.msg("请输入手机号")
		} else if(!code) {
			layer.msg("请输入验证码")
		} else {
			$.ajax({//填表去领红包
				type: "POST",
				data: {
					openId: openId,
					mobile: mobile,
					code: code,
					activityId:39 
				},
				url: ltapi + "/apiv1/wechat/fetchMoneyForGame",
				success: function(data) {
					console.log(data)
					if(data.status == 1) {
						if(itemName == "恒大集团辽宁公司"){
							$(".itemName").text("恒大集团辽宁公司")
						}else{
							$(".itemName").text(itemName+"项目")
						}
						$(".popup5").show()
						$(".popup9").hide()
						$(".popup").show()
					} else {
						layer.msg(data.msg)
					}
				}
			})
		}
	})

	
	
   $(document).on("click", ".index-btn-skill",function () { 
		$(".popup").show()
		$(".popup6").show()
	})
	$(document).on("click", ".index-btn-rule",function () { 
		$(".popup").show()
		$(".popup7").show()
	})
	$(document).on("click", "#close6",function () { 
		$(".popup").hide()
		$(".popup6").hide()
	})
	$(document).on("click", "#close7",function () {
		$(".popup").hide()
		$(".popup7").hide()
	})
	$(document).on("click", ".fenxiang",function () {
		$(".popup8").show()
		$(".popup1").hide()
		$(".popup2").hide()
		$(".popup3").hide()
		$(".popup4").hide()
		$(".popup5").hide()
	})
	
})