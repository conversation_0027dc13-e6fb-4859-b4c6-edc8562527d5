/*更多 */
var ltapi = "https://ltapi.fangxiaoer.com";

function getCookie(name) {
	var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
	console.log(arr);
	if (arr != null) return unescape(arr[2]);
	return null;
}
var openId = getCookie("openId")


$(".sharebtn").on("click", function() {
	$(".mshare").show();
	$(".fullbgn").show();
	$(".pic").show();
	$("body").css("overflow", "hidden");
	var ua = window.navigator.userAgent.toLowerCase();
	if (ua.match(/MicroMessenger/i) == 'micromessenger') {
		//alert("微信浏览器");
		$(".pic1").show();
	} else {
		//alert("不是微信浏览器");
		$(".pic2").show();
		$(".pic").css("margin-top", "60%");
	}
	//  is_weixin();
});

/*分享*/
$(function() {
	var timestamp = Date.parse(new Date());
	var url = window.location.href.split('#')[0];
	var temp = 'fangxiaoer#' + url + timestamp
	var sharecode = $.md5(temp).toUpperCase();

	$.ajax({
		url: ltapi + '/apiv1/other/getWxSign', //调用后台接口得到时间戳、签名、随机串
		type: 'post',
		dataType: 'json',
		data: {
			url: url,
			md5: sharecode,
			timeMillis: timestamp
		},
		success: function(data) {
			var data = data.content;
			var appid = data.appid;
			//console.log("appId:" +appid);
			var time = data.timestamp;
			//console.log("时间戳："+time);
			var nonceStr = data.nonceStr;
			//console.log("签名随机串:" +nonceStr);
			var signature = data.signature;
			//console.log("签名:" +signature);
			wx.config({
				debug: false, // 开启调试模式
				appId: appid, // 必填，公众号的唯一标识
				timestamp: time, // 必填，生成签名的时间戳
				nonceStr: nonceStr, // 必填，生成签名的随机串
				signature: signature, // 必填，签名，
				jsApiList: [ // 必填，需要使用的JS接口列表，
					'checkJsApi',
					'onMenuShareTimeline',
					'onMenuShareAppMessage',
					'onMenuShareQQ',
					'onMenuShareWeibo',
					'onMenuShareQZone'
				]
			});
		},
		error: function() {
			// alert("error");
		}
	});
});


wx.ready(function() {
	//分享到朋友圈  
	wx.onMenuShareTimeline({
		title: title, // 分享标题  
		desc: desc,
		link: window.location.href,
		imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" :
			imgUrl, //分享图，
		success: function() {
		
		},
		cancel: function() {
			//alert('cancel');
		}
	});

	//分享给朋友  
	wx.onMenuShareAppMessage({
		title: title, // 分享标题  
		desc: desc,
		link: window.location.href,
		imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" :
			imgUrl, //分享图，
		trigger: function(res) {
			// 不要尝试在trigger中使用ajax异步请求修改本次分享的内容，因为客户端分享操作是一个同步操作，这时候使用ajax的回包会还没有返回  
		},
		success: function(res) {
			// 分享成功执行此回调函数
			
			
			
		},


		cancel: function(res) {
			// alert('已取消');
		},
		fail: function(res) {
			// alert(JSON.stringify(res));
		}
	});

	//分享给qq
	wx.onMenuShareQQ({
		title: title, // 分享标题
		desc: desc, // 分享描述
		link: window.location.href, // 分享链接
		imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" :
			imgUrl, //分享图，
		success: function() {
			// 用户确认分享后执行的回调函数
			//alert('已分享');  
		},
		cancel: function() {
			// 用户取消分享后执行的回调函数
			//alert('已取消');  
		}
	});

	//分享到腾讯微博
	wx.onMenuShareWeibo({
		title: title, // 分享标题
		desc: desc, // 分享描述
		link: window.location.href, // 分享链接
		imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" :
			imgUrl, //分享图，
		success: function() {
			// 用户确认分享后执行的回调函数
		},
		cancel: function() {
			// 用户取消分享后执行的回调函数
		}

	});

	//分享到QQ空间
	wx.onMenuShareQZone({
		title: title, // 分享标题
		desc: desc, // 分享描述
		link: window.location.href, // 分享链接
		imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" :
			imgUrl, //分享图，
		success: function() {
			// 用户确认分享后执行的回调函数
		},
		cancel: function() {
			// 用户取消分享后执行的回调函数
		}
	});
});
// config信息验证失败会执行error函数，如签名过期导致验证失败 
wx.error(function(res) {
	// alert(res.errMsg);
	// console.log(res.errMsg)
});

/*遮罩背景*/
$(".close").on("click", function() {
	$(".fullbgn").hide();
	$(".mshare").hide();
	$(".fullbgn div").hide();
	$(".bdshare_dialog_box").hide();
	$("body").css("overflow", "auto")
});
$(".fullbgn").on("click", function() {
	$(this).hide();
	$(".mshare").hide();
	$(".fullbgn div").hide();
	$(".bdshare_dialog_box").hide();
	$("body").css("overflow", "auto")
});