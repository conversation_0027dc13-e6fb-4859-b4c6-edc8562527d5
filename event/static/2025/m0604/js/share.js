

/*更多 */
var urlt = "https://ltapi.fangxiaoer.com/";
var activityId = 83 
// var openId = 'okQkzuA1-XeFc4-sDWKjffUEyPgs'
var hasMobile = $("#hasMobile").val()

var prizeProgress;
var received;
var time = 2;//消失秒数
function time1() {//alert计时器
	time--;
	if (time >= 0) {
		setTimeout("time1()", 1000);
	} else {
		$(".fxe_alert").fadeOut();
	}
}
//alert显示内容
function fxe_alert(text1) {
	time = 2;
	$(".fxe_alert span").text(text1);
	var wid = $(".fxe_alert").width();
	$(".fxe_alert").fadeIn();
	setTimeout("time1()", 1000);
}
$(".sharebtn").on("click", function() {
    $(".mshare").show();
    $(".fullbgn").show();
    $(".pic").show();
    $("body").css("overflow", "hidden");
    var ua = window.navigator.userAgent.toLowerCase();
    if (ua.match(/MicroMessenger/i) == 'micromessenger') {
        //alert("微信浏览器");
        $(".pic1").show();
    } else {
        //alert("不是微信浏览器");
        $(".pic2").show();
        $(".pic").css("margin-top", "60%");
    }
    //  is_weixin();
});

//function is_weixin() { 
//  var ua = window.navigator.userAgent.toLowerCase(); 
//  if (ua.match(/MicroMessenger/i) == 'micromessenger') { 
//      //alert("微信浏览器");
//      $(".pic1").show();
//  } else { 
//      //alert("不是微信浏览器");
//      $(".pic2").show();
//      $(".pic").css("margin-top","60%");
//  } 
//}

/*分享*/
$(function() {
    var timestamp = Date.parse(new Date());
    var url = window.location.href.split('#')[0];
    var temp = 'fangxiaoer#' + url + timestamp
    var sharecode = $.md5(temp).toUpperCase();
    $.ajax({
        url: urlt + '/apiv1/other/getWxSign', //调用后台接口得到时间戳、签名、随机串
        type: 'post',
        dataType: 'json',
        data: { url: url, md5: sharecode, timeMillis: timestamp },
        success: function(data) {
            var data = data.content;
            var appid = data.appid;
            //console.log("appId:" +appid);
            var time = data.timestamp;
            //console.log("时间戳："+time);
            var nonceStr = data.nonceStr;
            //console.log("签名随机串:" +nonceStr);
            var signature = data.signature;
            //console.log("签名:" +signature);
            wx.config({
                debug: false, // 开启调试模式
                appId: appid, // 必填，公众号的唯一标识
                timestamp: time, // 必填，生成签名的时间戳
                nonceStr: nonceStr, // 必填，生成签名的随机串
                signature: signature, // 必填，签名，
                jsApiList: [ // 必填，需要使用的JS接口列表，
                    'checkJsApi',
                    'onMenuShareTimeline',
                    'onMenuShareAppMessage',
                    'onMenuShareQQ',
                    'onMenuShareWeibo',
                    'onMenuShareQZone'
                ]
            });
        },
        error: function() {
            // alert("error");
        }
    });
});

//完成wx.config，执行这里  
wx.ready(function() {
    //分享到朋友圈  
    wx.onMenuShareTimeline({
        title: title, // 分享标题  
        desc: desc,
        link: window.location.href,
        imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" : imgUrl, //分享图，
        success: function() {
            // 分享成功执行此回调函数  
            // alert('success');
            setTimeout(function(){
                console.log('已分享')
            	 var rk=$("#received").val()
            	var tsd = $("#hasMobile").val()
                //分享
                $.ajax({
                    type: "POST",
                    url: ltapi + "/apiv1/wechat/multipleShareEachDay",
                    data: {
                        activityId: activityId,
                        openId:openId,
                    },
                    success: function(res) {
            			var loginType = $("#loginType").val()
            			console.log(res)
            			if (res.status == 1) {
            				if(rk==0){//是否领过红包  0：否 1：是
            				
            				//弹出红包
            				$(".tc").hide()
            				$(".tc-5").show()
            				$(".full-bg").show()
            				//点击领取弹出是否需要登录注册
							var tsd = $("#hasMobile").val()
            				$("body").on('click', '.tc-5-btn', function(event) {
            					if(tsd==0){//弹出登录
            						$(".tc").hide()
            						$(".tc-4").show()
            						$(".full-bg").show()
            						$("#loginType").val("2")
            					}else{
            						//发红包
            						$.ajax({
            							type: "POST",
            							url: ltapi + "/apiv1/wechat/normalShareSendMoney",
            							data: {
            								activityId: activityId,
            								openId:openId,
            								mobile:mobile,
            								code:code,
            							},
            							success: function(res) {
            								if (res.status == 1) {
            									fxe_alert("领取成功")
            									setTimeout(function(){location.reload()},2500)
            								}
            							}
            						})
            					}
            					
            				})
            				}else{
								$(".tc").hide()
								$(".full-bg").hide()
                                fxe_alert("分享成功")
								setTimeout(function(){
									$.ajax({
										type: "POST",
										url: ltapi + "/apiv1/wechat/vankeXdxForLitLandmarkIndex",
										data: { 
											activityId: activityId,
											openId: openId,
										},
										success: function(data) {
											console.log(data)
											$(".cont2-2").html("")
											$(".cont3").html("")
											var data = data.content
											var list = data.list
											var litMemberList = data.litMemberList
											prizeProgress = data.prizeProgress
											hasMobile = data.hasMobile
											clickNum  = data.clickNum
											received = data.received
											$("#received").val(received)
											$("#hasMobile").val(hasMobile)
											console.log("手机号",hasMobile)
											$(".cont2-1").html(data.uv+"人已参与")
											if(clickNum < 0){
												clickNum =0
											}
											$(".cont4 span").html(clickNum)
											$(".xian2").css("width",data.prizeProgress*0.1*5.99+'rem')
											$(".xian2").html(data.prizeProgress*10+'%')
											
											for (var f = 0; f < litMemberList.length; f++) {
												var MemberList = '<li>'+
																	'<div><img src="'+litMemberList[f].fromMemberPic+'" alt=""></div>'+
																	'<p>'+litMemberList[f].projectName+'</p>'+
																	'<span>参与点亮</span>'+
																'</li>'
												
												$(".cont2-2").prepend(MemberList)		
											}
											
											for (var i = 0; i < list.length; i++) {
												var imglist = '<li class="imgItem" data-id="'+list[i].dustId+'">'+
															'<img src="'+list[i].showPic+'" alt="">'+
															'<p>'+list[i].title+'</p>'
														'</li>'
												
												$(".cont3").append(imglist)		
											}
										}
									})
								},2500)
                            }
            				
                       } 
                    }
                })
            
            
            },1000)
            

        },
        cancel: function() {
            //alert('cancel');
        }
    });

    //分享给朋友  
    wx.onMenuShareAppMessage({
        title: title, // 分享标题  
        desc: desc,
        link: window.location.href,
        imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" : imgUrl, //分享图，
        trigger: function(res) {
            // 不要尝试在trigger中使用ajax异步请求修改本次分享的内容，因为客户端分享操作是一个同步操作，这时候使用ajax的回包会还没有返回  
        },
        success: function(res) {
            // 分享成功执行此回调函数  
			setTimeout(function(){
			    console.log('已分享')
				 var rk=$("#received").val()
				var tsd = $("#hasMobile").val()
			    //分享
			    $.ajax({
			        type: "POST",
			        url: ltapi + "/apiv1/wechat/multipleShareEachDay",
			        data: {
			            activityId: activityId,
			            openId:openId,
			        },
			        success: function(res) {
						var loginType = $("#loginType").val()
						console.log(res)
						if (res.status == 1) {
							if(rk==0){//是否领过红包
								//弹出红包
								$(".tc").hide()
								$(".tc-5").show()
								$(".full-bg").show()
								//点击领取弹出是否需要登录注册
								var tsd = $("#hasMobile").val()
								$("body").on('click', '.tc-5-btn', function(event) {
									if(tsd==0){//弹出登录
										$(".tc").hide()
										$(".tc-4").show()
										$(".full-bg").show()
										$("#loginType").val("2")
									}else{
										//发红包
										$.ajax({
											type: "POST",
											url: ltapi + "/apiv1/wechat/normalShareSendMoney",
											data: {
												activityId: activityId,
												openId:openId,
												mobile:mobile,
												code:code,
											},
											success: function(res) {
												if (res.status == 1) {
													fxe_alert("领取成功")
													setTimeout(function(){location.reload()},2500)
												}
											}
										})
									}
									
								})
							}else{
								$(".tc").hide()
								$(".full-bg").hide()
			                    fxe_alert("分享成功")
								setTimeout(function(){
									$.ajax({
										type: "POST",
										url: ltapi + "/apiv1/wechat/vankeXdxForLitLandmarkIndex",
										data: { 
											activityId: activityId,
											openId: openId,
										},
										success: function(data) {
											console.log(data)
											$(".cont2-2").html("")
											$(".cont3").html("")
											var data = data.content
											var list = data.list
											var litMemberList = data.litMemberList
											prizeProgress = data.prizeProgress
											hasMobile = data.hasMobile
											clickNum  = data.clickNum
											received = data.received
											$("#received").val(received)
											$("#hasMobile").val(hasMobile)
											console.log("手机号",hasMobile)
											$(".cont2-1").html(data.uv+"人已参与")
											if(clickNum < 0){
												clickNum =0
											}
											$(".cont4 span").html(clickNum)
											$(".xian2").css("width",data.prizeProgress*0.1*5.99+'rem')
											$(".xian2").html(data.prizeProgress*10+'%')
											
											for (var f = 0; f < litMemberList.length; f++) {
												var MemberList = '<li>'+
																	'<div><img src="'+litMemberList[f].fromMemberPic+'" alt=""></div>'+
																	'<p>'+litMemberList[f].projectName+'</p>'+
																	'<span>参与点亮</span>'+
																'</li>'
												
												$(".cont2-2").prepend(MemberList)		
											}
											
											for (var i = 0; i < list.length; i++) {
												var imglist = '<li class="imgItem" data-id="'+list[i].dustId+'">'+
															'<img src="'+list[i].showPic+'" alt="">'+
															'<p>'+list[i].title+'</p>'
														'</li>'
												
												$(".cont3").append(imglist)		
											}
										}
									})
								},2500)
			                }
							
			           } 
			        }
			    })
			
			
			},1000)
		}
		,
        cancel: function(res) {
            // alert('已取消');
        },
        fail: function(res) {
            // alert(JSON.stringify(res));
        }
    });

    //分享给qq
    wx.onMenuShareQQ({
        title: title, // 分享标题
        desc: desc, // 分享描述
        link: window.location.href, // 分享链接
        imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" : imgUrl, //分享图，
        success: function() {
            // 用户确认分享后执行的回调函数
            //alert('已分享');  
        },
        cancel: function() {
            // 用户取消分享后执行的回调函数
            //alert('已取消');  
        }
    });

    //分享到腾讯微博
    wx.onMenuShareWeibo({
        title: title, // 分享标题
        desc: desc, // 分享描述
        link: window.location.href, // 分享链接
        imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" : imgUrl, //分享图，
        success: function() {
            // 用户确认分享后执行的回调函数
        },
        cancel: function() {
            // 用户取消分享后执行的回调函数
        }

    });

    //分享到QQ空间
    wx.onMenuShareQZone({
        title: title, // 分享标题
        desc: desc, // 分享描述
        link: window.location.href, // 分享链接
        imgUrl: imgUrl == null || imgUrl == '' ? "https://m.fangxiaoer.com/images/news_logo.jpg" : imgUrl, //分享图，
        success: function() {
            // 用户确认分享后执行的回调函数
        },
        cancel: function() {
            // 用户取消分享后执行的回调函数
        }
    });
});
// config信息验证失败会执行error函数，如签名过期导致验证失败 
wx.error(function(res) {
    // alert(res.errMsg);
    // console.log(res.errMsg)
});

/*遮罩背景*/
$(".close").on("click", function() {
    $(".fullbgn").hide();
    $(".mshare").hide();
    $(".fullbgn div").hide();
    $(".bdshare_dialog_box").hide();
    $("body").css("overflow", "auto")
});
$(".fullbgn").on("click", function() {
    $(this).hide();
    $(".mshare").hide();
    $(".fullbgn div").hide();
    $(".bdshare_dialog_box").hide();
    $("body").css("overflow", "auto")
});