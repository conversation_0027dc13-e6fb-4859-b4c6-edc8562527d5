body { margin: 0; padding: 0;
    background-color: #F5F5F5;
    background-image: url("../img/bg.png"); background-size: 100%; background-repeat: no-repeat; background-position: top center;
}
a { color: #000; text-decoration: none; }
i,em,b{ font-style: normal; }
*{ box-sizing: border-box; margin: 0; padding: 0;}
/*----------*/

.server{
    width: 9.47rem;
    /*height: 10rem;*/
    background: #FFFFFF;
    border-radius: 0.27rem;
    margin: 2.2rem auto 0.27rem auto;
    padding: 0.4rem 0.39rem;
}
.server h2{
    font-weight: bold;
    font-size: 0.43rem;
    color: #333333;
}
.server_list{}
.server_item{
    display: flex;
    align-content: center;
    align-items: center;
    gap: 0.27rem;
    border-bottom: 1px solid #EEEEEE;
    padding: 0.4rem 0;
    font-weight: 400;
    font-size: 0.37rem;
    color: #333333;
    line-height: 0.56rem;
}
.server_item:last-child{
    border-bottom: none;
}
.server_item i{
    width: 0.4rem;
    height: 0.4rem;
    background-image: url("../img/weixuan.png"); background-size: 100%; background-repeat: no-repeat; background-position: top center;
}
.server_item p{
    flex: 1;
}
.sv i{
    background-image: url("../img/xuanzhong.png") !important;
}



.contact{
    width: 9.47rem;
    background: #FFFFFF;
    border-radius: 0.27rem;
    padding: 0.4rem 0.39rem;
    margin: 0 auto;
}
.contact h2{
    font-weight: bold;
    font-size: 0.43rem;
    color: #333333;
}
.coli{
    display: flex;
    align-content: center;
    align-items: center;
    border-bottom: 1px solid #EEEEEE;
    padding: 0.43rem 0;
}
.coli i{
    width: 1.7rem;
    font-weight: 400;
    font-size: 0.37rem;
    color: #333333;
}
.coli input{
    font-weight: 400;
    font-size: 0.37rem;
    color: #333333;
    flex: 1;
}
.submit{
    width: 8.67rem;
    line-height: 1.07rem;
    background: #2E65E2;
    border-radius: 0.16rem;
    text-align: center;
    font-weight: 500;
    font-size: 0.37rem;
    color: #FFFFFF;
    margin-top: 0.4rem;
}
.notes{
    font-weight: 400;
    font-size: 0.27rem;
    color: #999999;
    text-align: center;
    margin-top: 0.29rem;
    margin-bottom: 1rem;
}
.popp{
    width: 100vw;
    height: 100vh;
    background: rgba(0,0,0,0.7);
    position: fixed;
    top: 0;
    left: 0;
    margin: auto;
    z-index: 999;
    display: none;
}



/*去除所有input默认样式*/
input {
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
    outline: none;
    border: none;
    background: none;
    padding: 0;
    margin: 0;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
}

input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
}
input[type="number"]{
    -moz-appearance: textfield;
}



.popp_content{
    width: 6.8rem;
    height: 4.29rem;
    background: #FFFFFF;
    border-radius: 0.27rem 0.27rem 0.27rem 0.27rem;
    position: fixed;
    top: 6.3rem;
    left: 0;
    right: 0;
    margin: auto;
    text-align: center;
    padding-top: 1.07rem;
}
.popp_content p{
    font-weight: 400;
    font-size: 0.37rem;
    color: #333333;
    line-height: 0.56rem;
}
.popp_btn{
    width: 2rem;
    line-height: 0.75rem;
    background: #2E65E2;
    border-radius: 0.11rem 0.11rem 0.11rem 0.11rem;
    text-align: center;
    font-weight: 400;
    font-size: 0.37rem;
    color: #FFFFFF;
    margin: 0.53rem auto;
}










