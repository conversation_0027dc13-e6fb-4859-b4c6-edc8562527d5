*{margin: 0;padding: 0;}
body{width: 100%;max-width: 640px;margin: 0 auto !important; font-family: "微软雅黑"; font-size: 12px;}
.clearfix{ clear: left}
/*导航*/
a{cursor:pointer}
.m_nav{width:100%;height:43px;background-color: #F6F6F6;color: #000;text-align:center;font-size: 15px;/* position: relative; */line-height: 43px;}

.m_nav a {
    background: url(https://static.fangxiaoer.com/m/images/nav.png) no-repeat;
    background-size: 136px auto;
}
.return{position: absolute;left:15px;top:12px;width:10px;height: 19px;}
.default_icon_fenxiang{position: absolute;right: 45px;top: 12px;width: 19px;height: 19px;background-position: -90px 0px !important;}
.catalog,.nav_close{display: block;position: absolute;right: 8px;top: 7px;width: 37px;height: 28px;background-position: -10px 5px !important;overflow: hidden;}

.catalog2{position: absolute;right: 50px;top:13px; display: none; height: 17px;width: 21px;line-height: 17px;cursor: pointer;float: right;background: none!important;font-size: 9pt;}
.catalog2 .default_icon{width: 20px;background: url(https://static.fangxiaoer.com/m/static/images/default_nav_icon.png) no-repeat -82px 0;background-size:149px auto;height:17px;display:block;float:left;cursor:pointer}
/*公共*/
.font11{font-size: 11px;}
.font12{font-size: 12px;}
.font13{font-size: 13px;}
.font14{font-size: 14px;}
.font15{font-size: 15px;}
.color000{color: #000;}
.colorff5200{color: #FF5200;}
.color757575{color:#757575;}
.bold{font-weight: bold;}
ul{list-style:none;margin:0;padding:0}
.cl{clear: both;}
.text-center{text-align: center;}
.pull-right{float: right;}
a{color:#333; text-decoration:none}
s{ text-decoration:none}
#cnzz_stat_icon_1255806361{display:none}
.hid{display:none;}
input[type="button"], input[type="submit"], input[type="reset"],input[type="search"] {
    -webkit-appearance: none;
}
.navbgn{display: none;width: 100%;height: 100%;background:rgba(0,0,0,.7);filter:alpha(opacity=80);-moz-opacity:0.8;-khtml-opacity: 0.8;opacity: 0.8;position: fixed;left: 0;top: 0;z-index: 99999;overflow: hidden;}
#MEIQIA-BTN{background-color:#ff5200 !important}

/*菜单导航*/
#m_menu{display: none;position: absolute;top: 0;width: 100%;max-width: 640px;overflow-y: scroll;-webkit-overflow-scrolling: touch;z-index: 9999999;}
#subnav{display: none; position: absolute; top: 0; background: #fff; z-index: 99999; padding-bottom: 12%;}
#subnav .m_sub{display: block; padding: 0 15px; height: 55px; line-height: 55px; font-size: 16px; border-bottom: 1px solid #f4f4f4; overflow: hidden;}
#subnav .m_sub a:first-child{display: block; float: left;}
#subnav .m_sub a:last-child{display: block; float: right; padding-right: 15px; background: url(https://static.fangxiaoer.com/m/images/renticon.png) no-repeat right center; background-size: 7px auto;}
#subnav ul{display: block; overflow: hidden;}
#subnav ul li{display: block; float: left; width: 25%; text-align: center; font-size: 12px; border-bottom: 1px solid #f4f4f4;}
#subnav ul li a{display: block; padding: 20px 0; color: #000;}
#subnav ul li a:active{color: #000 !important;}
#subnav ul li a p{height: 18px; margin-top: 8px;}
#subnav ul li a i{position:relative;display: block;margin: 0 auto;width: 45px;height: 45px;background: url(https://static.fangxiaoer.com/m/images/home/<USER>
#subnav ul li:nth-child(2) i{background-position: -46px 0;}
#subnav ul li:nth-child(3) i{background-position: 0 -44px;}
#subnav ul li:nth-child(4) i{background-position: -46px -43px;}
#subnav ul li:nth-child(5) i{background-position: 0 -88px;}
#subnav ul li:nth-child(6) i{background-position: 0 -223px;}
#subnav ul li:nth-child(7) i{background-position: 0px -135px;}
#subnav ul li:nth-child(8) i{background-position: -46px -133px;}
/*#subnav ul li:nth-child(9) i{background-position: 0 -177px;}*/
#subnav ul li:nth-child(9) i{background-position: -46px -177px;}
#subnav ul li:nth-child(10) i{background-position: -46px -89px;}
#subnav ul li:nth-child(11) i{background-position: -46px -219px;}
#subnav ul li:nth-child(12) i{background-position: 0 -272px;}
#subnav ul li:nth-child(13) i{background-position: -46px -272px;}
#subnav ul li:nth-child(14) i{background-position: 0 -313px;}
#subnav ul li:nth-child(15) i{background-position: -46px -310px;}
#subnav ul li:nth-child(16) i{background-position: 0 -356px;}
#subnav ul li:nth-child(17) i{background-position: -46px -356px;}
#subnav ul li:nth-child(18) i{background-position:0 -446px;}
#subnav ul li:nth-child(19) i{background-position:-46px -446px;}
#subnav ul li:nth-child(20) i{background-position: 0 -494px;}
#subnav ul li:nth-child(21) i{background-position:-46px -494px;}
#subnav ul li:nth-child(22) i{background-position: 0 -540px;}
/*#subnav ul li:nth-child(24) i{background-position: -46px -500px;}*/
/*#subnav ul li:nth-child(25) i{background-position: -0px -350px;}*/
/*#subnav ul li:nth-child(26) i{background-position: -45px -600px;}*/
/*#subnav ul li:nth-child(27) i{background-position: -0px -500px;}*/
#subnav .wxpro{width: 82%;margin: 20px auto;overflow: hidden;}
#subnav .wxpro img{width: 100%;}
#subnav .wxpro .wxcode{display: block;float: left;width: 74px;}
#subnav .wxpro .wxfont{display: block; float: right; font-size: 15px; line-height: 25px; padding-top: 15px;}
#subnav .wxpro .wxfont small{font-size: 13px; color: #666;}
#subnav .wxpro .wxfont p{margin: 0 !important; padding: 0 !important;}
#subnav .navdown{width: 86%; margin: 2% auto; margin-top: 10%; background: #fff; border-radius: 35px; padding: 5px 2%; -webkit-box-shadow:0 0 10px #999; -moz-box-shadow:0 0 10px #999; box-shadow:0 0 10px #999; position: relative; z-index: 999; overflow: hidden;}
#subnav .navdown .wxlogo{display: block; float: left; width: 40px;}
#subnav .navdown .wxlogo img{width: 100%;}
#subnav .navdown .wxfont{display: block;float: left;font-size: 14px;line-height: 20px;margin-left: 1%;padding-top: 3px;}
#subnav .navdown .wxfont small{font-size: 12px; color: #666;}
#subnav .navdown .wxfont p{margin: 0 !important; padding: 0 !important;}
#subnav .navdown .now{display: block;float: right;width: 24%;height: 40px;line-height: 40px;font-size: 12px;/* padding: 0 3%; */text-align: center;margin-top: 3px;background: #ff5200;border-radius: 25px;color: #fff;}
@media screen and (max-width:320px) {
    #subnav .wxpro{width: 80%;}
    #subnav .navdown .wxfont{margin: 0; font-size: 12px;}
    /*#subnav .navdown .now{padding: 0 10px;}*/
}

/*new*/
.m_nav ul{width: auto; margin: 0 auto;}
.m_nav ul li{width: 40px; display: inline-block; font-size: 16px; font-weight: normal; color: #666; line-height: 40px;}
.m_nav ul li a{background: none; padding: 0 3px; /*padding-bottom: 9px;*/}
.m_nav ul li.on a{color: #ef5200; border-bottom: 2px solid #ef5200; /*padding-bottom: 9px;*/}
.m_nav a{background-size: 139px auto; z-index: 999;}

/*登录引导框*/
#signGuide{display: none; z-index: 99999; overflow: hidden;}
#signGuide .sibgn{display: block; width: 100%; height: 100%; background:rgba(0, 0, 0,0.6);  position: fixed; top: 0; z-index: 99999; overflow: hidden;}
#signGuide .signGuide{position: fixed;top: 25%;left: 10%;max-width: 640px;width: 80%;background: #fff;z-index: 99999;border-radius: 10px; overflow: hidden;}
#signGuide .signGuide .signClose{position: absolute; top: 15px; right: 15px; width: 20px; cursor: pointer;}
#signGuide .signGuide span{display: block; color: #a86e63; font-size: 14px;}
#signGuide .signGuide span:nth-child(3){padding: 1% 12%;}
#signGuide .signGuide span i{display: block; font-size: 16px; font-style: normal; margin-bottom: 5px;}
#signGuide .signGuide span b{margin-right: 5px;}
#signGuide .signGuide span img{display: block; width: 100%;}
#signGuide .signGuide a{display: block; width: 80%; margin: 15px auto; line-height: 40px; background: #ff5200; border-radius: 5px; text-align: center; color: #fff;}

/*顶部APP下载*/
#apps{display: none; width: 100%; background: #f8f8f8; overflow: hidden;}
#apps .apps{width: 90%; margin: 3% auto; padding: 2% 3%; background: #fff; border: 1px solid #fff; background: #ffffff; box-shadow: 1px 3px 5px rgba(12,2,5,.07); border-radius: 10px; overflow: hidden;}
#apps .apps em{display: block; float: left; width: 42px; height: 42px; font-size: 0px;  background: url(https://static.fangxiaoer.com/m/static/images/home/<USER>
#apps .apps span{display: block; float: left; font-size: 15px; margin-left: 2%; line-height: 22px;}
#apps .apps span i{display: block; font-size: 13px; color: #888a92; font-style: normal;}
#apps .apps a{display: block; float: right; width: 22%; font-size: 14px; background: #ff5200; color: #fff; line-height: 30px; text-align: center; border-radius: 5px; margin-top: 8px;}
@media screen and (max-width:320px) {
    #apps .apps span{font-size: 12px;}
    #apps .apps span i{font-size: 12px;}
    #apps .apps a{font-size: 12px;}
}
/*二手房详情小区问答*/
.plotAsk{
    background:  #fff;
    margin-top: 10px;
    padding-bottom: 20px;
}
.plotAsk ul{
    width: 96%;
    margin: 0 auto;
}
.plotAsk ul li{
    overflow:  hidden;
    line-height: 18pt;
    color:  #999;
    font-size: 9pt;
}
.plotAsk ul li .Q-A{
    overflow:  hidden;
    font-size:  10pt;
    float:  left;
    width: 86%;
}
.plotAsk ul li h4{
    overflow:  hidden;
    text-overflow:  ellipsis;
    white-space: pre;
    width: 94%;
    font-weight:  normal;
    color:  #333;
    background: url(https://static.fangxiaoer.com/m/images/sale/villageQaIcon1.jpg) no-repeat 0px 4px;
    background-size:  18px 18px;
    padding-left: 22px;
}
.plotAsk ul li .iconQ-A{color:#999}
.remind{ background-color:#f6f6f6}
.remind h1{font-size:13px;color:#4b78b8;width:91px;margin:0 auto;height:35px;line-height:35px;padding-left: 25px;background: url(https://static.fangxiaoer.com/m/static/images/Reversion_wei/remind.png);background-repeat: no-repeat;background-size:  16px auto;background-position: left center;}

.home-device{position:relative}
.Advertisement{position:absolute;right: 5px;bottom: 7px;z-index:99;background-color:rgba(0,0,0,0.4);}
.Advertisement h1{font-size:12px;color: #fff;padding-left:  5px;padding-right:  5px;line-height: 20px;font-weight:  normal;}



/* 房源详情头部 改为透明板2018-10-22 */
.scrollShow {
    display: block;}
.house_information{
    height: 43px;
    background: #f6f6f600;
    display: block;
    position: fixed;
    top: 0;
    max-width: 640px;
    z-index: 99999;
    width:  100%;
}
.house_information .m_nav{display: block;position: fixed;top: 0;max-width: 640px;z-index: 99999;background: none;}
.scrollShow{
    /* display: none; */
}
.house_information .scrollShowBlock{
    display:  block;
    background: #F6F6F6;
    height:  43px;
}
.house_information .scrollShowBlock span{
    display:  block;
    overflow:  hidden;
    height: 39px;
    float:  left;
    width: 39px;
    margin-left: 40%;
    margin-top: 2px;
}
.house_information .scrollShowBlock span+span{
    margin-left: 10px;
    display: inline-block;
    height: 39px;
    float: left;
    width: 39px;
    margin-top:  2px;
}
.house_information .scrollShowBlock span+s span{margin-left: 10px;}
/*.scrollShow .pictures,.scrollShow .videos{display: none;}*/
.house_information .scrollShowBlock .videos{}
.house_information .scrollShowBlock .pictures{
    /* margin-left: 10px; */
    display:  block;
}
.house_information .scrollShowBlock span a{
    display:  block;
    width:  100%;
    position: relative;
    height:  100%;
}
.house_information .scrollShowBlock span a img{
    width:  100%;
    height:  100%;
}

.house_information .scrollShowBlock span a #prismBigPlayIcon{
    width: 20px;
    height: 20px;
    margin-left: -10px;
    margin-top: -10px;
}
.house_information .m_nav a{
    /*background: url(/images/new/navScrollShow.png);*/
    width: 30px;
    height: 30px;
    display:  block;
}
.house_information .m_nav .return{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_11.png) center;
    background-size:100%;
    background-position: 0 0 !important;
    background-size:  100% 100%;
    top: 6px;
}
.house_information .m_nav .afavorite{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_22.png) center;
    width: 30px;
    height: 30px;
    display: block;
    background-size:  100% 100%;
    background-position: 0 0 !important;
    right: 80px;
    top: 6px;
}
.house_information .m_nav .sharebtn{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_33.png) center;
    display: block;
    background-size:  100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .m_nav .cfavorite {
    position: absolute;
    right: 81px;
    cursor: pointer;
    float: right;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_22Hover.png) no-repeat;
    width: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .m_nav .catalog{
    width: 30px;
    height: 30px;
    /* background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_44.png) center no-repeat; */
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .scrollShowBlock .return{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_1.png) center;
    background-position: 0 0 !important;
    background-size: 100% 100%;
    top: 6px;
}
.house_information .scrollShowBlock .afavorite{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_2.png) center;
    widthh: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    right: 80px;
    top: 6px;
}
.house_information .scrollShowBlock .sharebtn{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_3.png) center;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
    position: fixed;
}
.house_information .scrollShowBlock .catalog{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_4.png) center no-repeat;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}

.house_information .scrollShowBlock  .cfavorite {
    position: absolute;
    right: 81px;
    cursor: pointer;
    float: right;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_2Hover.png) no-repeat;
    width: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}

.no_pl{
    text-align: center !important;
    padding-top: 50% !important;
    font-size: 10pt !important;
    color: #999898;
    margin: 0 !important;
    width: 100%;
}
.no_pl img{
    width: 80%;
    margin-bottom: 23pt;
    margin-top: -40px;
}
.no_pl p{}
*{margin: 0;padding: 0;}
body{width: 100%;max-width: 640px;margin: 0 auto !important; font-family: "微软雅黑"; font-size: 12px;}
.clearfix{ clear: left}
/*导航*/
a{cursor:pointer}
.m_nav{width:100%;height:43px;background-color: #F6F6F6;color: #000;text-align:center;font-size: 15px;/* position: relative; */line-height: 43px;}

.m_nav a {
    background: url(https://static.fangxiaoer.com/m/images/nav.png) no-repeat;
    background-size: 136px auto;
}
.return{position: absolute;left:15px;top:12px;width:10px;height: 19px;}
.default_icon_fenxiang{position: absolute;right: 45px;top: 12px;width: 19px;height: 19px;background-position: -90px 0px !important;}
.catalog,.nav_close{display: block;position: absolute;right: 8px;top: 7px;width: 37px;height: 28px;background-position: -10px 5px !important;overflow: hidden;}

.catalog2{position: absolute;right: 50px;top:13px; display: none; height: 17px;width: 21px;line-height: 17px;cursor: pointer;float: right;background: none!important;font-size: 9pt;}
.catalog2 .default_icon{width: 20px;background: url(https://static.fangxiaoer.com/m/static/images/default_nav_icon.png) no-repeat -82px 0;background-size:149px auto;height:17px;display:block;float:left;cursor:pointer}
/*公共*/
.font11{font-size: 11px;}
.font12{font-size: 12px;}
.font13{font-size: 13px;}
.font14{font-size: 14px;}
.font15{font-size: 15px;}
.color000{color: #000;}
.colorff5200{color: #FF5200;}
.color757575{color:#757575;}
.bold{font-weight: bold;}
ul{list-style:none;margin:0;padding:0}
.cl{clear: both;}
.text-center{text-align: center;}
.pull-right{float: right;}
a{color:#333; text-decoration:none}
s{ text-decoration:none}
#cnzz_stat_icon_1255806361{display:none}
.hid{display:none;}
input[type="button"], input[type="submit"], input[type="reset"],input[type="search"] {
    -webkit-appearance: none;
}
.navbgn{display: none;width: 100%;height: 100%;background:rgba(0,0,0,.7);filter:alpha(opacity=80);-moz-opacity:0.8;-khtml-opacity: 0.8;opacity: 0.8;position: fixed;left: 0;top: 0;z-index: 99999;overflow: hidden;}
#MEIQIA-BTN{background-color:#ff5200 !important}

/*菜单导航*/
#m_menu{display: none;position: absolute;top: 0;width: 100%;max-width: 640px;overflow-y: scroll;-webkit-overflow-scrolling: touch;z-index: 9999999;}
#subnav{display: none; position: absolute; top: 0; background: #fff; z-index: 99999; padding-bottom: 12%;}
#subnav .m_sub{display: block; padding: 0 15px; height: 55px; line-height: 55px; font-size: 16px; border-bottom: 1px solid #f4f4f4; overflow: hidden;}
#subnav .m_sub a:first-child{display: block; float: left;}
#subnav .m_sub a:last-child{display: block; float: right; padding-right: 15px; background: url(https://static.fangxiaoer.com/m/images/renticon.png) no-repeat right center; background-size: 7px auto;}
#subnav ul{display: block; overflow: hidden;}
#subnav ul li{display: block; float: left; width: 25%; text-align: center; font-size: 12px; border-bottom: 1px solid #f4f4f4;}
#subnav ul li a{display: block; padding: 20px 0; color: #000;}
#subnav ul li a:active{color: #000 !important;}
#subnav ul li a p{height: 18px; margin-top: 8px;}
#subnav ul li a i{position:relative;display: block;margin: 0 auto;width: 45px;height: 45px;background: url(https://static.fangxiaoer.com/m/images/home/<USER>
#subnav ul li:nth-child(2) i{background-position: -46px 0;}
#subnav ul li:nth-child(3) i{background-position: 0 -44px;}
#subnav ul li:nth-child(4) i{background-position: -46px -43px;}
#subnav ul li:nth-child(5) i{background-position: 0 -88px;}
#subnav ul li:nth-child(6) i{background-position: 0 -223px;}
#subnav ul li:nth-child(7) i{background-position: 0px -135px;}
#subnav ul li:nth-child(8) i{background-position: -46px -133px;}
/*#subnav ul li:nth-child(9) i{background-position: 0 -177px;}*/
#subnav ul li:nth-child(9) i{background-position: -46px -177px;}
#subnav ul li:nth-child(10) i{background-position: -46px -89px;}
#subnav ul li:nth-child(11) i{background-position: -46px -219px;}
#subnav ul li:nth-child(12) i{background-position: 0 -272px;}
#subnav ul li:nth-child(13) i{background-position: -46px -272px;}
#subnav ul li:nth-child(14) i{background-position: 0 -313px;}
#subnav ul li:nth-child(15) i{background-position: -46px -310px;}
#subnav ul li:nth-child(16) i{background-position: 0 -356px;}
#subnav ul li:nth-child(17) i{background-position: -46px -356px;}
#subnav ul li:nth-child(18) i{background-position:0 -446px;}
#subnav ul li:nth-child(19) i{background-position:-46px -446px;}
#subnav ul li:nth-child(20) i{background-position: 0 -494px;}
#subnav ul li:nth-child(21) i{background-position:-46px -494px;}
#subnav ul li:nth-child(22) i{background-position: 0 -540px;}
/*#subnav ul li:nth-child(24) i{background-position: -46px -500px;}*/
/*#subnav ul li:nth-child(25) i{background-position: -0px -350px;}*/
/*#subnav ul li:nth-child(26) i{background-position: -45px -600px;}*/
/*#subnav ul li:nth-child(27) i{background-position: -0px -500px;}*/
#subnav .wxpro{width: 82%;margin: 20px auto;overflow: hidden;}
#subnav .wxpro img{width: 100%;}
#subnav .wxpro .wxcode{display: block;float: left;width: 74px;}
#subnav .wxpro .wxfont{display: block; float: right; font-size: 15px; line-height: 25px; padding-top: 15px;}
#subnav .wxpro .wxfont small{font-size: 13px; color: #666;}
#subnav .wxpro .wxfont p{margin: 0 !important; padding: 0 !important;}
#subnav .navdown{width: 86%; margin: 2% auto; margin-top: 10%; background: #fff; border-radius: 35px; padding: 5px 2%; -webkit-box-shadow:0 0 10px #999; -moz-box-shadow:0 0 10px #999; box-shadow:0 0 10px #999; position: relative; z-index: 999; overflow: hidden;}
#subnav .navdown .wxlogo{display: block; float: left; width: 40px;}
#subnav .navdown .wxlogo img{width: 100%;}
#subnav .navdown .wxfont{display: block;float: left;font-size: 14px;line-height: 20px;margin-left: 1%;padding-top: 3px;}
#subnav .navdown .wxfont small{font-size: 12px; color: #666;}
#subnav .navdown .wxfont p{margin: 0 !important; padding: 0 !important;}
#subnav .navdown .now{display: block;float: right;width: 24%;height: 40px;line-height: 40px;font-size: 12px;/* padding: 0 3%; */text-align: center;margin-top: 3px;background: #ff5200;border-radius: 25px;color: #fff;}
@media screen and (max-width:320px) {
    #subnav .wxpro{width: 80%;}
    #subnav .navdown .wxfont{margin: 0; font-size: 12px;}
    /*#subnav .navdown .now{padding: 0 10px;}*/
}

/*new*/
.m_nav ul{width: auto; margin: 0 auto;}
.m_nav ul li{width: 40px; display: inline-block; font-size: 16px; font-weight: normal; color: #666; line-height: 40px;}
.m_nav ul li a{background: none; padding: 0 3px; /*padding-bottom: 9px;*/}
.m_nav ul li.on a{color: #ef5200; border-bottom: 2px solid #ef5200; /*padding-bottom: 9px;*/}
.m_nav a{background-size: 139px auto; z-index: 999;}

/*登录引导框*/
#signGuide{display: none; z-index: 99999; overflow: hidden;}
#signGuide .sibgn{display: block; width: 100%; height: 100%; background:rgba(0, 0, 0,0.6);  position: fixed; top: 0; z-index: 99999; overflow: hidden;}
#signGuide .signGuide{position: fixed;top: 25%;left: 10%;max-width: 640px;width: 80%;background: #fff;z-index: 99999;border-radius: 10px; overflow: hidden;}
#signGuide .signGuide .signClose{position: absolute; top: 15px; right: 15px; width: 20px; cursor: pointer;}
#signGuide .signGuide span{display: block; color: #a86e63; font-size: 14px;}
#signGuide .signGuide span:nth-child(3){padding: 1% 12%;}
#signGuide .signGuide span i{display: block; font-size: 16px; font-style: normal; margin-bottom: 5px;}
#signGuide .signGuide span b{margin-right: 5px;}
#signGuide .signGuide span img{display: block; width: 100%;}
#signGuide .signGuide a{display: block; width: 80%; margin: 15px auto; line-height: 40px; background: #ff5200; border-radius: 5px; text-align: center; color: #fff;}

/*顶部APP下载*/
#apps{display: none; width: 100%; background: #f8f8f8; overflow: hidden;}
#apps .apps{width: 90%; margin: 3% auto; padding: 2% 3%; background: #fff; border: 1px solid #fff; background: #ffffff; box-shadow: 1px 3px 5px rgba(12,2,5,.07); border-radius: 10px; overflow: hidden;}
#apps .apps em{display: block; float: left; width: 42px; height: 42px; font-size: 0px;  background: url(https://static.fangxiaoer.com/m/static/images/home/<USER>
#apps .apps span{display: block; float: left; font-size: 15px; margin-left: 2%; line-height: 22px;}
#apps .apps span i{display: block; font-size: 13px; color: #888a92; font-style: normal;}
#apps .apps a{display: block; float: right; width: 22%; font-size: 14px; background: #ff5200; color: #fff; line-height: 30px; text-align: center; border-radius: 5px; margin-top: 8px;}
@media screen and (max-width:320px) {
    #apps .apps span{font-size: 12px;}
    #apps .apps span i{font-size: 12px;}
    #apps .apps a{font-size: 12px;}
}
/*二手房详情小区问答*/
.plotAsk{
    background:  #fff;
    margin-top: 10px;
    padding-bottom: 20px;
}
.plotAsk ul{
    width: 96%;
    margin: 0 auto;
}
.plotAsk ul li{
    overflow:  hidden;
    line-height: 18pt;
    color:  #999;
    font-size: 9pt;
}
.plotAsk ul li .Q-A{
    overflow:  hidden;
    font-size:  10pt;
    float:  left;
    width: 86%;
}
.plotAsk ul li h4{
    overflow:  hidden;
    text-overflow:  ellipsis;
    white-space: pre;
    width: 94%;
    font-weight:  normal;
    color:  #333;
    background: url(https://static.fangxiaoer.com/m/images/sale/villageQaIcon1.jpg) no-repeat 0px 4px;
    background-size:  18px 18px;
    padding-left: 22px;
}
.plotAsk ul li .iconQ-A{color:#999}
.remind{ background-color:#f6f6f6}
.remind h1{font-size:13px;color:#4b78b8;width:91px;margin:0 auto;height:35px;line-height:35px;padding-left: 25px;background: url(https://static.fangxiaoer.com/m/static/images/Reversion_wei/remind.png);background-repeat: no-repeat;background-size:  16px auto;background-position: left center;}

.home-device{position:relative}
.Advertisement{position:absolute;right: 5px;bottom: 7px;z-index:99;background-color:rgba(0,0,0,0.4);}
.Advertisement h1{font-size:12px;color: #fff;padding-left:  5px;padding-right:  5px;line-height: 20px;font-weight:  normal;}



/* 房源详情头部 改为透明板2018-10-22 */
.scrollShow {
    display: block;}
.house_information{
    height: 43px;
    background: #f6f6f600;
    display: block;
    position: fixed;
    top: 0;
    max-width: 640px;
    z-index: 99999;
    width:  100%;
}
.house_information .m_nav{display: block;position: fixed;top: 0;max-width: 640px;z-index: 99999;background: none;}
.scrollShow{
    /* display: none; */
}
.house_information .scrollShowBlock{
    display:  block;
    background: #F6F6F6;
    height:  43px;
}
.house_information .scrollShowBlock span{
    display:  block;
    overflow:  hidden;
    height: 39px;
    float:  left;
    width: 39px;
    margin-left: 40%;
    margin-top: 2px;
}
.house_information .scrollShowBlock span+span{
    margin-left: 10px;
    display: inline-block;
    height: 39px;
    float: left;
    width: 39px;
    margin-top:  2px;
}
.house_information .scrollShowBlock span+s span{margin-left: 10px;}
/*.scrollShow .pictures,.scrollShow .videos{display: none;}*/
.house_information .scrollShowBlock .videos{}
.house_information .scrollShowBlock .pictures{
    /* margin-left: 10px; */
    display:  block;
}
.house_information .scrollShowBlock span a{
    display:  block;
    width:  100%;
    position: relative;
    height:  100%;
}
.house_information .scrollShowBlock span a img{
    width:  100%;
    height:  100%;
}

.house_information .scrollShowBlock span a #prismBigPlayIcon{
    width: 20px;
    height: 20px;
    margin-left: -10px;
    margin-top: -10px;
}
.house_information .m_nav a{
    /*background: url(/images/new/navScrollShow.png);*/
    width: 30px;
    height: 30px;
    display:  block;
}
.house_information .m_nav .return{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_11.png) center;
    background-size:100%;
    background-position: 0 0 !important;
    background-size:  100% 100%;
    top: 6px;
}
.house_information .m_nav .afavorite{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_22.png) center;
    width: 30px;
    height: 30px;
    display: block;
    background-size:  100% 100%;
    background-position: 0 0 !important;
    right: 80px;
    top: 6px;
}
.house_information .m_nav .sharebtn{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_33.png) center;
    display: block;
    background-size:  100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .m_nav .cfavorite {
    position: absolute;
    right: 81px;
    cursor: pointer;
    float: right;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_22Hover.png) no-repeat;
    width: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .m_nav .catalog{
    width: 30px;
    height: 30px;
    /* background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_44.png) center no-repeat; */
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .scrollShowBlock .return{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_1.png) center;
    background-position: 0 0 !important;
    background-size: 100% 100%;
    top: 6px;
}
.house_information .scrollShowBlock .afavorite{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_2.png) center;
    widthh: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    right: 80px;
    top: 6px;
}
.house_information .scrollShowBlock .sharebtn{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_3.png) center;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
    position: fixed;
}
.house_information .scrollShowBlock .catalog{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_4.png) center no-repeat;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}

.house_information .scrollShowBlock  .cfavorite {
    position: absolute;
    right: 81px;
    cursor: pointer;
    float: right;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_2Hover.png) no-repeat;
    width: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}

.no_pl{
    text-align: center !important;
    padding-top: 50% !important;
    font-size: 10pt !important;
    color: #999898;
    margin: 0 !important;
    width: 100%;
}
.no_pl img{
    width: 80%;
    margin-bottom: 23pt;
    margin-top: -40px;
}
.no_pl p{}
*{margin: 0;padding: 0;}
body{width: 100%;max-width: 640px;margin: 0 auto !important; font-family: "微软雅黑"; font-size: 12px;}
.clearfix{ clear: left}
/*导航*/
a{cursor:pointer}
.m_nav{width:100%;height:43px;background-color: #F6F6F6;color: #000;text-align:center;font-size: 15px;/* position: relative; */line-height: 43px;}

.m_nav a {
    background: url(https://static.fangxiaoer.com/m/images/nav.png) no-repeat;
    background-size: 136px auto;
}

/*新房详情图标位置及大小*/
.return{
    position: absolute;
    left: 23px;
    top: 12px;
    width: 10px;
    height: 19px;
}
.default_icon_fenxiang{position: absolute;right: 45px;top: 12px;width: 19px;height: 19px;background-position: -90px 0px !important;}
.catalog,.nav_close{
    display: block;
    position: absolute;
    right: 12px;
    top: 7px;
    width: 37px;
    height: 28px;
    background-position: -10px 5px !important;
    overflow: hidden;
    background: url(http://static.fangxiaoer.com/m/static/images/newSearch/btn_more.png) no-repeat !important;
    background-size: 100% 100% !important;
    display: block;
    position: absolute;
    right: 15px;
    top: 17px;
    width: 18px;
    height: 11.5px;
    overflow: hidden;
}

.catalog2{position: absolute;right: 50px;top:13px; display: none; height: 17px;width: 21px;line-height: 17px;cursor: pointer;float: right;background: none!important;font-size: 9pt;}
.catalog2 .default_icon{width: 20px;background: url(https://static.fangxiaoer.com/m/static/images/default_nav_icon.png) no-repeat -82px 0;background-size:149px auto;height:17px;display:block;float:left;cursor:pointer}
/*公共*/
.font11{font-size: 11px;}
.font12{font-size: 12px;}
.font13{font-size: 13px;}
.font14{font-size: 14px;}
.font15{font-size: 15px;}
.color000{color: #000;}
.colorff5200{color: #FF5200;}
.color757575{color:#757575;}
.bold{font-weight: bold;}
ul{list-style:none;margin:0;padding:0}
.cl{clear: both;}
.text-center{text-align: center;}
.pull-right{float: right;}
a{color:#333; text-decoration:none}
s{ text-decoration:none}
#cnzz_stat_icon_1255806361{display:none}
.hid{display:none;}
input[type="button"], input[type="submit"], input[type="reset"],input[type="search"] {
    -webkit-appearance: none;
}
.navbgn{display: none;width: 100%;height: 100%;background:rgba(0,0,0,.7);filter:alpha(opacity=80);-moz-opacity:0.8;-khtml-opacity: 0.8;opacity: 0.8;position: fixed;left: 0;top: 0;z-index: 99999;overflow: hidden;}
#MEIQIA-BTN{background-color:#ff5200 !important}

/*菜单导航*/
#m_menu{display: none;position: absolute;top: 0;width: 100%;max-width: 640px;overflow-y: scroll;-webkit-overflow-scrolling: touch;z-index: 9999999;}
#subnav{display: none; position: absolute; top: 0; background: #fff; z-index: 99999; padding-bottom: 12%;}
#subnav .m_sub{display: block; padding: 0 15px; height: 55px; line-height: 55px; font-size: 16px; border-bottom: 1px solid #f4f4f4; overflow: hidden;}
#subnav .m_sub a:first-child{display: block; float: left;}
#subnav .m_sub a:last-child{display: block; float: right; padding-right: 15px; background: url(https://static.fangxiaoer.com/m/images/renticon.png) no-repeat right center; background-size: 7px auto;}
#subnav ul{display: block; overflow: hidden;}
#subnav ul li{display: block; float: left; width: 25%; text-align: center; font-size: 12px; border-bottom: 1px solid #f4f4f4;}
#subnav ul li a{display: block; padding: 20px 0; color: #000;}
#subnav ul li a:active{color: #000 !important;}
#subnav ul li a p{height: 18px; margin-top: 8px;}
#subnav ul li a i{position:relative;display: block;margin: 0 auto;width: 45px;height: 45px;background: url(https://static.fangxiaoer.com/m/images/home/<USER>
/*#subnav ul li a i{position:relative;display: block;margin: 0 auto;width: 45px;height: 45px;background: url(../images/home/<USER>/
#subnav ul li:nth-child(2) i{background-position: -46px 0;}
#subnav ul li:nth-child(3) i{background-position: 0 -44px;}
#subnav ul li:nth-child(4) i{background-position: -46px -43px;}
#subnav ul li:nth-child(5) i{background-position: 0 -88px;}
#subnav ul li:nth-child(6) i{background-position: 0 -223px;}
#subnav ul li:nth-child(7) i{background-position: 0px -135px;}
#subnav ul li:nth-child(8) i{background-position: -46px -133px;}
/*#subnav ul li:nth-child(9) i{background-position: 0 -177px;}*/
#subnav ul li:nth-child(9) i{background-position: -46px -177px;}
#subnav ul li:nth-child(10) i{background-position: -46px -89.5px;}
#subnav ul li:nth-child(11) i{background-position: -46px -219px;}
#subnav ul li:nth-child(12) i{background-position: 0 -272px;}
#subnav ul li:nth-child(13) i{background-position: -46px -272px;}
#subnav ul li:nth-child(14) i{background-position: 0 -313px;}
#subnav ul li:nth-child(15) i{background-position: -46px -310px;}
#subnav ul li:nth-child(16) i{background-position: 0 -356px;}
#subnav ul li:nth-child(17) i{background-position: -46px -356px;}
#subnav ul li:nth-child(18) i{background-position:0 -446px;}
#subnav ul li:nth-child(19) i{background-position:-46px -446px;}
#subnav ul li:nth-child(20) i{background-position: 0 -494px;}
#subnav ul li:nth-child(21) i{background-position:-46px -494px;}
#subnav ul li:nth-child(22) i{background-position: 0 -540px;}
/*#subnav ul li:nth-child(24) i{background-position: -46px -500px;}*/
/*#subnav ul li:nth-child(25) i{background-position: -0px -350px;}*/
/*#subnav ul li:nth-child(26) i{background-position: -45px -600px;}*/
/*#subnav ul li:nth-child(27) i{background-position: -0px -500px;}*/

#subnav .wxpro{width: 82%;margin: 20px auto;overflow: hidden;}
#subnav .wxpro img{width: 100%;}
#subnav .wxpro .wxcode{display: block;float: left;width: 74px;}
#subnav .wxpro .wxfont{display: block; float: right; font-size: 15px; line-height: 25px; padding-top: 15px;}
#subnav .wxpro .wxfont small{font-size: 13px; color: #666;}
#subnav .wxpro .wxfont p{margin: 0 !important; padding: 0 !important;}
#subnav .navdown{width: 86%; margin: 2% auto; margin-top: 10%; background: #fff; border-radius: 35px; padding: 5px 2%; -webkit-box-shadow:0 0 10px #999; -moz-box-shadow:0 0 10px #999; box-shadow:0 0 10px #999; position: relative; z-index: 999; overflow: hidden;}
#subnav .navdown .wxlogo{display: block; float: left; width: 40px;}
#subnav .navdown .wxlogo img{width: 100%;}
#subnav .navdown .wxfont{display: block;float: left;font-size: 14px;line-height: 20px;margin-left: 1%;padding-top: 3px;}
#subnav .navdown .wxfont small{font-size: 12px; color: #666;}
#subnav .navdown .wxfont p{margin: 0 !important; padding: 0 !important;}
#subnav .navdown .now{display: block;float: right;width: 24%;height: 40px;line-height: 40px;font-size: 12px;/* padding: 0 3%; */text-align: center;margin-top: 3px;background: #ff5200;border-radius: 25px;color: #fff;}
@media screen and (max-width:320px) {
    #subnav .wxpro{width: 80%;}
    #subnav .navdown .wxfont{margin: 0; font-size: 12px;}
    /*#subnav .navdown .now{padding: 0 10px;}*/
}

/*new*/
.m_nav ul{width: auto; margin: 0 auto;}
.m_nav ul li{width: 40px; display: inline-block; font-size: 16px; font-weight: normal; color: #666; line-height: 40px;}
.m_nav ul li a{background: none; padding: 0 3px; /*padding-bottom: 9px;*/}
.m_nav ul li.on a{color: #ef5200; border-bottom: 2px solid #ef5200; /*padding-bottom: 9px;*/}
.m_nav a{/* background-size: 139px auto; */z-index: 999;}

/*登录引导框*/
#signGuide{display: none; z-index: 99999; overflow: hidden;}
#signGuide .sibgn{display: block; width: 100%; height: 100%; background:rgba(0, 0, 0,0.6);  position: fixed; top: 0; z-index: 99999; overflow: hidden;}
#signGuide .signGuide{position: fixed;top: 25%;left: 10%;max-width: 640px;width: 80%;background: #fff;z-index: 99999;border-radius: 10px; overflow: hidden;}
#signGuide .signGuide .signClose{position: absolute; top: 15px; right: 15px; width: 20px; cursor: pointer;}
#signGuide .signGuide span{display: block; color: #a86e63; font-size: 14px;}
#signGuide .signGuide span:nth-child(3){padding: 1% 12%;}
#signGuide .signGuide span i{display: block; font-size: 16px; font-style: normal; margin-bottom: 5px;}
#signGuide .signGuide span b{margin-right: 5px;}
#signGuide .signGuide span img{display: block; width: 100%;}
#signGuide .signGuide a{display: block; width: 80%; margin: 15px auto; line-height: 40px; background: #ff5200; border-radius: 5px; text-align: center; color: #fff;}

/*顶部APP下载*/
#apps{display: none; width: 100%; background: #f8f8f8; overflow: hidden;}
#apps .apps{width: 90%; margin: 3% auto; padding: 2% 3%; background: #fff; border: 1px solid #fff; background: #ffffff; box-shadow: 1px 3px 5px rgba(12,2,5,.07); border-radius: 10px; overflow: hidden;}
#apps .apps em{display: block; float: left; width: 42px; height: 42px; font-size: 0px;  background: url(https://static.fangxiaoer.com/m/static/images/home/<USER>
#apps .apps span{display: block; float: left; font-size: 15px; margin-left: 2%; line-height: 22px;}
#apps .apps span i{display: block; font-size: 13px; color: #888a92; font-style: normal;}
#apps .apps a{display: block; float: right; width: 22%; font-size: 14px; background: #ff5200; color: #fff; line-height: 30px; text-align: center; border-radius: 5px; margin-top: 8px;}
@media screen and (max-width:320px) {
    #apps .apps span{font-size: 12px;}
    #apps .apps span i{font-size: 12px;}
    #apps .apps a{font-size: 12px;}
}
/*二手房详情小区问答*/
.plotAsk{
    background:  #fff;
    margin-top: 10px;
    padding-bottom: 20px;
}
.plotAsk ul{
    width: 96%;
    margin: 0 auto;
}
.plotAsk ul li{
    overflow:  hidden;
    line-height: 18pt;
    color:  #999;
    font-size: 9pt;
}
.plotAsk ul li .Q-A{
    overflow:  hidden;
    font-size:  10pt;
    float:  left;
    width: 86%;
}
.plotAsk ul li h4{
    overflow:  hidden;
    text-overflow:  ellipsis;
    white-space: pre;
    width: 94%;
    font-weight:  normal;
    color:  #333;
    background: url(https://static.fangxiaoer.com/m/images/sale/villageQaIcon1.jpg) no-repeat 0px 4px;
    background-size:  18px 18px;
    padding-left: 22px;
}
.plotAsk ul li .iconQ-A{color:#999}
.remind{ background-color:#f6f6f6}
.remind h1{font-size:13px;color:#4b78b8;width:91px;margin:0 auto;height:35px;line-height:35px;padding-left: 25px;background: url(https://static.fangxiaoer.com/m/static/images/Reversion_wei/remind.png);background-repeat: no-repeat;background-size:  16px auto;background-position: left center;}

.home-device{position:relative}
.Advertisement{position:absolute;right: 5px;bottom: 7px;z-index:99;background-color:rgba(0,0,0,0.4);}
.Advertisement h1{font-size:12px;color: #fff;padding-left:  5px;padding-right:  5px;line-height: 20px;font-weight:  normal;}



/* 房源详情头部 改为透明板2018-10-22 */
.scrollShow {
    display: block;}
.house_information{
    height: 43px;
    background: #f6f6f600;
    display: block;
    position: fixed;
    top: 0;
    max-width: 640px;
    z-index: 99999;
    width:  100%;
}
.house_information .m_nav{display: block;position: fixed;top: 0;max-width: 640px;z-index: 99999;background: none;}
.scrollShow{
    /* display: none; */
}
.house_information .scrollShowBlock{
    display:  block;
    background: #F6F6F6;
    height:  43px;
}
.house_information .scrollShowBlock span{
    display:  block;
    overflow:  hidden;
    height: 39px;
    float:  left;
    width: 39px;
    margin-left: 40%;
    margin-top: 2px;
}
.house_information .scrollShowBlock span+span{
    margin-left: 10px;
    display: inline-block;
    height: 39px;
    float: left;
    width: 39px;
    margin-top:  2px;
}
.house_information .scrollShowBlock span+s span{margin-left: 10px;}
/*.scrollShow .pictures,.scrollShow .videos{display: none;}*/
.house_information .scrollShowBlock .videos{}
.house_information .scrollShowBlock .pictures{
    /* margin-left: 10px; */
    display:  block;
}
.house_information .scrollShowBlock span a{
    display:  block;
    width:  100%;
    position: relative;
    height:  100%;
}
.house_information .scrollShowBlock span a img{
    width:  100%;
    height:  100%;
}

.house_information .scrollShowBlock span a #prismBigPlayIcon{
    width: 20px;
    height: 20px;
    margin-left: -10px;
    margin-top: -10px;
}
.house_information .m_nav a{
    /* background: url(/images/new/navScrollShow.png); */
    width: 30px;
    height: 30px;
    display:  block;
}
.house_information .m_nav .return{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_11.png) center;
    background-size:100%;
    background-position: 0 0 !important;
    background-size:  100% 100%;
    top: 6px;
}
.house_information .m_nav .afavorite{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_22.png) center;
    width: 30px;
    height: 30px;
    display: block;
    background-size:  100% 100%;
    background-position: 0 0 !important;
    right: 80px;
    top: 6px;
}
.house_information .m_nav .sharebtn{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_33.png) center;
    display: block;
    background-size:  100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .m_nav .cfavorite {
    position: absolute;
    right: 81px;
    cursor: pointer;
    float: right;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_22Hover.png) no-repeat;
    width: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}
.house_information .m_nav .catalog{
    width: 20px;
    height: 18px;
    /* background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_44.png) center no-repeat; */
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 12px;
}
.house_information .scrollShowBlock .return{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_1.png) center;
    background-position: 0 0 !important;
    background-size: 100% 100%;
    top: 6px;
}
.house_information .scrollShowBlock .afavorite{
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_2.png) center;
    widthh: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    right: 80px;
    top: 6px;
}
.house_information .scrollShowBlock .sharebtn{
    width: 30px;
    height: 30px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_3.png) center;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
    position: fixed;
}
.house_information .scrollShowBlock .catalog{
    width: 20px;
    height: 18px;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_4.png) center no-repeat;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 12px;
}

.house_information .scrollShowBlock  .cfavorite {
    position: absolute;
    right: 81px;
    cursor: pointer;
    float: right;
    background: url(https://static.fangxiaoer.com/m/static/images/new/navScrollShow_2Hover.png) no-repeat;
    width: 30px;
    height: 30px;
    display: block;
    background-size: 100% 100%;
    background-position: 0 0 !important;
    top: 6px;
}

.no_pl{
    text-align: center !important;
    padding-top: 50% !important;
    font-size: 10pt !important;
    color: #999898;
    margin: 0 !important;
    width: 100%;
}
.no_pl img{
    width: 80%;
    margin-bottom: 23pt;
    margin-top: -40px;
}
.no_pl p{}

/*二手房相关各列表竞价置顶标签*/
.icon_toTop{
    position: absolute;
    left: 10px;
    top: 10px;
    display: block;
    width: 20px;
    height: 22.5px;
    background: url(https://static.fangxiaoer.com/m/images/icon_toTop.png) top center no-repeat;
    background-size: 100% 100%;
    z-index: 9;
}
.icon_biddPrice{
    position: absolute;
    left: 10px;
    top: 10px;
    display: block;
    width: 20px;
    height: 22.5px;
    background: url(https://static.fangxiaoer.com/m/images/icon_biddPrice.png) top center no-repeat;
    background-size: 100% 100%;
    z-index: 9;
}
.title,.right_info{font-weight:normal}

/* 二手房相关视频，全景图标 */
.listIconOne{}
.uhouselist .listIconTwo{
    left: 18px;
    top: 28px;
}
.subHousesMain .listIconTwo{
    left: 18px;
    top: 28px;}
.listIconTwo{
    position: absolute;
    left: 26px;
    top: 36px;
    width: 72px;
    overflow: hidden;
}
.ListIconVr{
    background: url(https://static.fangxiaoer.com/m/images/ListIconVr.png);
    width: 28px;
    height: 28px;
    display: block;
    position: absolute;
    left: 49px;
    top: 36px;
    background-size: 100% 100%;
}
.ListIconVideo{
    background: url(https://static.fangxiaoer.com/m/images/ListIconVideo.png);
    width: 28px;
    height: 28px;
    display: block;
    position: absolute;
    left: 49px;
    top: 36px;
    background-size: 100% 100%;
}
.listIconOne .ListIconVr{}
.listIconOne .ListIconVideo{}
.listIconTwo .ListIconVr{
    position: initial;
    float: left;
}
.listIconTwo .ListIconVideo{
    position: initial;
    float: left;
    margin-left: 15px;
}

/* 二手房相关详情页 */
#stateShiftBtn{
    position: absolute;
    bottom: 10px;
    overflow: hidden;
    z-index: 99;
    left: 50%;
    margin-left: -72px;
    display: none;
}
#stateShiftBtn div{
    float: left;
    width: 40px;
    background: rgba(255, 255, 255, 0.7);
    border-radius: 20px;
    text-align: center;
    font-size: 10px;
    line-height: 20px;
    color: #222;
    margin: 0 4px;
}
.stateShiftBtnBtn3{
    margin-left: -75px !important;
}
.stateShiftBtnBtn2{
    margin-left: -48px !important;
}
#stateShiftBtn div.hover{
    background:#ff5200;
    background-image: linear-gradient(90deg, #ffa81f,#ff5300);
-webkit-linear-gradient(90deg, #ffa81f,#ff5300);
    color:#fff
}
.swImgNumShow{/* display: block !important; */}
.swImgNumHide{/* display: none !important; */}
#fxeHouseVideoIcon{
    position: absolute;
    top: 50%;
    width: 80px;
    left: 50%;
    margin-left: -40px;
    height: 80px;
    margin-top: -55px;
}


/* 2020/1/16 添加服务协议与隐私政策*/
.checkagreeInput{
    display: -webkit-box;
    margin-top: 10px;
    font-size: 12px;
    width: 96%;
    margin: 10px auto 0 auto;}
.checkagreeInput .checked{background: url(https://static.fangxiaoer.com/m/static/images/checkimgChecked.png) top center !important;background-size: 100% 100% !important;}
.checkagreeInput .checkimg {
    display: block;
    background-size: 12px 12px;
    width: 14px;
    height: 14px;
    border-radius: 12px;
    margin-top: 3px;
    margin-right: 6px;
    cursor: pointer;
    background: url(https://static.fangxiaoer.com/m/static/images/checkimgYuan.png) top center;
    background-size: 100% 100%;
}
.checkagreeInput div{font-size: 12px;color: #999; -webkit-box-flex: 1;line-height: 18px;}
.checkagreeInput a{color: #8099af;text-decoration: none;display: inline;}

/* 2020/9/1 搜索框改版*/
/* 二手房，租房，商铺，写字楼列表页内搜索 */
.new-Search-Box-list{
    height: 35px;
    padding: 4px 0;
}
.new-Search-Inner-list{overflow: hidden;}
.new-Search-Ico-list{
    width: 14px;
    height: 12px;
    background: url(https://static.fangxiaoer.com/m/static/images/secicon/search.png) no-repeat;
    background-size: 100% 100% !important;
    left: 11px;
    position: absolute;
    top: 12px;
}
.new-Search-Ico-out{
    width: 14px;
    height: 12px;
    background: url(https://static.fangxiaoer.com/m/static/images/secicon/search.png) no-repeat;
    background-size: 100% 100% !important;
    left: 29px;
    position: absolute;
    top: 12px;
}
.new-Search-village{
    display: inline-block !important;
    background: #fff !important;
    position: relative;
    margin: 0 !important;
    float: left;
    height: 35px;
    line-height: 35px;
    position: relative;
    overflow: hidden;
    float: left;
    margin-left: 24px !important;
    padding-left: 20px;
}
.new-Search-Href-list{
    display: inline-block !important;
    background: #fff !important;
    position: relative;
    margin: 0 !important;
    float: left;
    margin-left: 40px !important;
    /* width: 59% !important; */
    height: 37px;
    line-height: 35px;
}
.new-Search-out-list{
    display: inline-block !important;
    background: #fff !important;
    position: relative;
    margin: 0 !important;
    float: left;
    /* width: 59% !important; */
    height: 35px;
    line-height: 35px;
    padding-left: 18px;
    position: relative;
    overflow: hidden;
    float: left;
}
.new-Search-input-list{
    background: #f5f5f5;
    display: inline-block;
    height: 35px;
    border-radius: 4px;
    padding-left: 12%;
    /* width: 86% !important; */
    padding-right: 8px;
    font-size: 14px;
    outline: none;
}
.new-Search-input-list-w100{
    background: #f5f5f5;
    display: inline-block;
    height: 35px;
    border-radius: 4px;
    padding-left: 30px;
    border: none;
    float: left;
    font-size: 14px;
    outline: none;
    padding-right: 8px;
}

.new-Search-Close-list{display: none;}
.new-Search-toMap-list{
    background: none !important;
    display: inline-block;
    float: left;
    margin-left: 16px;
    line-height: 18px;
    margin-top: 8.5px;
    overflow: hidden;
}
.new-Search-toMap-list p{
    display: inline-block;
    line-height: 18px;
    font-size: 15px;
}
.new-Search-toMap-list .toMap-ico{
    background: url(http://static.fangxiaoer.com/m/static/images/newSearch/btn_map.png) no-repeat;
    background-size: 100% 100%;
    width: 15.5px;
    height: 18px;
    display: inline-block;
    float: left;
    margin-right: 6px;
}
.new-back-Ico-out{
    color: #404040;
    font-size: 15px;
    float: left;
    line-height: 35px;
    margin-left: 15px;
    cursor: pointer;
    background: none !important;
}
.new-Return{
    width: 10px;
    height: 17.5px;
    background: url(http://static.fangxiaoer.com/m/static/images/newSearch/btn_return.png) no-repeat !important;
    background-size: 100% 100% !important;
    left: 15px;
    display: inline-block;
    position: absolute;
    top: 13px;
}
.new-Catalog{
    background: url(http://static.fangxiaoer.com/m/static/images/newSearch/btn_more.png) no-repeat !important;
    background-size: 100% 100% !important;
    display: block;
    position: absolute;
    right: 15px;
    top: 17px;
    width: 18px;
    height: 11.5px;
    overflow: hidden;
}


/* 2020-11-11 全站导航重构 */
/* 房源详情页- 默认显示*/
.headPng-nav{
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 44px;
    z-index: 99999;
    text-align: center;
    line-height: 45px;
    font-size: 15px;
}
.head-detail{
    height: 45px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/img_zz.png) top center;
    background-size: 100% 100%;
}
.headPng-right{
    float: right;
}
.headPng-return{
    display: block;
    width: 20px;
    height: 20px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_xq_nav_btn_rtn.png) top center;
    background-size: 100% 100%;
    float: left;
    margin: 12px 0 0 10px;
}
.headPng-collect{
    display: block;
    width: 20px;
    height: 20px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_zx_nav_btn_sc.png) top center;
    background-size: 100% 100%;
    float: left;
    margin: 12px 16.5px 0 0;
}
.headPng-catalog{
    display: block;
    width: 20px;
    height: 20px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_zx_nav_btn_more.png) top center;
    background-size: 100% 100%;
    float: left;
    margin: 12px 13.5px 0 0;
}
.headPng-share{
    display: block;
    width: 20px;
    height: 20px;
    float: left;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_zx_nav_btn_share.png) top center;
    background-size: 100% 100%;
    margin: 12px 17.5px 0 0;
}

/* 房源详情页-下滑一屏后显示 */
.scrollShowHead{
    background: #F6F6F6;
}
.scrollShowHead .headPng-return{
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
    background-size: 100% 100%;
}
.scrollShowHead .headPng-collect{
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_sc.png) top center;
    background-size: 100% 100%;
}
.scrollShowHead .headPng-catalog{
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_more.png) top center;
    background-size: 100% 100%;
}
.scrollShowHead .headPng-share{
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_share.png) top center;
    background-size: 100% 100%;
}
.headPng-collect.hover{
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_zx_nav_btn_ysc.png) top center;
    background-size: 100% 100%;
}
.scrollShowHeadList{
    background: #F6F6F6;
    position: fixed !important;
    top: 0;
}
/* 房源列表页-默认显示 */
.headJpg-nav{
    width: 100%;
    height: 43px;
    background-color: #fff;
    color: #000;
    text-align: center;
    font-size: 15px;
    line-height: 43px;
    z-index: 99;
    position: relative;
}
.headJpg-return{
    display: block;
    width: 20px;
    height: 20px;
    float: left;
    margin: 12px 0 0 10px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
    background-size: 100% 100%;
}
.headJpg-catalog{
    display: block;
    width: 20px;
    height: 20px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_more.png) top center;
    background-size: 100% 100%;
    position: absolute;
    right: 13.5px;
    top: 12px;
}
/* 网站导航弹出页 */
.allTc-nav{
    width: 100%;
    height: 43px;
    background-color: #F6F6F6;
    color: #000;
    text-align: center;
    font-size: 15px;
    line-height: 43px;
}
.allTc-return{
    display: block;
    width: 20px;
    height: 20px;
    float: left;
    margin: 12px 0 0 10px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
    background-size: 100% 100%;
}
.allTc-close{
    display: block;
    width: 20px;
    height: 20px;
    background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_more.png) top center;
    background-size: 100% 100%;
    position: absolute;
    right: 13.5px;
    top: 12px;
}
/* 2021年5月20日 首页导航重构 */
#pageNavigation{
    position: absolute;
    top: 0;
    left: 0;
    z-index: 99999999;
    overflow-y: auto;
    background: #fff;
    padding-bottom: 12%;
    width: 100%;
    height: 100%;
    display: none;
}
#pageNavigation>div{
}
#pageNavigation ul {
    display: block;
    overflow: hidden;
    border-bottom: 1px solid #f4f4f4;
}
#pageNavigation ul li {
    display: block;
    float: left;
    width: 20%;
    text-align: center;
    font-size: 12px;
}
#pageNavigation ul li a {
    display: block;
    padding: 20px 0;
    color: #000;
}
#pageNavigation ul li a i {
    position: relative;
    display: block;
    margin: 0 auto;
    width: 50px;
    height: 50px;
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/page_index_jgq_btn_xf.png) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li a p{
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: bold;
    color: #3E3E3E;
    line-height: 12px;
    margin-top: 10.5px;
}
#pageNavigation .m_sub{
    width: 100%;
    height: 44px;
    background: #FFFFFF;
    line-height: 44px;
    font-size: 15px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #222222;
}
#pageNavigation .m_sub a{
    position: absolute;
    left: 18px;
}
#pageNavigation .m_sub a+a{
    position: absolute;
    left: 50%;
    margin-left: -45px;
}
#pageNavigation ul li+li+li+li+li+li i{
    width: 33px;
    height: 30px;
}
#pageNavigation ul li:nth-child(2) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/page_index_jgq_btn_esf.png) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(3) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/page_index_jgq_btn_zf.png) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(4) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/page_index_jgq_btn_sp.png) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(5) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/page_index_jgq_btn_xzl.png) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(6) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/1.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(7) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_zx.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(8) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_bnzf.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(9) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/4.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(10) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/7.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(11) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/9.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(12) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/5.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(13) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_wycz.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(14) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_spzs.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(15) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_xzl.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(16) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/icon-3.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(17) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_ppfq.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(18) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/icon-2.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(19) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/icon-1.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(20) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_jsq.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(21) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/icon-4.jpg) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation ul li:nth-child(22) i{
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/page_index_jgq_btn_xf.png) no-repeat;
    background-size: 100% 100%;
}
#pageNavigation .m_sub .pageNavigationClose{
    width: 20px;
    height: 20px;
    background: url(https://static.fangxiaoer.com/m/static/images/Navigation/btn_sqjt.png) no-repeat;
    background-size: 100% 100%;
    left: auto;
    right: 15px;
    top: 14.5px;
    margin-left: 0;
}
.Navigationbottom{
}
.Navigationbottom img{
    width: 60px;
    height: 60px;
    border-radius: 15px;
    display: block;
    margin: 32.5px auto 13.5px auto;
}
.Navigationbottom p{
    text-align: center;
    font-size: 14px;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #222222;
    line-height: 13px;
}
.Navigationbottom p+p{
    margin-top: 6.5px;
}
.Navigationbottom .NavigationbottomBtn{
    width: 80%;
    height: 45px;
    background: #FF6F28;
    border-radius: 5px;
    display: block;
    text-align: center;
    line-height: 45px;
    color: #fff;
    font-size: 15px;
    margin: 31.5px auto 39.5px auto;
    font-weight: 400;
}