//创建时间：2017-04-26
//更新时间:
//用于复杂页面的常用验证
//作者:张凯情
//fxe_alert("提示语")非阻塞式提示
//正确返回true  错误返回错误信息

var sy_hint = ["账户名与密码不匹配，请重新输入!",//登录  0
			 "请输入账户名!",//1
			 "请输入密码!",//2
			 "请输入账户名和密码!",//3
			 "请输入你的手机号码!",//4
			 "手机号码格式不正确，请重新输入!",//
			 "请输入验证码!",//6
			 "验证码错误!",//7
			 "手机号格式不正确!",//  8
			 "该账户不存在，请请核对后重新输入。",//  9
			 "密码确认不正确!",//10
			 "密码格式不正确!",//11
			 "推荐人与用户名不能重复!",//12
			 "该账户不存在，请核对后重新输入。",//13
			 "推荐人手机号格式不正确!",//14
			 "该手机号已注册!",//15
			 "原始密码错误",//16
			 "请输入手机号码",//17
			 "请输入原始密码!",//18
 			 "密码长度6-20位，必须由数字和字母组成，可以包含下划线“_”"//19
 			 ]
//弹出
function fxe_alert(text) {
	$("body").append("<div class='fxe-alert'>" + text + "</div>");
	setTimeout("del()", 3000);
}
function del() {
	var time = 400;
	$(".fxe-alert").eq(0).hide(time);
	setTimeout("$('.fxe-alert').eq(0).remove()", time)
}
var sy_confirm = {
//	sessionId:$("#fxrSessionId").val(),
	//初始化  int =1 弹出提示并返回   int=2 只做返回  alert 失去焦点的时候是否做提示
	init:function(int,alert){
	    sy_confirm.type = int
		if(alert){
			$("#phone").blur(function(){
			    sy_confirm.phone($("#phone").val())
			})
			$("#code").blur(function(){
			    sy_confirm.code($("#code").val())
			})
			$("#pwd").blur(function(){
			    sy_confirm.password($("#pwd").val())
			})
			$("#repwd").blur(function(){
				if($("#pwd").val()!=$("#repwd").val()){
					layer.msg("两次输入的密码不一致")
				}
			})
		}
		//app隐藏头
		var txt = window.location.href;
		if(txt.indexOf("?") > 0){
			txt = txt.split("?")[1].split("&")
			for (var i = 0;i<txt.length;i++){
				var num = txt[i].split("jty=").length;
				if(num>1){
					if(txt[i].split("jty=")[1]==1){
						$(".m_top").hide();
						$(".time").css("top","0");
						break;
					}
				}
			}
		}
	},
	type:1,
	//是否登录
	isLogin:function(){
		var msg;
		if($("#fxrSessionId").val()==""){
			msg==false;
		}else{
			msg==true;
		}
	},
	//登录
	login:function(phone,code){
		var msg="";
		if(sy_confirm.phone(phone)!=true){
			msg=sy_confirm.phone(phone)
		}else if(code==""){
			sy_confirm.type == 1 ? layer.msg("请输入验证码"):""
			msg="请输入验证码"
		}else if(code.length!=6){
			sy_confirm.type == 1 ? layer.msg("验证码错误"):""
			msg="验证码错误"
		}else{
			 $.ajax({
                type: "POST",
                url:'/fxrLogin/',
                data:$('#login').serialize(),// 你的formid
                async: false,
                error: function(request) {
                    sy_confirm.type == 1 ? layer.msg("登录失败"):""
                    msg="登录失败"   ;
                    setCookie("eventLogIn","true");
		                	window.location.reload();
                },
                success: function(data) {
                	data=jQuery.parseJSON(data)
                	if(data.status==1){
                		if(data.msg=="success"){
		                	msg=true;
	                		setCookie("eventLogIn","true");
		                	window.location.reload();
                		}
                	}else{
                		msg=data.msg
                		sy_confirm.type == 1 ? layer.msg(data.msg):""
                	}
                }
           });	
		}
		return msg;
	},
	//退出登录
	logOut:function(){
		 $.ajax({
                type: "GET",
                url:'/fxrLogout/',
                async: false,
                error: function(request) {
                    alert("登出失败");
                },
                success: function(data) {
                    window.location.reload();
                }
            });
	},
	//判断是否是微信  如果不是提示  如果是 可以使用微信支付
	isWeiXin: function() {
		var ua = window.navigator.userAgent.toLowerCase();
		if(ua.match(/MicroMessenger/i) == 'micromessenger') {
			msg=true
		} else {
			msg=false
		}
		return msg;
	},
	//手机号格式
	phone: function(phone) {
		var msg = false;
		var reg = /^1[3,4,5,7,8]{1}[0-9]{1}[0-9]{8}$/;
		if(phone == "") {
		    msg = sy_hint[17];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[17]) : ""
		} else if(!reg.test(phone)) {
		    msg = sy_hint[8];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[8]) : ""
		} else {
			msg =  true;
		}
		return msg;
	},
	//密码格式验证
	password:function(pas){
		var msg = false;
        var regp = /(?:\d.*_)|(?:_.*\d)|(?:[A-Za-z].*_)|(?:_.*[A-Za-z])|(?:[A-Za-z].*\d)|(?:\d.*[A-Za-z])/
		if (pas== "") {
		    msg = sy_hint[2];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[2]) : ""
		} else if (!regp.test(pas)) {
		    msg = sy_hint[19];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[19]) : ""
		} else if (pas.length < 4 || pas.length > 20) {//4-20个字符
		    msg = sy_hint[11];
		    sy_confirm.type == 1 ? fxe_alert(sy_hint[11]) : ""
		} else {
			msg = true;
		}
        return msg;
	},
	//密码 只验证位数 不验证规则
	Password:function(pas){
		var msg = false;
		if (pas== "") {
		    msg = sy_hint[18];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[18]) : ""
		} else if (pas.length < 4 || pas.length > 20) {//4-20个字符
		    msg = sy_hint[0];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[0]) : ""
		} else {
			msg = true;
		}
        return msg;
	},
	//旧验证码验证（只验证位数 不验证规则）
	oldPassword:function(pas){
		var msg = false;
		if (pas== "") {
		    msg = sy_hint[18];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[18]) : ""
		} else if (pas.length < 4 || pas.length > 20) {//4-20个字符
		    msg = sy_hint[16];
		    sy_confirm.type == 1 ? layer.msg(sy_hint[16]) : ""
		} else {
			msg = true;
		}
        return msg;
	},
	code:function(code){
		var msg = false;
		if(code.length==0){
		    msg = sy_hint[6]
		    sy_confirm.type == 1 ? layer.msg(sy_hint[6]) : ""
		}else if(code.length!=6){
		    msg = sy_hint[7]
		    sy_confirm.type == 1 ? layer.msg(sy_hint[7]) : ""
		}else{
			msg=true;
		}
		return msg;
},
	Code: function(phone) {//获取验证码  获取成功返回1
		try {
			var r = 0;
			$.ajax({
				type: "POST",
				data: {
					mobile: phone
				},
				url: "/smscode/",
				async: false,
				success: function(data) {
					data = jQuery.parseJSON(data)
					if(data.status == 0) {
						r=data.msg
		    			layer.msg(data.msg)
					} else if(data.status==1){
						r=true;
					}else{
						r = data.status;
					}
				},
				error: function(e) {
					alert(e);
				}
			});
		} catch(e) {
			console.log(e.message);
		}
		return r;
	},
	//需求定制     电话  验证码  区域 价格 户型 面积 备注 类型（1-新房/2-二手房/3-租房）
	xuqiu:function(phone,code,region,budget,housetype,area,italy,type){
		var msg = false;
		var txt = window.location.href;
		//txt = txt.split("?ly=")[1]==undefined?"":txt.split("?ly=")[1];
		if(txt.indexOf("?") > 0){
			txt = txt.split("?")[1].split("&")
			for (var i = 0;i<txt.length;i++){
				var num = txt[i].split("ly=").length;
				if(num>1){
					txt=txt[i].split("ly=")[1];
					italy+="     来源："+txt
					break;
				}
			}
		}
		$.ajax({
			type:"post",
			url:"/apis/",
			async:false,
			data:{url:"https://ltapi.fangxiaoer.com/apiv1/active/saveGuide", phone:phone,code:code,region:region,budget:budget,housetype:housetype,area:area,italy:italy,type:type},
			success:function(data){
				data = jQuery.parseJSON(data)
				if(data.status  == 1) {
					msg = true;
					data=data.content
					sy_confirm.sessionId=data.sessionId;
				}else{
					msg=data.msg=="您近期已经参加过此活动"?"您近期已经参加过此活动":sy_hint[7]
				}
			},error:function(data){
				msg =data
			}
		});
		return msg;
	},
	
//	confirmCode: function (phone,code) {
//	    var msg = false;
//	    $.ajax({
//	        type: "POST",
//	        async: false,
//	        data: { action: "CheckSend", mobile: phone, code: code },
//	        url: "/Action/SendsmsHelp.ashx",
//	        success: function (data) {
//	            if (data == "1") {
//	                msg = true;
//	            }
//	            else {
//	                msg = sy_hint[7]
//	                sy_confirm.type == 1 ? fxe_alert(sy_hint[7]) : ""
//	            }
//	        },
//	        error: function (error) {
//	            console.log(error);
//	            msg = "服务器繁忙请稍后重试"
//	            sy_confirm.type == 1 ? fxe_alert("服务器繁忙请稍后重试") : ""
//	        }
//	    });
//	    return msg;
//	},
//是否是会员
	isVIP:function(phone){
		var msg=false
		$.ajax({
			type:"post",
			url:"https://ltapi.fangxiaoer.com/apiv1/base/checkIsMember",
			async:false,
			data:{mobile:phone},
			success:function(data){
				if(data.status==1){
					msg=true
					msg = sy_hint[15];
					sy_confirm.type == 1 ? fxe_alert(sy_hint[15]) : ""
				}
			}
		});
		
		return msg;
	},
	wait: 60,//倒计时
	timeWait: function() {//倒计时函数
	    sy_confirm.wait = 60;
	    sy_confirm.time();
		
	},
	time:function () {
	    if (sy_confirm.wait == 0) {
				$(".fxe_validateCode").hide();
				$(".fxe_validateCode0").hide();
				$("#event_xqdz_validateCode").hide();
				$("#event_xqdz_syValidateCode").hide();
				$(".fxe_ReSendValidateCoad").show().html("重新获取");
				$(".fxe_ReSendValidateCoad0").show().html("重新获取");
				$("#event_xqdz_ReSendValidateCoad").show().html("重新获取");
				$("#event_xqdz_syReSendValidateCoad").show().html("获取");
				sy_confirm.wait = 60;
			} else {
	        $(".fxe_validateCode").show().html("在" + sy_confirm.wait + "秒后重发");
	        $(".fxe_validateCode0").show().html("在" + sy_confirm.wait + "秒后重发");
	        $("#event_xqdz_validateCode").show().html("在" + sy_confirm.wait + "秒后重发");
	        $("#event_xqdz_syValidateCode").show().html(sy_confirm.wait +"S");
				$(".fxe_ReSendValidateCoad").hide();
				$(".fxe_ReSendValidateCoad0").hide();
				$("#event_xqdz_ReSendValidateCoad").hide();
				$("#event_xqdz_syReSendValidateCoad").hide();
				sy_confirm.wait--;
				setTimeout("sy_confirm.time()", 1000);
			}
		},
		event_xqdz:function(data){
			var event_xqdz="<dl class='event_xqdz_footer'><dt><p>"+data.info+data.name+"</p><p>************转"+data.phone+"</p></dt><dd id='event_xqdz' style='background: url(../static/images/page_zt_img_bnzf.png) no-repeat #ff5200 11px -1px;    background-size: 38px;'>帮您找房</dd><a href='tel:4006322002,"+data.phone+"'><dd style='background: url(../static/images/event_xqdz_phone.png) no-repeat #ff5200  19px 7px;background-size: 23px;'>打电话</dd></a></dl>"
			var event_xqdz_select="<div class='event_xqdz'><ul class='event_xqdz_select'><li class='select'><span>区&emsp;域</span><p id='event_xqdz_area'>请选择区域</p></li><li class='select'><span>价&emsp;格</span><p id='event_xqdz_price'>请选择价格</p></li><li><span>手机号</span><input placeholder='请输入手机号码' type='text'  id='event_xqdz_phone'  maxlength='11'/></li><li><span>验证码</span><input type='text' id='event_xqdz_code' style='width: 44%;'  maxlength='6' placeholder='请输入验证码' /><b id='event_xqdz_ReSendValidateCoad'>获取验证码</b><b id='event_xqdz_validateCode'></b></li><li style='padding: 10px 0;margin: 0;'><a>帮您找房</a></li></ul><ul class='event_xqdz_area'><li>沈河区</li><li>大东区</li><li>皇姑区</li><li>和平区</li><li>铁西区</li><li>于洪区</li><li>浑南区</li><li>沈北新区</li><li>苏家屯</li></ul><ul class='event_xqdz_price'><li>35万以下</li><li>35—50万</li><li>50—80万</li><li>80—100万</li><li>100—120万</li><li>120万以上</li></ul></div>"
			$("body").append(event_xqdz);
			$("body").css("padding-bottom","54px")
			$("body").append(event_xqdz_select);
			$("#event_xqdz").bind("click",function(){
				$(".event_xqdz>ul").hide()
				$(".event_xqdz").show()
				$(".event_xqdz .event_xqdz_select").show()
			})
			$(".event_xqdz").bind("click",function(e){
				var target = $(e.target);
				if(target.closest(".event_xqdz ul").length<1){
					$(".event_xqdz").hide()
				}
			})
			$("#event_xqdz_area").parent().bind("click",function(){
				$(".event_xqdz>ul").hide()
				$(".event_xqdz").show()
				$(".event_xqdz_area").show()
			})
			$("#event_xqdz_price").parent().bind("click",function(){
				$(".event_xqdz>ul").hide()
				$(".event_xqdz").show()
				$(".event_xqdz_price").show()
			})
			$(".event_xqdz_area li").bind("click",function(){
				$("#event_xqdz_area").text($(this).text());
				$(".event_xqdz>ul").hide()
				$(".event_xqdz").show()
				$(".event_xqdz .event_xqdz_select").show()
			})
			$(".event_xqdz_price li").bind("click",function(){
				$("#event_xqdz_price").text($(this).text());
				$(".event_xqdz>ul").hide()
				$(".event_xqdz").show()
				$(".event_xqdz .event_xqdz_select").show()
			})
			$("#event_xqdz_ReSendValidateCoad").bind("click",function(){
				if(sy_confirm.phone($("#event_xqdz_phone").val())!=true){
				}else if(sy_confirm.Code($("#event_xqdz_phone").val())!=true){
					
				}else{
					sy_confirm.time()
				}
			})
			$(".event_xqdz_select a").bind("click",function(){
				if($("#event_xqdz_area").text()==""||$("#event_xqdz_area").text()=="请选择区域"){
					layer.msg("请选择区域")
				}else if($("#event_xqdz_price").text()==""||$("#event_xqdz_price").text()=="请选择价格"){
					layer.msg("请选择价格")
				}else if($("#event_xqdz_code").val()==""){
					layer.msg("请输入验证码")
				}else if(sy_confirm.phone($("#event_xqdz_phone").val())!=true){
				}else if(sy_confirm.xuqiu($("#event_xqdz_phone").val(),$("#event_xqdz_code").val(),$("#event_xqdz_area").text(),$("#event_xqdz_price").text(),"无","无","此订单来自于专题“"+data.title+"”  姓名："+data.name+"    电话：************转"+data.phone,1)==true){
					$(".event_xqdz").hide();
					layer.msg("提交成功")
				}
			})
			
				//需求定制     电话  验证码  区域 价格 户型 面积 备注 类型（1-新房/2-二手房/3-租房）
	
			
		},
		event_qxdz_sy:function(data){
//			(function (m, ei, q, i, a, j, s) {
//		        m[i] = m[i] || function () {
//		            (m[i].a = m[i].a || []).push(arguments)
//		        };
//		        j = ei.createElement(q),
//		            s = ei.getElementsByTagName(q)[0];
//		        j.async = true;
//		        j.charset = 'UTF-8';
//		        j.src = '//static.meiqia.com/dist/meiqia.js';
//		        s.parentNode.insertBefore(j, s);
//		    })(window, document, 'script', '_MEIQIA');
//		    _MEIQIA('entId', 33615);
//		    _MEIQIA('assign', {
//		        agentToken: 'ea33ee0dd925ff4989a620824573c909'
//		    });
		    
		    
//			 (function(m, ei, q, i, a, j, s) {
//			        m[i] = m[i] || function() {
//			            (m[i].a = m[i].a || []).push(arguments)
//			        };
//			        j = ei.createElement(q),
//			            s = ei.getElementsByTagName(q)[0];
//			        j.async = true;
//			        j.charset = 'UTF-8';
//			        j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';;
//			        s.parentNode.insertBefore(j, s);
//			    })(window, document, 'script', '_MEIQIA');
//			    _MEIQIA('entId', 33615);
			 (function(m, ei, q, i, a, j, s) {
			        m[i] = m[i] || function() {
			            (m[i].a = m[i].a || []).push(arguments)
			        };
			        j = ei.createElement(q),
			            s = ei.getElementsByTagName(q)[0];
			        j.async = true;
			        j.charset = 'UTF-8';
			        j.src = 'https://static.meiqia.com/dist/meiqia.js?_=t';
			        s.parentNode.insertBefore(j, s);
			    })(window, document, 'script', '_MEIQIA');
			    _MEIQIA('entId', 33615);
//			    _MEIQIA('manualInit');
 				_MEIQIA('assign', {
			        agentToken: 'ea33ee0dd925ff4989a620824573c909'
			    });
		    var event_xqdz="<dl class='event_xqdz_sy'><dt style='background:url(https://event.fangxiaoer.com/static/images/event_xqdz_bag.png) no-repeat bottom left "+data.color+";'><div><h1>"+data.name+"</h1><p>************转"+data.phone+"</p><img src='https://event.fangxiaoer.com/static/images/event_xqdz_x.png'/></div></dt><dd><select id='event_xqdz_syArea'><option>沈河区</option><option>大东区</option><option>皇姑区</option><option>和平区</option><option>铁西区</option><option>于洪区</option><option>浑南区</option><option>沈北新区</option><option>苏家屯</option></select></dd><dd><select id='event_xqdz_syPrice'><option>35万以下</option><option>35-50万</option><option>50-80万</option><option>80-100万</option><option>100-120万</option><option>120万以上</option></select></dd><dd><input type='text' placeholder='请输入手机号' maxlength='11' id='event_xqdz_syPhone'/></dd><dd><input type='text' placeholder='请输入验证码' maxlength='6' id='event_xqdz_syCode' style='width: 78px;'/><b id='event_xqdz_syReSendValidateCoad'>获取</b><b id='event_xqdz_syValidateCode'></b></dd><dd><a>帮您找房</a></dd><dd style='padding: 0;'><span>客服在线，欢迎咨询!</span></dd></dl><div class='event_xqdz_x' style='background:"+data.color+";'><i></i>立即找房</div>"
		  $("body").append(event_xqdz); 
		  $('.event_xqdz_sy dd span').bind("click",function(){
		  	$("#MEIQIA-BTN").click()
		  })
		  $(".event_xqdz_sy dt div img").bind("click",function(){
		  	$(".event_xqdz_sy").hide(200);
		  	$(".event_xqdz_x").show(200);
		  })
		  $(".event_xqdz_x").bind("click",function(){
		  	$(".event_xqdz_sy").show(200);
		  	$(".event_xqdz_x").hide(200);
		  })
			$("#event_xqdz_syReSendValidateCoad").bind("click",function(){
				if(sy_confirm.phone($("#event_xqdz_syPhone").val())!=true){
				}else if(sy_confirm.Code($("#event_xqdz_syPhone").val())!=true){
				}else{
					sy_confirm.time()
				}
			}) 
		    $(".event_xqdz_sy dd a").bind("click",function(){
				if($("#event_xqdz_syCode").val()==""){
					layer.msg("请输入验证码")
				}else if(sy_confirm.phone($("#event_xqdz_syPhone").val())!=true){
				}else if(sy_confirm.xuqiu($("#event_xqdz_syPhone").val(),$("#event_xqdz_syCode").val(),$("#event_xqdz_syArea").val(),$("#event_xqdz_syPrice").val(),"无","无","此订单来自于专题“"+data.title+"”  姓名："+data.name+"    电话：************转"+data.phone,1)==true){
					$("#event_xqdz_syPhone").val("")
					$("#event_xqdz_syCode").val("")
					layer.msg("提交成功")
				}
			})
		    
		    
		    
		    
		}






}
function setCookie(cname, cvalue) {
    var d = new Date();
    d.setTime(d.getTime() + (1 * 60 * 60 * 1000));
    var expires = "expires=" + d.toGMTString();
    document.cookie = cname + "=" + cvalue + "; " + expires + ";path=/";
}
function getCookie(cname) {
    var name = cname + "=";
    var ca = document.cookie.split(';');
    for (var i = 0; i < ca.length; i++) {
        var c = ca[i].trim();
        if (c.indexOf(name) == 0) return c.substring(name.length, c.length);
    }
    return "";
}