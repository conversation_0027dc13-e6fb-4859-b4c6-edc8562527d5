i, em, b { font-style: normal; }
* { box-sizing: border-box; margin: 0; padding: 0; }
body { background-color: #F8F8F8; max-width: 10rem;min-height: 100vh; padding-bottom: 30px;}
/*去掉默认样式，上边框的阴影，点击时的蒙版*/
input { outline: none; -webkit-appearance: none; -webkit-appearance: none; -webkit-tap-highlight-color: rgba(0, 0, 0, 0); }
input::-webkit-input-placeholder {
    color: #999999;
    opacity:1;
}
.hide{
    display: none !important;
}
h1{
    font-weight: bold;
    font-size: 0.64rem;
    color: #303030;
    font-style: normal;
    text-transform: none;
    text-align: center;
    width: 10rem;
    margin-top: 1.68rem;
    margin-bottom: 0.64rem;
}
.wmain{
    width: 8.29rem;
    margin: auto;
}
.wirteoff{
    width: 100%;
    display: flex;
    justify-content: space-between;
    margin-top: 0.37rem;
    margin-bottom: 0.32rem;
}
.wirteoff input{
    width: 5.47rem;
    height: 1.12rem;
    background: #FFFFFF;
    border-radius: 0.11rem 0.11rem 0.11rem 0.11rem;
    border: 0.03rem solid #999999;
    padding: 0 0.21rem;
}
.wirteoff i{
    display: inline-block;
    font-weight: 400;
    font-size: 0.43rem;
    color: #FFFFFF;
    font-style: normal;
    text-transform: none;
    text-align: center;
    width: 2.51rem;
    line-height: 1.12rem;
    background: #FFAD2B;
    border-radius: 0.11rem 0.11rem 0.11rem 0.11rem;
}
.confirmOff{
    width: 8.29rem;
    line-height: 1.15rem;
    background: #F8481F;
    border-radius: 0;
    text-align: center;
    font-weight: bold;
    font-size: 0.53rem;
    color: #FFFFFF;
    margin-top: 1.07rem;
    margin-bottom: 0.47rem;
    display: none;
}
.footmore{
    font-weight: 400;
    font-size: 0.32rem;
    color: #303030;
    line-height: 0.53rem;
    text-align: left;
    font-style: normal;
    text-transform: none;
    margin-top: 1rem;
}
.infos{
    font-weight: 400;
    font-size: 0.4rem;
    color: #303030;
    display: none;
}
.infos p:nth-child(1){
    /*margin-bottom: 0.27rem;*/
}
.infos-li{
    border-top: 1px solid #999999;
    width: 100%;
    padding: 10px 0;
    font-size: 14px;
    position: relative;
    display: flex;
}
.infos-li:last-child{
    border-bottom: 1px solid #999999;
}
.infos-li i{
    display: inline-block;
    width: 0.37rem;
    height: 0.37rem;
    position: absolute;
    top: 0.2rem;
    right: 0;
    background-image: url(../img/a1.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
}
.infos-num{
    font-size: 0.4rem;
    font-weight: bolder;
    margin-right: 0.2rem;
}
.ivs{
    background-image: url(../img/a2.png) !important;
}
.line{
    text-decoration: line-through;
    color: #777;
}

.fxe_alert span {display: inline-block;background: url(https://static.fangxiaoer.com/web/images/ico/sign/b50.png);padding: 0.1rem 0.35rem;border-radius: 0.5rem;font-size: 12pt;color: #fff;}
.fxe_alert {text-align: center;margin: 0 auto;display: inline-block;width: 6.6rem;position: fixed;top: 6rem;z-index: 999999;left: 50%;margin-left: -3.3rem; display: none;}










