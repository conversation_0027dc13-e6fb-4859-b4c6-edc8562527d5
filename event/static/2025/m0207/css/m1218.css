body{ max-width: 10rem;
    background-image: url('../img/11.jpg'); background-size: 10rem 13.5rem; background-repeat: no-repeat; background-position: center top; height: 100vh;}
.ch<PERSON><PERSON>{ width: 1.3rem;height: 1.3rem;background-image: url(../img/3.png);background-size: 100% 100%;background-repeat: no-repeat;background-position: center;position: absolute;bottom: 8.45rem;left: 4.4rem;z-index: 3;}
.fbt1{ width: 5rem; height: 1.4rem; z-index: 3331; position: absolute; left: 0; right: 0; margin: auto; bottom: 3.3rem;}
.fbt2{ width: 3rem;height: 0.8rem; z-index: 3331; position: absolute;left: 0;right: 0;margin: auto;bottom: 2.4rem;}





/*-------*/
.page{background: #0177d0;padding-bottom: 0.6rem;}
img{
width: 100%;
height: 100%;
}
.cont0{}
.w96{
width: 92%;
margin: 0 auto;
}
.titleImg{
width: 9.2rem;
height: 1.1rem;
margin-bottom: 0.27rem;
}
.cont{margin: 0 auto;padding: 0.32rem 0.26rem;overflow: hidden;margin-bottom: 0.32rem;width: 8.99rem;border-radius:  0 0 0.27rem 0.27rem;margin-top: -0.09rem;background: linear-gradient(184deg, #FFFFFF 0%, #FFFDEC 99%);}
.cont1{
    background: linear-gradient(0deg, #FFFFFF 0%, #FFFDEC 99%);
}
.cont2{
}
.cont3{
    background: linear-gradient(0deg, #FFFFFF 0%, #FFFDEC 99%);
}
.cont4{}
.cont5{
    background: linear-gradient(184deg, #FFFFFF 0%, #FFFDEC 99%);
}
.cont6{
    background: linear-gradient(184deg, #FFFFFF 0%, #FFFDEC 99%);
}
.cont7{
    background: linear-gradient(184deg, #FFFFFF 0%, #FFFDEC 99%);
}
.cont8{}
.cont1{}
.cont1-ul{}
.cont1-ul li:last-child{margin-right:0}
.cont1-li{
float: left;
text-align: center;
margin-right: 0.24rem;
}
.cont1-li div{
width: 1.96rem;
height: 1.47rem;
}
.cont1-li p{
font-family: Alibaba PuHuiTi;
font-weight: 500;
font-size: 0.31rem;
color: #303030;
margin: 0.19rem 0 0.31rem 0;
}
.cont1Swiper{width: 8.53rem;height: 6.4rem;overflow: hidden;position: relative;margin-bottom: 0.2rem;}
.cont1Swiper .swiper-wrapper{}
.video_buttom{background-image: url(https://static.fangxiaoer.com/web/images/ico/sign/hasVideo.png)!important;
;;;;background-size: 100% 100%;;;;;;
;;position: absolute;;;
;;;left: 50%;;;;
;;;top: 50%;;;;
;;;;width: 1rem;;;;;
;;;;height: 1rem;;;;;
;;;;;;;margin-left: -0.5rem;;;;;;;;
;;;;;;;margin-top: -0.5rem;;;;;;;;
}
.swiper-slide{}
.swiper-slide img{
width: 8.75rem !important;
height: 100%;
}
.cont1-L,.cont1-L00{width: 0.4rem !important;height: 0.79rem !important;background: url(../img/13.png) top center rgb(0 0 0 / 50%);background-size: 100% 100%;z-index: 99 !important;}
.cont1-R,.cont1-R00{width: 0.4rem !important;height: 0.79rem !important;background: url(../img/12.png) top center  rgb(0 0 0 / 50%);background-size: 100% 100%;}
.swiper-button-next:after, .swiper-container-rtl .swiper-button-prev:after,.swiper-button-prev:after, .swiper-container-rtl .swiper-button-next:after{
content: '' !important;
}
.cont2 p{width: 8.41rem;font-family: Alibaba PuHuiTi;font-weight: 500;font-size: 0.32rem;color: #303030;line-height: 0.5rem;text-indent: 0.64rem;margin-bottom: 0.2rem;}
.cont3-ul{}
.cont3-ul li:first-child{border-top: none}
.cont3-li{
    border-top: 1px dashed #666;
}
.cont3-li a{width: 8.3rem;display: inline-block;white-space: pre;text-overflow: ellipsis;overflow: hidden;font-family: Source Han Sans CN;font-weight: 500;font-size: 0.36rem;color: #303030;line-height: 0.42rem;line-height: 0.85rem;}
.cont5-ul{
overflow: hidden;
}
.cont5-ul li:nth-child(3n){ margin-right: 0}
.cont5-li{float: left;margin-right: 0.2rem;margin-bottom: 0.16rem;overflow: hidden;}
.cont5-li a{
width: 2.67rem;
height: 0.99rem;
background: #FFFFFF;
border-radius: 0.05rem;
border: 0.01px solid rgba(0,0,0,0.15);
display: block;
text-align: center;
line-height: 0.99rem;
overflow: hidden;
white-space: pre;
text-overflow: ellipsis;
}
.cont5-li img{}
.cont6-ul{}
.cont6-li{
width: 2.64rem;
height: 0.99rem;
background: #FFFFFF;
border-radius: 0.05rem;
border: 0.01px solid rgba(0,0,0,0.15);
float: left;
margin-right: 0.2rem;
margin-bottom: 0.16rem;
}
.tc1-no p{
margin-top: 0.36rem;
height: 0.37rem;
font-size: 0.37rem;
font-family: Source Han Sans CN;
font-weight: 400;
color: #BDC0C5;
line-height: 0.37rem;
text-align: center;
}
.tc1-no{
padding-top: 0.36rem;
width: 7.32rem;
height: 5.03rem;
background: linear-gradient(0deg, #FFF1E3 0%, #FFFFFF 100%);
border-radius: 0.27rem;
margin: 0 auto;
margin-top: 0.83rem;
box-shadow: 2px 2px 2px 0px rgb(113 29 0 / 28%);
}
.tc1-no img{
width: 4.09rem;
height: 3.16rem;
margin: 0 auto;
display: block;
}
.cont6-ul li:nth-child(3n){ margin-right: 0}
/* 转盘 */
.lotteryBox{
background: url(../img/bg1.png) no-repeat;
background-size:100% 100%;
/* height: 7.28rem; */
/* width: 7.7rem; */
display: flex;
align-items:
center;
justify-content:
center;
/* margin: 0 auto; */
width: 7.7rem;
height: 7.28rem;
border-radius: 0.32rem;
padding: 0.27rem;
margin-bottom: 0.41rem;
}

.lotteryBox ul {
display: flex;
flex-wrap:
wrap;
align-items:
center;
justify-content:
center;
padding:.2rem;
background: url(../img/bg2.png) no-repeat;
background-size: 100% 100%;
width: 7.17rem;
height: 6.75rem;
}
.lotteryBox ul li {
position:
relative;
width: 2.14rem;
height: 2rem;
margin-bottom: 0.19rem;
margin-right: .12rem;
}
.resOpportunity {
width:100%;
bottom:11%;
left:50%;
-moz-transform:translate(-36%,0);
-webkit-transform:translate(-36%,0);
-o-transform:translate(-36%,0);
-ms-transform:translate(-36%,0);
transform:translate(-36%,0);
font-size: .22rem;
}
.lotteryBox ul li img{
width:100%;
z-index:2;
}
.lotteryBox ul li:nth-child(3n){
margin-right: 0;
}
.lotteryBox ul li.active .shade{
display: block;
}
.shade{
width:100%;
height: 100%;
position:
absolute;
left:0;
top:0;
background: url(../img/kuang.png) no-repeat;
background-size: 100% 100%;
z-index: 999;
display:
none;
}
.fxe_alert span {display: inline-block;background: url(https://static.fangxiaoer.com/web/images/ico/sign/b50.png);padding: 0.1rem 0.35rem;border-radius: 0.5rem;font-size: 12pt;color: #fff;}
.fxe_alert {text-align: center;margin: 0 auto;display: inline-block;width: 6.6rem;position: fixed;top: 4rem;z-index: 999999;display: none;left: 50%;margin-left: -3.3rem;}

.myPrisze{
position: absolute;
right: 0;
top: .96rem;
width: 1.9rem;
background: #fff;
text-align: center;
line-height: 0.6rem;
border-radius: 0.2rem 0 0 0.2rem;
color: #bc1f2a;
font-size: 0.32rem;
}

.Tc{
display: none;
z-index: 1001;
}
.tc1{
width: 8.37rem;
position: fixed;
left: 50%;
top: 20%;
margin-left: -4.18rem;
background: url(../img/01.png) top center;
background-size: 100% 100%;
padding-top: 0.6rem;
padding-bottom: 0.64rem;
}

.tc2{
width: 8.37rem;
position: fixed;
left: 50%;
top: 15%;
margin-left: -4.18rem;
background: url(../img/02.png) top center;
background-size: 100% 100%;
padding-top: 0.6rem;
padding-bottom: 0.64rem;
}

.close{
width: 0.64rem;
height: 0.64rem;
border-radius: 50%;
/* float: right; */
position: absolute;
background: url(../img/10.png) top center;
background-size: 100% 100%;
right: 0.2rem;
top: 0.2rem;
cursor: pointer;
z-index: 10;
}
.tcTitle{
height: 0.64rem;
font-size: 0.64rem;
font-family: Source Han Sans CN;
font-weight: bold;
color: #FFFFFF;
text-shadow: 2px 2px 2px #711d00;
/* text-align: left; */
line-height: 0.64rem;
/* margin-left: 2.6rem; */
width: 100%;
text-align: center;
}
.cont2-txt{
width: 7.66rem;
border-radius: 0.27rem;
margin: 0 auto;
padding: 0.41rem 0.33rem;
}

.cont2-txt p{
font-size: 0.32rem;
font-family: Source Han Sans CN;
font-weight: 400;
color: #2D2F3C;
line-height: 0.53rem;
}
.tc3-ul{
width: 7.32rem;
height: 4.52rem;
background: linear-gradient(0deg, #FFF1E3 0%, #FFFFFF 100%);
box-shadow: 0rem 0rem 0rem 0rem rgba(113,29,0,0.48);
border-radius: 0.27rem;
margin: 0 auto;
margin-top: 0.64rem;
}
.tc3-ul li{
width: 5.76rem;
margin: 0 auto;
border-bottom: 1px solid #FFD6BD;
padding-top: 1.15rem;
overflow: hidden;
}
.tc3-ul li input{
border: none;
outline: none;
font-size: 0.37rem;
background: none;
line-height: 0.6rem;
}
.tc3-ul li input#phone{}
.tc3-ul li input#code{
width: 3rem;
}
.tc3-ul li span{
float: right;
height: 0.28rem;
font-size: 0.29rem;
font-family: Source Han Sans CN;
font-weight: 400;
color: #FF6A06;
}
.tc3-ul li span.fxe_ReSendValidateCoad{}
.tc3-ul li span.fxe_validateCode{}

.tc3,.tc4{
width: 8.37rem;
position: fixed;
left: 50%;
top: 20%;
margin-left: -4.18rem;
background: url(../img/01.png) top center;
background-size: 100% 100%;
padding-top: 0.6rem;
padding-bottom: 0.6rem;
}

.tc3-btn{
width: 7.19rem;
height: 1.23rem;
background: linear-gradient(0deg, #FFD468 0%, #FFEDAD 100%);
box-shadow: 0px 0px 3px 3px rgb(113 29 0 / 18%);
border-radius: 0.61rem;
text-align: center;
line-height: 1.23rem;
font-size: 0.43rem;
font-family: Source Han Sans CN;
font-weight: bold;
color: #FF6A06;
margin: 0 auto;
margin-top: 0.43rem;
}

.jiang {height: 3.2rem;margin-top: 0.27rem;}
.jiang img{
width: auto;
height: 100%;
margin: 0 auto;
display: block;
}
.tc2-2{
width: 7.32rem;
/* height: 6.76rem; */
background: linear-gradient(0deg, #FFF1E3 0%, #FFFFFF 100%);
box-shadow: 0rem 0rem 0rem 0rem rgba(113,29,0,0.48);
border-radius: 0.27rem;
margin: 0 auto;
margin-top: 0.4rem;
padding-top: 0.43rem;
}
.tc2-2>p{
text-align: center;
font-family: Source Han Sans CN;
font-weight: 500;
font-size: 0.37rem;
color: #740000;
}
.cont8{}
.cont8-a{
    overflow: hidden;
}
.cont8 p{margin-bottom: 0.27rem;width: 8.49rem;font-family: Source Han Sans CN;font-weight: 500;font-size: 0.32rem;color: #303030;line-height: 0.42rem;}
.cont8 a{float: left;text-align: center;line-height: 0.88rem;width: 4rem;height: 0.84rem;border-radius: 0.11rem;font-family: Source Han Sans CN;font-weight: 500;font-size: 0.36rem;color: #FFFFFF;position: relative;background: #1D40D8;}
.cont8 a+a{margin-left: 0.08rem;float: right;}
.cont8 a img{}
.fullTc{display: none;width: 100%;height: 100%;position: fixed;left: 0;top: 0;background: rgb(0 0 0 / 50%);z-index: 1000;}
.cont8 p span{
    display: inline-block !important;
    padding: 0 !important;
    margin: 0 !important;
    background: none !important;
}

.videoPlayK {
background: #000000;
position: fixed;
top: 0;
left: 0;
width: 100%;
height: 100%;
z-index: 99999999999;
display: none;
}
.m_nav a {
background: url(https://static.fangxiaoer.com/m/images/nav.png) no-repeat;
;;background-size: 139px auto;;;
z-index: 999;
}
.return {
position: absolute;
left: 23px;
top: 12px;
width: 10px;
height: 19px;
}
.m_nav {
width: 100%;
height: 43px;
background-color: #F6F6F6;
color: #000;
text-align: center;
font-size: 15px;
/* position: relative; */
line-height: 43px;
}
.catalog, .nav_close {
display: block;
position: absolute;
right: 12px;
top: 7px;
width: 37px;
height: 28px;
background-position: -10px 5px !important;
overflow: hidden;
}
.videoPlayK video {
position: fixed;
top: 43px;
left: 0;
width: 100%;
height: 629px;
}


.video-icon{
}
.video-icon i {
display: inline-block;
width: 0.45rem;
height: 0.45rem;
background: url(../img/sp1.png) no-repeat;
background-size: 100% 100%;
position: absolute;
top: 0.28rem;
right: 0.35rem;
}
.seeMore{
	width: 8.48rem;
	height: 0.89rem;
	background: url(../img/new/11.png) top center;
	background-size: 100% 100%;
	margin: 0 auto;
}
.cont9{}
.wz{
overflow: hidden;
margin-bottom: 0.5rem;
}
.iocn9 li{width: 49.5%;float: left;width: 4rem;height: 1.97rem;border-radius: 0.11rem;background: rgb(2 57 158 / 50%);}
.iocn9 li a{display: block;overflow: hidden;}
.iocn9 li+li{
    float: right;
    background: rgb(3 119 193 / 50%);
}
.wz a{
width: 1.67rem;
height: 0.67rem;
background: #F8401B;
border-radius: 0.05rem;
display: block;
text-align: center;
line-height: 0.67rem;
font-family: Alibaba PuHuiTi;
font-weight: 400;
font-size: 0.31rem;
color: #FFFFFF;
}
.iocn9 img{
width: auto;
height: 1.73rem;
display: block;
margin: 0 auto;
}
.iocn9 span{font-family: Source Han Sans CN;font-weight: bold;font-size: 0.49rem;color: #FFFFFF;}
.iocn9 h5{
    float: right;
    width: 1.81rem;
    height: 1.81rem;
    text-align: center;
    background: url(../img/new/5.png) top center;
    background-size: 100% 100%;
    margin-top: 0.09rem;
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 0.43rem;
    color: #FFFFFF;
    padding: 0.3rem 0.3rem;
    cursor: pointer;
}
.iocn9 p{
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 0.31rem;
    color: #FFFFFF;
    line-height: 0.31rem;
    margin-top: 0.13rem;
}
.iocn9 div{
    float: left;
    margin: 0.55rem 0 0 0.19rem;
}
.cont6-ul li:nth-child(3n){ margin-right: 0}

.cont6-ul li{

}
.cont7-li{
	width: 2.67rem;
	height: 0.99rem;
	background: #FFFFFF;
	border-radius: 0.05rem;
	border: 0.01px solid rgba(0,0,0,0.15);
	display: block;
	text-align: center;
	line-height: 0.99rem;
	overflow: hidden;
	white-space: pre;
	text-overflow: ellipsis;
		float: left;
		margin-right: 0.2rem;
		margin-bottom: 0.16rem;
}

.xR{
position: fixed;
right: 0;
top: 9rem;
z-index: 100;
}
.xR li{margin-bottom: 0.13rem;}
.xR li a,.xR li span{width: 1.45rem;height: 0.51rem;background: rgb(0 0 0 / 50%);border-radius: 0.08rem 0rem 0rem 0.08rem;text-align: center;line-height: 0.51rem;display: block;font-family: Alibaba PuHuiTi;font-weight: 500;font-size: 0.31rem;color: #FFFFFF;cursor: pointer;border: 0.3px solid rgb(255 255 255 / 30%);}
.wz li{
float: left;
margin-right: 0.6rem;
}
.wz li:last-child{
margin-right: 0;
}
.cont11{background: url(../img/99.png) bottom;background-size: 100% 100%;}

/* 弹幕测试 */
.bulletChats{
/* position: absolute; */
top: 3.76rem;
overflow: hidden;
/* position: fixed; */
height: 7rem;
background-repeat: no-repeat;
background-size: 100% 100%;
bottom: 100px;
left: 0;
z-index: 9;
width: 100%;
font-size: 12px;
}
.bulletChats marquee>span{
 height: 0.96rem;
 border-radius: 0.96rem;
 padding-left: 0.5rem;
 padding-right: 0.5rem;
 width: auto;
 margin-bottom: 0.2rem;
 display: inline-block;
 margin-right: 0.3rem;
 background: #FFE3CE;
}
.bulletChats span img{
 width: 0.67rem;
 height: 0.67rem;
 border-radius: 50%;
 float: left;
 margin-top: 0.145rem;
 margin-left: 0.12rem;
 margin-right: 0.28rem;
}
.bulletChats span span{
 height: 0.88rem;
 line-height: 0.96rem;
 text-align: left;
 font-family: Alibaba PuHuiTi;
 font-weight: 500;
 font-size: 0.32rem;
 color: #303030;
}
.block{
position:absolute;
}


.bulletChats .marquee1{
margin-left: -1rem;
}
.bulletChats .marquee2{
margin-left: -0.6rem;
margin-top: 0.2rem;
}
.bulletChats .marquee3{
margin-left: -0.1rem;
margin-top: 0.2rem;
}
.bulletChats .marquee4{
margin-left: 1rem;
margin-top: 0.2rem;
}
.bulletChats .marquee5{
margin-left: -0.91rem;
margin-top: 0.2rem;
}
.bulletChats .marquee2>span:nth-child(2n){
 
  margin-left: 0.5rem;
}
.bulletChats .marquee3>span:nth-child(2n){

  margin-left: 0.1rem;
}
.bulletChats .marquee2>span{margin-right: 15px;margin-left: 60px;}
.bulletChats .marquee2 div+div{
margin-left: 0;
}

.dcwq{}
.cont10{}
.cont10 p{
font-family: Alibaba PuHuiTi;
font-weight: 500;
font-size: 0.32rem;
color: #303030;
line-height: 0.6rem;
margin-bottom: 0.32rem;
}
.zl{width: 8.99rem;/* height: 2.94rem; */background: linear-gradient(0deg, #FFFFFF 0%, #FFFDEC 99%);border-radius: 0 0 0.27rem 0.27rem;margin: 0 auto;padding-top: 0.2rem;margin-top: -0.12rem;margin-bottom: 0.43rem;padding-bottom: 0.56rem;}
.zl p{
height: 0.51rem;
font-family: Source Han Sans SC;
font-weight: 400;
font-size: 0.51rem;
color: #C70000;
line-height: 0.51rem;
}
.zl .zlbtn{margin: 0.35rem auto 0.24rem auto;width: 6.75rem;height: 1.27rem;border-radius: 0.37rem;display: block;background: url(../img/new/4.png) top center;background-size: 100% 100%;}
.zl div{height: 0.38rem;font-family: Source Han Sans SC;font-weight: 400;font-size: 0.36rem;color: #696969;line-height: 0.38rem;/* margin-left: 0.69rem; */margin: 0 auto;text-align: center;}
.zl div span{padding: 0 0.1rem;font-weight: bold;font-size: 0.51rem;color: #C70000;}
.showTxt{
position: fixed;
left: 0;
bottom: 0;
overflow: hidden;
background: #fff;
height: 1.5rem;
padding: 0.27rem 0.29rem;
width: 100%;
z-index: 1000;
}
#realTxt{
box-shadow: 0rem 0rem 0rem 0rem rgba(132,132,132,0.6);
line-height: 0.91rem;
padding-left: 0.3rem;
width: 7.08rem;
height: 0.92rem;
background: #EAEAEA;
border-radius: 0.07rem;
border: none;
}
.upMsg{
float: right;
text-align: center;
line-height: 0.92rem;
width: 1.89rem;
height: 0.92rem;
background: #FF7200;
border-radius: 0.07rem;
font-family: Alibaba PuHuiTi;
font-weight: 400;
font-size: 0.37rem;
color: #FFFFFF;
}

.qm{
height: 0.37rem;
font-family: Alibaba PuHuiTi;
font-weight: 500;
font-size: 0.37rem;
color: #303030;
line-height: 0.37rem;
margin-bottom: 0.2rem;
}
.imgUl{
overflow: hidden;
}
.imgUl li{float: left;width: 48%;margin-right: 0.12rem;margin-bottom: 0.2rem;height: 3rem;overflow: hidden;border-radius: 0.13rem;}
.imgUl li:nth-child(2n){
margin-right: 0;
}
.imgUl li.w22{width: 4.16rem;height: 3.12rem;}
.imgUl li.w33{
width: 2.7rem;
height: 2.07rem;
}
.imgUl li.w11{
width: 100%;
/* width: 9.4rem; */
height: auto;
margin-bottom: 0.16rem;
}
.w11 img{
height: auto;
}
.bulletChats .marquee1>span{background: #D2FBFF;}
.bulletChats .marquee1>span:nth-child(2n){background: #D5E8FF;}
.bulletChats .marquee2>span{background: #FFDEF7;}
.bulletChats .marquee2>span:nth-child(2n){background: #D0FFD3;}
.bulletChats .marquee3>span{}
.bulletChats .marquee3>span:nth-child(2n){background: #FFFAC5;}

.cont12{}
.cont12 p{}
.cont12 .ljyy{
width: 8.59rem;
height: 0.88rem;
background: #ffa96a;
border-radius: 0.05rem;
text-align: center;
line-height: 0.88rem;
font-family: Alibaba PuHuiTi;
font-weight: 500;
font-size: 0.43rem;
color: #FEFFAF;
margin-top: 0.31rem;
}
.tc5{
width: 9.2rem;
position: fixed;
left: 50%;
top: 7%;
margin-left: -4.6rem;
padding-top: 0.6rem;
padding-bottom: 0.6rem;
background: #fff;
border-radius: 0.4rem;
padding-top: 0.9rem;
}
.tc5 p{
width: 8.71rem;
font-family: Alibaba PuHuiTi;
font-weight: 500;
font-size: 0.32rem;
color: #303030;
margin: 0 auto;
margin-bottom: 0.23rem;
}
.tc5-title{
position: absolute;
width: 9.2rem;
height: 1.11rem;
top: 0;
}
.mapImg{
width: 8.56rem;
margin: 0.32rem auto 0.2rem auto;
display: block;
}
.yyBtn{
width: 8.59rem;
height: 0.88rem;
background: #ffa96a;
border-radius: 0.05rem;
text-align: center;
color: #FEFFAF;
margin: 0 auto;
margin-top: 0.2rem;
display: block;
}
.tc6{
width: 9.2rem;
background: #EDEDED;
box-shadow: 0rem 0rem 0rem 0rem #EDEDED;
border-radius: 0.27rem;
position: fixed;
left: 50%;
top: 30%;
margin-left: -4.6rem;
padding-top: 0.55rem;
}
.tc6 h4{
width: 100%;
height: 0.57rem;
font-family: Alibaba PuHuiTi;
font-weight: 800;
font-size: 0.6rem;
color: #303030;
margin-bottom: 0.93rem;
text-align: center;
}
.tc6 p{
width: 100%;
height: 0.35rem;
font-family: Alibaba PuHuiTi;
font-weight: 800;
font-size: 0.35rem;
color: #303030;
margin-bottom: 1.81rem;
text-align: center;
}

.yyPhone{
width: 8.39rem;
height: 0.88rem;
border-radius: 0.13rem;
border: 1px solid #ffa96a;
margin: 0 auto;
display: block;
padding-left: 0.2rem;
margin-top: 0.2rem;
outline: none;
}
.yydd{
	width: 8.39rem;
	height: 0.88rem;
	border-radius: 0.13rem;
	border: 1px solid #ffa96a;
	display: block;
	margin: 0 auto;
outline: none;
}
.cjylBtn{
position: absolute;
right: 0;
top: 1.96rem;
width: 1.87rem;
color: #bc1f2a;
font-size: 0.32rem;
height: 1.39rem;
background: url(../img/103.png) top center;
background-size: 100% 100%;
}
.cont00{}
.ul00,.ul11{overflow: hidden;margin-bottom: 0.37rem;}
.ul00 li,.ul11 li{width: 1.97rem;height: 0.69rem;line-height: 0.69rem;text-align: center;float: left;background: rgba(0,0,0,0.5);font-family: Source Han Sans CN;font-weight: bold;font-size: 0.44rem;color: #FFFFFF;border-radius: 0.05rem;margin-right: 0.32rem;cursor: pointer;}
.ul00 li.active,.ul11 li.active{width: 1.97rem;height: 0.69rem;border-radius: 0.05rem;font-family: Source Han Sans CN;font-weight: bold;font-size: 0.44rem;color: #FFFFFF;line-height: 0.69rem;background: #1D40D8;}
.showul00{
width: 8.57rem;
height: 6.43rem;
border-radius: 0.11rem;
overflow: hidden;
position: relative;
margin-bottom: 0.23rem;
}
.showul11{
border-radius: 0.11rem;
overflow: hidden;
position: relative;
margin-bottom: 0.23rem;
width: 8.56rem;
height: 4.83rem;
}
.showul00>div{
display: none;
height: 6.43rem;
}
.showul11>div{
display: none;
height: 100%;
}
.showul11>div#showvideo001{
display: block;
}
.showul00>div#showul001{display: block;}
.showul{
}
.showul .swiper-slide p{
width: 8.57rem;
height: 0.55rem;
background: rgb(0 0 0 / 50%);
border-radius: 0rem 0rem 0.11rem 0.11rem;
position: absolute;
left: 0;
bottom: 0;
text-align: center;
font-family: Alibaba PuHuiTi;
font-weight: 500;
font-size: 0.37rem;
color: #EDEDED;
line-height: 0.55rem;
}
.showul .swiper-slide img{}
.cont1-R00{
    z-index: 99 !important;
}
.cont1-L00{}
.videoI{
background: url(../img/a/play.png) no-repeat;
background-size: contain;
width: 1rem;
height: 1rem;
position: absolute;
left: 50%;
bottom: 50%;
margin-left: -0.5rem;
margin-top: -0.5rem;
}

.spshow{
position: fixed;
left: 0;
top: 0;
background: rgb(0 0 0 / 100%);
width: 100%;
height: 100%;
z-index: 9999;
display: none;
justify-content: center;
align-items: center;
}
#spshow{}
.cont99{}
.cont99>h4{
font-family: Alibaba PuHuiTi;
font-weight: bold;
font-size: 0.43rem;
color: #303030;
line-height: 0.43rem;
margin-bottom: 0.2rem;
text-align: center;
}
.cont99>h5{
font-size: 0.32rem;
margin-bottom: 0.32rem;
}
.cont99>p{
font-family: Alibaba PuHuiTi;
font-weight: 400;
font-size: 0.32rem;
color: #303030;
line-height: 0.41rem;
margin-bottom: 0.31rem;
}
.ff52{}
.ff52 h5{
font-family: Alibaba PuHuiTi;
font-weight: bold;
font-size: 0.43rem;
color: #F85800;
line-height: 0.43rem;
/* margin-bottom: 0.23rem; */
}
.ff52 p{
font-size: 0.32rem;
color: #F85800;
line-height: 0.43rem;
margin: 0.23rem 0;
font-weight: 400;
font-family: Alibaba PuHuiTi;
}  
.img99{
overflow: hidden;
}
.img99 img{
width: 4.01rem;
height: 3.4rem;
float: left;
}
.img99 img+img{
float: right;
}
.topHe{
width: 100%;
height: 43px;
background-color: #fff;
color: #000;
text-align: center;
font-size: 15px;
line-height: 43px;
z-index: 99;
position: fixed;
top: 0;
}
.topHe .return{
background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
	background-size: 100% 100%;
;display: block;
width: 20px;
height: 20px;
float: left;
;;;;;
;;cursor: pointer;;;
}

.spK{margin-bottom: 0.32rem;}
.videoTitle{
	text-align: center;
	line-height: 0.4rem;
	font-size: 0.32rem;
	margin-bottom: 0.2rem;
}
.xian{
	display: block;
	width: 90%;
	height: 2px;
	background: #ff5200;
	margin: 0.32rem auto;
}
.bannerImg{
    height: 8.35rem;
}

.titleBox{
    width: 9.13rem;
    height: 1.106rem;
    display: block;
    margin: 0 auto;
    overflow: hidden;
    
}
.titleIcon{
    display: block;
    width: 0.55rem;
}
.titleBox2{
    width: 1.88rem;
    height: 1.106rem;
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 0.48rem;
    color: #FFFFFF;
    line-height: 1.106rem;
    margin-left: 0.98rem;
}
.Ticon1{background: url(../img/new/01.png) top center;background-size: 100% 100%;}
.Ticon2{background: url(../img/new/02.png) top center;background-size: 100% 100%;}
.Ticon3{background: url(../img/new/03.png) top center;background-size: 100% 100%;}
.Ticon4{background: url(../img/new/04.png) top center;background-size: 100% 100%;margin-top: 0.7rem;}
.Ticon5{background: url(../img/new/05.png) top center;background-size: 100% 100%;}
.Ticon6{background: url(../img/new/06.png) top center;background-size: 100% 100%;}
.Ticon7{background: url(../img/new/07.png) top center;background-size: 100% 100%;}
.Ticon8{background: url(../img/new/08.png) top center;background-size: 100% 100%;}
.Ticon9{background: url(../img/new/09.png) top center;background-size: 100% 100%;}
.Ticon10{background: url(../img/new/10.png) top center;background-size: 100% 100%;}
.Ticon11{background: url(../img/new/010.png) top center;background-size: 100% 100%;}

.lqyhq{
    margin: 0 auto;
    width: 8.99rem;
    height: 2.78rem;
    background: linear-gradient(0deg, #FFFFFF 0%, #FFFDEC 99%);
    border-radius: 0.27rem;
    margin-bottom: 0.41rem;
}
.lqyhq1{
    display: block;
    width: 1.77rem;
    height: 2.23rem;
    float: left;
    margin: 0.28rem 0.27rem 0  0.61rem;
}
.lqyhq2{
    float: left;
    margin-top: 0.39rem;
    font-family: Source Han Sans CN;
    font-weight: 500;
    font-size: 0.59rem;
    color: #303030;
    line-height: 0.59rem;
}
.lqyhq3{
    float: left;
    display: block;
    width: 5.8rem;
    height: 1.08rem;
    background: url(../img/new/3.png) top center;
    background-size: 100% 100%;
    margin-top: 0.36rem;
}


.showSTitle{
    height: 0.69rem;
    border-radius: 0.05rem;
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 0.44rem;
    color: #FFFFFF;
    line-height: 0.69rem;
    padding: 0 0.09rem;
    margin-bottom: 0.19rem;
    display: inline-block;
    background: #1D40D8;
    }
.cont8-i1,.cont8-i2{
    display: block;
    width: 0.35rem;
    height: 0.4rem;
    position: absolute;
    background: url(../img/new/1.png) top center;
    background-size: 100% 100%;
    left: 0.8rem;
    top: 0.21rem;
}

.cont8-i2{
	background: url(../img/new/2.png) top center;
	background-size: 100% 100%;
	left: 0.4rem;
	top: 0.21rem;
}
.TTCClose{
    background: url(../img/close2.png) top center;
    background-size: 100% 100%;
    display: block;
    width: 0.71rem;
    height: 0.71rem;
    cursor: pointer;
    position: absolute;
    right: 0.2rem;
    top: 0.2rem;
}
.TTC1{
    width: 10rem;
    height: 11.92rem;
    position: fixed;
    left: 50%;
    top: 15%;
    background: url(../img/new/31.png) top center;
    background-size: 100% 100%;
    z-index: 1001;
    margin-left: -5rem;
    padding-top: 5.2rem;
    display: none;
}
.TTC1 p{
    width: 4.98rem;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 0.32rem;
    color: #303030;
    margin: 0 auto;
}
.TTC1 p+p{}
.TTC1-ul{
    margin: 0.31rem auto;
    display: block;
    width: 4.93rem;
}
.TTC1-ul li{margin-bottom: 0.15rem;}
.TTC1-ul li input{
    width: 4.93rem;
    height: 0.9rem;
    background: #FFE2C7;
    border-radius: 0.13rem;
    border: none;
    outline: none;
    padding-left: 0.32rem;
    color: #FF6000;
}
.TTC1-ul li input:focus {
  background: #FFE2C7;
}
input::placeholder{color: #ccc;}
.TTC1-ul li input#code{
    width: 2.6rem;
}
.TTC1-btn{
    width: 4.37rem;
    height: 0.8rem;
    background: #FF8A2B;
    border-radius: 0.4rem;
    margin: 0 auto;
    text-align: center;
    line-height: 0.8rem;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 0.34rem;
    color: #FFFFFF;
    cursor: pointer;
}
.TTC2{
    display: none;
    width: 9.77rem;
    height: 8.69rem;
    position: fixed;
    top: 20%;
    left: 50%;
    z-index: 10001;
    background: url(../img/new/30.png) top center;
    background-size: 100% 100%;
    margin-left: -4.88rem;
}
.TTC2 h4{}
.TTC2 p{}
.TTC2 p+p{}
.get_code{
 color: #FF6000;
}

.zhuzhai {
    display: block;
    color: #000;
    text-decoration: none;
    position: relative;
    padding: 15px 0 !important;
    margin: 0 8px;
    padding-bottom: 12px !important;
    border-top: 1px solid #e8e8e8;
}
.esfJcD {
    float: left;
    width: 108px;
    overflow: hidden;
    position: relative;
    margin-right: 10px;
}
.left_tu {
    display: block;
    float: left;
    margin-right: 10px;
    margin-top: 0 !important;
    width: 108px;
    height: 82px;
    border-radius: 5px;
}
.right_info {
    height: auto;
    float: left;
    width: 61%;
}

.cl {
    clear: both;
}
.zhuzhai .title {
    font-size: 15px !important;
    width: auto !important;
    height: auto;
    font-weight: bold;
    color: #000000;
    margin-top: -3px;
    line-height: 19px !important;
    white-space: normal;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    margin-right: 0;
    text-overflow: ellipsis;
    margin-bottom: 0;
}

.address {
    color: #777;
    overflow: hidden;
    white-space: nowrap;
    text-overflow: ellipsis;
}
.address_main {
    font-size: 11px;
    line-height: 11px;
    height: 12px;
    margin-bottom: -1px;
    color: #545454;
    display: inline-block;
    margin-right: 3px;
    margin-top: 9px;
}

.charact_dist {
    display: block;
    margin-top: 7px;
    overflow: hidden;
}

.charact {
    list-style: none;
}
.charact li {
    border-radius: 3px;
    font-style: normal;
    margin-right: 2px;
    display: inline-block;
    height: 16px !important;
    line-height: 16px !important;
    color: #545454 !important;
    border: 0px solid #ddd;
    font-size: 10px;
    background: #f4f5f7;
    padding: 0px 3px;
    border-color: #ddd;
}
.charact li.em1 {
    border-color: #ddd;
    color: #ff5200;
}

.esfPrice {
    float: left;
    font-size: 16px;
    color: #ff6f28;
    font-weight: bold;
    margin-top: 4px;
    margin-right: 9px;
}
.esfPrice i {
    font-weight: 400;
    font-style: normal;
    font-size: 12px;
}

.new-house2 .address_span {
    font-size: 11px;
    line-height: 11px;
    height: 11px;
    color: #545454;
    margin-top: 9px;
}

.address_span span {
    float: left;
    font-size: 11px;
}
.houseList{
    background: #fff;
    width: 8.99rem;
    margin: 0 auto;
    margin-top: -0.09rem;
    background: linear-gradient(184deg, #FFFFFF 0%, #FFFDEC 99%);
    padding-bottom: 1.3rem;
}
.houseList a:first-child{border-top:none}

.address_span {
    font-size: 11px;
    line-height: 11px;
    height: 11px;
    color: #545454;
    margin-top: 9px;
}
.moreHouse{
    width: 8.48rem;
    height: 0.89rem;
    background: url(../img/new/11.png) top center;
    background-size: 100% 100%;
    margin: 0 auto;
    margin-top: -1.2rem;
}
.hide{display: none !important;}
/* äºŒæ‰‹æˆ¿ç›¸å…³è§†é¢‘ï¼Œå…¨æ™¯å›¾æ ‡ */
.listIconOne{}
.uhouselist .listIconTwo{
    left: 18px;
    top: 28px;
}
.subHousesMain .listIconTwo{
    left: 18px;
    top: 28px;}
.listIconTwo{
    position: absolute;
    left: 26px;
    top: 36px;
    width: 72px;
    overflow: hidden;
}
.ListIconVr{
    background: url(https://static.fangxiaoer.com/m/images/ListIconVr.png);
    width: 28px;
    height: 28px;
    display: block;
    position: absolute;
    left: 49px;
    top: 36px;
    background-size: 100% 100%;
}
.ListIconVideo{
    background: url(https://static.fangxiaoer.com/m/images/ListIconVideo.png);
    ;;;;;;;;;width: 0.52rem;;;;;;;;;;
    ;;height: 0.52rem;;;
    display: block;
    position: absolute;
    ;;;;;;;;;left: 0.22rem;;;;;;;;;;
    ;;;;;;;;;;;;;;;;;;;;
    ;;;;;;;;top: 1.8rem;;;;;;;;;background-size: 100% 100%;
}
.listIconOne .ListIconVr{}
.listIconOne .ListIconVideo{}
.listIconTwo .L istIconVr{
    position: initial;
    float: left;
}
.listIconTwo .ListIconVideo{
    position: initial;
    float: left;
    margin-left: 15px;
}
.drawBtn{
	
width: 1.45rem;
	
height: 0.91rem;
	
background: url(../img/new/12.png) top center;
	
background-size: 100% 100%;
	
margin-bottom: 0.16rem;
	
cursor: pointer;
}

/*转盘*/
.popup{width: 9.8rem;height: 12rem;display: none;position: fixed;left: 0;right: 0;top: 5rem;margin: auto;background-size: 100% 100%;background-repeat: no-repeat;background-position: top center;z-index: 10001;}
.luck {margin: auto;z-index: 9;width: 4.57rem;height: 5.14rem;position: relative;}
.turntable {display: block;width: 4.57rem;height: 5.14rem;margin: 0 auto;}
.luckBtn {display: block;width: 1.57rem;height: 1.57rem;position: absolute;cursor: pointer;top: 1.3rem;left: 0;right: 0;margin: auto;}
.luck_tp { width: 6rem; height: 2.8rem; margin: auto; z-index: 10; background-image: url(../img/dw/a16.png); background-size: 100% 100%; background-repeat: no-repeat; background-position: center; }
.kkbtn { color: #22871A; font-size: 16px; text-align: center; font-weight: bolder; background-image: url(../img/dw/a17.png); background-size: 100% 100%; background-repeat: no-repeat; background-position: center; width: 3.65rem; height: 1.08rem; line-height: 1rem; margin: 0.5rem auto auto auto; }
.fullbg{
    display: none;
    width: 100%;
    height: 100%;
    position: fixed;
    left: 0;
    top: 0;
    background: rgb(0 0 0 / 50%);
    z-index: 1000;
}
.closeX{
 display: block;
 width: 0.72rem;
 height: 0.72rem;
 background-image: url(../img/new/32.png);
 background-size: 100% 100%;
 position: absolute;
 right: -26px;
 top: -6rem;
 cursor: pointer;
 z-index: 999;
}
.luckLogin{
	height: 4.77rem;
	width: 5.82rem;
	margin: 0 auto;
	background-image: url(../img/new/tc1.png);
	background-size: 100% 100%;
	margin-top: -0.7rem;
	padding-top: 1px;
	display: block;
	position: relative;
}
.luckLogin ul{
    width: 5.98rem;
    height: 95%;
    margin: 0 auto;
    background: url(../img/new/34.png) bottom center;
    background-size: 100% 100%;
    margin-top: 0.45rem;
    padding-top: 1rem;
    margin-left: -0.08rem;
}
.luckLogin li{
	list-style: none;
	margin-bottom: 0.21rem;
	margin-left: 0.7rem;
	overflow: hidden;
}
.luckLogin li span{
    height: 0.31rem;
    font-family: Source Han Sans SC;
    font-weight: 500;
    font-size: 0.22rem;
    color: #FF6000;
}
.luckLogin li input{
    width: 4.13rem;
    height: 0.68rem;
    background: #FFE2C7;
    border-radius: 0.13rem;
    border: none;
    padding-left: 0.19rem;
    outline: none;
}
.luckLogin li input#code2{
    width: 2.42rem;
}
.luckLogin li span.fxe_validateCode{}

.drawLogin{
    width: 3.3rem;
    height: 0.61rem;
    background: #FF8A2B;
    border-radius: 0.3rem;
    text-align: center;
    line-height: 0.61rem;
    margin: 0 auto;
    margin-top: 0.32rem;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 0.25rem;
    color: #FFFFFF;
    cursor: pointer;
}

.MyGift{
    margin: 0 auto;
    width: 5.52rem;
    background: url(../img/new/tc3.png) no-repeat;
    background-size: 100% 100%;
    margin-top: -0.7rem;
    padding-top: 1px;
    padding-bottom: 0.61rem;
    display: none;
}
.MyGift h4{
    width: 3.07rem;
    height: 0.43rem;
    background: #BF5C04;
    border-radius: 0.21rem;
    margin: 0.88rem auto 0.33rem auto;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 0.32rem;
    color: #FFFFFF;
    text-align: center;
}
.giftTxt {}
.giftTxt li{
    font-family: Source Han Sans SC;
    font-weight: 800;
    font-size: 0.36rem;
    color: #FFFFFF;
    text-shadow: 0.03rem 0.03rem 0.03rem rgb(111 50 27 / 52%);
    line-height: 0.55rem;
    text-align: center;
}
.Giftmsg{
    width: 4.5rem;
    margin: 0.28rem auto 0.23rem auto;
    display: block;
}
.noGift{
    display: none;
    position: relative;
    height: 4.77rem;
    width: 5.52rem;
    margin: 0 auto;
    background-image: url(../img/new/tc2.png);
    background-size: 100% 100%;
    margin-top: -0.7rem;
    padding-top: 1px;
}
.noGiftBtn{
    width: 3.3rem;
    height: 0.61rem;
    background: #FFFFFF;
    border-radius: 0.3rem;
    margin: 0 auto;
    font-family: Source Han Sans SC;
    font-weight: bold;
    font-size: 0.25rem;
    color: #FF8222;
    line-height: 0.61rem;
    text-align: center;
    position: absolute;
    left: 50%;
    margin-left: -1.65rem;
    bottom: 0.4rem;
    cursor: pointer;
}
.GiftBtn{
     width: 3.3rem;
	  height: 0.61rem;
     background: #FFFFFF;
     border-radius: 0.3rem;
     margin: 0 auto;
     font-family: Source Han Sans SC;
     font-weight: bold;
     font-size: 0.25rem;
     color: #FF8222;
     line-height: 0.61rem;
     text-align: center;
     cursor: pointer;
}
#marquee-box{
    overflow: hidden;
    margin: 0 auto;
}
#marquee-con{
    width: 500%;
    height: 4.31rem;
    float: left;
    overflow: hidden;
}
#marquee,#marquee-1{
    float:
    left;
    height: 4.31rem;
    width: 500%;
}
.marquee-img{
    float:
    left;
    margin-left: 0.31rem;
    width: 5.75rem;
    height: 4.31rem;
}
.marqueek{
	padding: 0.27rem 0.16rem;
	background: linear-gradient(0deg, #FFFFFF 0%, #E4F8FF 100%);
	width: 8.99rem;
	margin: 0 auto;
	margin-top: -0.2rem;
	height: 4.85rem;
}
/*-----*/
.explain{ font-weight: 800;font-size: 0.32rem;color: #FF7713; width: 1.57rem;line-height: 0.59rem;background: #FFFFFF;border-radius: 0.05rem 0rem 0rem 0.05rem; text-align: center; position: fixed; top: 0.4rem; right: 0; z-index: 3332;}
.show{ display: block !important;}
















