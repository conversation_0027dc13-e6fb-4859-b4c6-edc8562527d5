i, em, b { font-style: normal; }
* { box-sizing: border-box; margin: 0; padding: 0; }
body { background-color: #FEDD9D; max-width: 10rem;
    /*background-image: url(../img/bg.jpg); background-size: 100% 100vh; background-repeat: no-repeat; background-position: center bottom; */
    min-height: 100vh;
}

.cjm{ width: 10rem; height: 13.5rem; position: relative;
    background-image: url(../img/88.png); background-size: 100%; background-repeat: no-repeat; background-position: center 0.6rem;}
.hti{ width: 8.42rem; height: 6.05rem; position: absolute; left: 0.3rem; top: -5.5rem; z-index: 222;
    background-image: url(../img/99.png); background-size: 100%; background-repeat: no-repeat; background-position: top center;
}




.content { width: 10rem; margin: 0 auto; position: relative;}
.head { width: 100%; position: relative; }
.head img { width: 100%; }
.rbt { position: fixed; width: 2.2rem; top: 1.3rem; right: 0; z-index: 10; }
.ti { border-radius: 0.1rem 0rem 0rem 0.1rem; color: #fff; text-align: center; font-size: 0.37rem; font-family: Microsoft YaHei; font-weight: 400; line-height: 0.67rem; width: 100%; margin-bottom: 0.266rem; height: 0.8rem; }
.main { width: 100%; padding: 0 0.426rem; }
.mtp { width: 100%; position: relative; margin-top: -0.68rem; z-index: 9; }
.tpa { width: 5.86rem; height: 1.74rem; background-color: #0358CD; border-radius: 0.13rem; display: inline-block; color: #fff; position: relative; }
.tasp { display: block; font-size: 0.4rem; line-height: 0rem; margin-top: .3rem;
}
.tal { position: absolute; top: 0.22rem; left: 0.75rem; display: inline-block; text-align: center; }
.tar { position: absolute; display: inline-block; text-align: center;
    width: 3rem;
    top: 0;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    padding-top: 0.37rem;
}
.tpb { width: 3.1rem; height: 1.74rem; background: #0358CD;  border-radius: 0.13rem; display: inline-block; position: absolute; top: 0; right: 0; text-align: center; }
.tpb img { width: 1.16rem; height: 1rem; display: block; margin: 0 auto; padding-top: 0.146rem; }
.tpb span { font-size: 0.346rem; font-family: Microsoft YaHei; font-weight: 400; color: #fff; margin-top: 0.06rem; display: block; }
.vote { width: 100%; margin-top: 0.64rem; margin-bottom: 1.5rem; }
.vobt { font-size: 0.5rem; font-family: Microsoft YaHei; font-weight: bold; color: #FFFFFF; width: inherit; height: 1rem; position: fixed; bottom: 0.25rem; padding: 0 0.426rem; z-index: 13; }
.vom1{  height: 100%; border-radius: 0.1rem; text-align: center; display: none;
    background-image: url('../img/t1.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;
}
.vom2{  height: 100%; border-radius: 0.1rem; text-align: center; display: none;
    background-image: url('../img/t2.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;
}
.vom3{  height: 100%; border-radius: 0.1rem; text-align: center; display: none;
    background-image: url('../img/t3.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;
}
.f1 { font-size: 0.5rem; width: 3rem;}
.f2 { font-size: 0.35rem; margin-top: -0.1rem; width: 3rem;}
.vi { width: 4.25rem; height: 6.5rem; margin-bottom: 0.64rem; display: inline-block; margin-right: 0.53rem; border: 1px solid #3A3A3A; overflow: hidden;  }
.vi:nth-child(even) { margin-right: 0 !important; }
.vimg { width: 4.25rem; height: 3.18rem; background: #3A3A3A; border-radius: 0.13rem; position: relative; }
.vimg img { width: 100%; height: 100%; }
.uvo { text-align: center; width: 100%; margin-top: 0.37rem; }
.uvo span { display: block; font-size: 0.34rem; font-family: Microsoft YaHei; font-weight: 400; color: #3A3A3A; line-height: 0.85rem; }
.ubt { width: 3.93rem; height: 1.05rem; background: #0358CD; color: #fff; border-radius: 0.13rem; text-align: center; margin: 0.1rem auto 0 auto; font-size: 0.34rem; font-family: Microsoft YaHei; font-weight: 400; line-height: 1rem; }
.bg { position: fixed; top: 0; left: 0; background-color: rgba(0,0,0,.7); z-index: 99999; width: 100vw; height: 100vh; display: none; }
.zanw { width: 9.14rem; height: 12rem; position: fixed; z-index: 999999; top: 0; bottom: 0; left: 0; right: 0;  border-radius: 0.1rem; margin: auto; z-index: 999999; text-align: center; display: none; background-color: #fff; }
.zanw span { font-size: 0.43rem; font-family: Microsoft YaHei; font-weight: 400; color: #909090; }
.zanw img { width: 6.98rem; height: 5.37rem; display: block; margin: auto; }
.zcls {
    background: #8e1111;
    text-align: center;
    position: absolute;  left: 0; right: 0; margin: auto;
    font-size: 0.4rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #fff;
    width: 6rem;
    line-height: 1.1rem;
    border-radius: 0.55rem;
    bottom: 0.55rem;
}
.guize { width: 8.93rem; padding-bottom: 0.5rem; position: fixed; z-index: 999999; top: 4.72rem; left: 0; right: 0; margin: auto; border-radius: 0.27rem; display: none; overflow: hidden; background-image: url(../img/tcBg.png); background-size: 100% 100%; background-repeat: no-repeat; background-position: top center; }

.gzc ,.zclose{ width: 0.7rem; height: 0.7rem; position: absolute; top: 0.1rem; right: 0.1rem; background-image: url(../img/close2.png); background-size: 100%; background-repeat: no-repeat; background-position: center; }
.gtit { line-height: 0.48rem; text-align: center; margin-top: 0.62rem;

    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 0.64rem;
    color: #FFFFFF;
    text-shadow: 0.05rem 0.05rem 0 #711D00;
}
.tkm{ width: 100%; }
.gtma2{ width: 100%; max-height: 11.8rem;}
.gtm2 { margin-top: 0.72rem; padding: 0.3rem; margin-left: auto; margin-right: auto;width: 7.82rem;height: 11.4rem;background: linear-gradient(0deg, #FFF1E3 0%, #FFFFFF 100%);box-shadow: 0.01rem 0.01rem 0.2rem 0.06rem rgba(113,29,0,0.48);border-radius: 0.27rem;     }
.ytit{ width: 100%; text-align: center; font-size: 0.42rem; color: #711D00; margin-bottom: 0.2rem;}

.gkk{ margin-top: 1rem; padding: 0.3rem; margin-left: auto; margin-right: auto;width: 7.82rem;height: 10.7rem;background: linear-gradient(0deg, #FFF1E3 0%, #FFFFFF 100%);box-shadow: 0.01rem 0.01rem 0.2rem 0.06rem rgba(113,29,0,0.48);border-radius: 0.27rem; }
.gtma{ width: 100%; overflow-y: scroll;}
.gtm { margin-top: 0.72rem; padding: 0.3rem; margin-left: auto; margin-right: auto;width: 7.82rem;

    background: linear-gradient(0deg, #F1FCFF 0%, #FFFFFF 100%);
    box-shadow: 0rem 0rem 0rem 0rem rgba(0,3,113,0.2);
    border-radius: 0.13rem;
}
.gtm h3{ font-weight: bold;font-size: 0.37rem;color: #FF6000;}
.gtm p {  font-size: 0.34rem;  margin-bottom: 0.15rem; }
.paihang { width: 9.2rem; height: 16rem; background-color: #fff; position: fixed; z-index: 999999; top: 0; bottom: 0; left: 0; right: 0; margin: auto; border-radius: 0.1rem; display: none; background-image: url(../img/bg.jpg); background-size: 100%; background-repeat: no-repeat; background-position: center bottom; }
.pgzc { width: 0.85rem; height: 0.85rem; position: absolute; top: 0.22rem; right: 0.44rem; background-image: url(../img/close.png); background-size: 100%; background-repeat: no-repeat; background-position: center; }
.pgtit { font-size: 0.53rem; font-family: Microsoft YaHei; font-weight: 400; color: #0358CD; line-height: 0.48rem; text-align: center; margin-top: 0.62rem; }
.pam { width: 100%;   }
.amt { margin-bottom: 0.42rem; height: 3rem; position: relative; overflow: hidden; }
.at1 { width: 2.25rem; height: 2.3rem; position: absolute; top: 0; left: 0; right: 0; margin: auto; }
.at2 { width: 2.25rem; height: 2.3rem; position: absolute; top: 0.98rem; left: 0.28rem; }
.at3 { width: 2.25rem; height: 2.3rem; position: absolute; top: 0.98rem; right: 0.28rem; }
.amb { width: 100%; height: 13.35rem; overflow: scroll; padding: 0.3rem 0.3rem 0 0.3rem; margin-top: 0.6rem;}
.ai { background: #0358CD; border-radius: 35px; width: 100%; height: 0.93rem; margin-bottom: 0.35rem; line-height: 0.93rem; font-size: 0.34rem; color: #fff; font-family: Microsoft YaHei; font-weight: 400; position: relative; }
.i1 { left: 0.7rem; top: 0rem; position: absolute; }
.i2 { top: 0rem; left: 2.3rem; position: absolute;
    width: 3.75rem;
    height: 0.93rem; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;
}
.i3 {     top: 0rem;
    right: 0.45rem;
    position: absolute;
    width: 1.9rem;
    text-overflow: ellipsis;
    white-space: nowrap;
    overflow: hidden;}
.ath { font-size: 0.34rem; font-family: Microsoft YaHei; font-weight: bold; color: #3A3A3A; line-height: 0.69rem; text-align: center; }
.toux { width: 100%; position: relative; width: 2rem; height: 2rem; margin-top: 0.5rem; margin-left: auto; margin-right: auto; display: none;}
.toux img { width: 100%; height: 100%; border-radius: 50%; }
.toux i { display: block; width: 0.93rem; height: 0.54rem; position: absolute; top: -0.55rem; left: 0; right: 0; margin: auto; background-size: 100%; background-repeat: no-repeat; background-position: center; }
.ti1 { background-image: url(../img/h1.png); }
.ti2 { background-image: url(../img/h2.png); }
.ti3 { background-image: url(../img/h3.png); }
.tx1 { font-size: 0.37rem; font-family: Microsoft YaHei; font-weight: bold; color: #3A3A3A; text-align: center;
    /*margin-top: 0.22rem;*/ }
.tx2 { font-size: 0.34rem; font-family: Microsoft YaHei; font-weight: 400; color: #3A3A3A; text-align: center; margin-top: 0.15rem; }
.share {width: 100%;height: auto;position: fixed;top: 0;right: 0;z-index: 999999;display: none;}
.share img {display: block;width: 85%;height: auto;position: absolute;top: 1rem;right: 0.5rem;}
.redp { width: 100%; height: 100vh; position: fixed; top: 0; right: 0; z-index: 999999; display: none; }
.redp img { display: block; position: absolute; top: 0; bottom: 0; left: 0; right: 0; margin: auto; width: 7.32rem; height: 9.4rem; }
.swp { width: 8.26rem; height: 6.2rem; margin: 0 auto; overflow: hidden; position: relative; z-index: 10; border-radius: 0.26rem; overflow: hidden;}
/*.swp img { width: 8.26rem; height: 6.2rem; object-fit: cover; border-radius: 0.26rem; }*/
.swp img { height: 6.2rem; display: block;  margin: 0 auto; }

.page2 { padding-top: 0.2rem; position: relative; display: none; }
.pmk { width: 8.26rem; margin: 0 auto; }
.uinfo { width: 100%; height: 0.4rem; position: relative; margin-top: 0.92rem; margin-bottom: 0.92rem; font-size: 0.34rem; font-family: Microsoft YaHei; font-weight: 400; color: #3A3A3A; }
.fn1 { position: absolute; top: 0; left: 0.5rem; }
.fn2 { position: absolute; top: 0; left: 0.3rem; }
.fn3 { position: absolute; top: 0; right: 0.5rem; }
.to { width: 100%; height: 1rem; background: #0358CD; border-radius: 0.29rem; text-align: center; font-size: 0.4rem; font-family: Microsoft YaHei; font-weight: 400; color: #fff; line-height: 1rem; margin-bottom: 0.48rem; letter-spacing: .1rem;
    position: relative; }
.to2 { width: 100%; height: 1rem; background: #DDDDDD; border-radius: 0.29rem; text-align: center; font-size: 0.4rem; font-family: Microsoft YaHei; font-weight: 400; color: #999999; line-height: 1rem; margin-bottom: 0.48rem; letter-spacing: .1rem;
    position: relative; display: none;}
.to3 { width: 100%; height: 1rem; background: #0358CD; border-radius: 0.29rem; text-align: center; font-size: 0.4rem; font-family: Microsoft YaHei; font-weight: 400; color: #fff; line-height: 1rem; margin-bottom: 0.48rem; letter-spacing: .1rem;
    position: relative; display: none; }
.tpd { width: 100%; height: 1rem; background: #DDDDDD; border-radius: 0.29rem; text-align: center; line-height: 1rem; font-family: Microsoft YaHei; font-weight: 400; color: #3A3A3A; font-size: 0.4rem; letter-spacing: .1rem;
}
.tmg { display: none; width: 4.7rem; margin: 0 auto; top: -2.1rem; left: 0; right: 0; position: absolute; z-index: 999999; }
.tips { position: absolute; top: 0.49rem; z-index: 11; left: 0; right: 0; margin: 0 auto; width: 9.2rem; height: 1.08rem; line-height: 1.08rem; text-align: center; font-size: 0.34rem; font-family: Microsoft YaHei; font-weight: 400; color: #308AFA; background: #E7EFFF; display: none; }
.yli { /*width: 9.14rem; height: 10rem; position: fixed; margin: auto;   display: none; top: 0; bottom: 0; left: 0; right: 0; z-index: 999999; background-color: #fff; border-radius: 0.2rem;*/

    width: 8.93rem;  position: fixed; z-index: 999999; top: 0; bottom: 0; left: 0; right: 0; margin: auto; border-radius: 0.27rem; display: none; overflow: hidden; background-image: url(../img/tcBg.png); background-size: 100% 100%; background-repeat: no-repeat; background-position: top center;
}
.yclos{ width: 0.85rem; height: 0.85rem; position: absolute; bottom: -1.17rem; background-image: url(../img/close.png); background-size: 100%; background-repeat: no-repeat; background-position: center; left: 0; right: 0; margin: auto;}


.zli span{ display: block; color: #8e1111; font-size: 0.32rem; margin-bottom: 0.2rem;}
.zli{ margin-bottom: 0.2rem;}



.bkdx{
    background: #8e1111;
    text-align: center;
    position: absolute;  left: 0; right: 0; margin: auto;
    font-size: 0.4rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #fff;

    width: 6rem;
    line-height: 1.1rem;
    border-radius: 0.55rem;
    bottom: 0.55rem;
}
.yt{ font-size: 0.5rem; text-align: center;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #404040;
   }
.ytm{ text-align: left;     padding: 0.1rem 0rem 0.62rem 0.1rem; }
.ytm span{ display: block; font-size: 0.46rem;
    font-family: Microsoft YaHei;
    font-weight: bolder;
    line-height: 0.48rem; margin-bottom: 0.56rem; text-align: center; color: #0358CD;}
.ytm p{ font-size: 0.32rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3A3A3A;
    line-height: 0.55rem;}

.cied{ width: 9.14rem; height: 6.32rem; background: #fff; border-radius: 0.26rem; position: fixed; top: -4rem; bottom: 0; left: 0; right: 0; margin: auto; z-index: 999999; text-align: center; display: none; }
.cied em{
    width: 6.24rem;
    height: 1.08rem;
    font-size: 0.44rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3A3A3A;
    line-height: 0.74rem;
    display: block;
    margin: 1.72rem auto 0 auto;
    margin-bottom: 1rem;
    }
.nov span{
    margin: 1.73rem auto 0 auto;
    text-align: center;
    display: block;
    font-size: 0.34rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3A3A3A;
    line-height: 0.74rem;
    padding: 0.5rem;
    background: #fff;
    width: 80%;
    border-radius: 7px;
    }
.nov span i{ display: block; margin-bottom: 0.41rem;
    font-size: 0.46rem;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #3A3A3A;
    line-height: 0.48rem;}
.logo{ width: 2.48rem; height: 0.69rem; margin: 0.42rem auto 0.84rem auto;}
.logo img{ width: 100%; height: 100%; display: block;}
.lput{
    width: 100%;
    text-align: left;
    padding: 0 0.32rem;
    font-size: 0.46rem;
    font-family: Microsoft YaHei;
    font-weight: bold;
    color: #3A3A3A;
    background: #fff;
    width: 6.95rem;
    margin: 0 auto;
    padding: 0.75rem;
    background: linear-gradient(0deg, #F1FCFF 0%, #FFFFFF 100%);
    box-shadow: 0rem 0rem 0rem 0rem rgba(0,3,113,0.2);
    border-radius: 0.25rem;
    margin-bottom: 0.35rem;
    }
.lput input{
    background: none;
    border: none;
    border-radius: 0.13rem;
    width: 5.47rem;
    height: 0.97rem;
    margin-left: 0rem;
    padding: 0 .2rem;
    font-size: 0.4rem;
    border-bottom: 1px solid #ededed;
    }
.logv,.dbox{}

.inh{ margin-bottom: 0.66rem; line-height: 0.97rem; position: relative; height: 0.97rem;}
.inh input{}
.inh em{ line-height: 0.9rem; position: absolute; top: 0.05rem; left: 0;}

.inm{  position: relative; height: 0.97rem;}
.inm input{border: none;border-radius: 0.13rem;width: 5.47rem;height: 0.97rem;margin-left: 0rem;padding: 0 .2rem;font-size: 0.4rem;border-bottom: 1px solid #ededed;}
.inm em{ line-height: 0.9rem; position: absolute; top: 0.05rem; left: 0;}
.inm i{ position: absolute; top: 0rem; right: 0;}

.yzk{
    display: inline-block;
    font-size: 0.34rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #ff5200;
    width: 2.69rem;
    height: 0.97rem;
    line-height: 0.97rem;
    border-radius: 0.13rem;
    text-align: center;
    margin-left: 0.15rem;
    }
.setc,.setc2{ display: none;}

.mox{}
.mox img,.moox img{ height: 4.5rem;}
.ell{ text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}/*单行溢出点点*/




/*--------------------*/
.common_kuang{width: 7.95rem;height: 8.27rem;background: url(../img/tcBg.png) top center;border-radius: 0.26rem;position: fixed;top: -2rem;bottom: 0;left: 0;right: 0;margin: auto;z-index: 999999;text-align: center;display: none;background-size: 100% 100%;}
.common_kuang h4{
    height: 0.59rem;
    font-family: Source Han Sans CN;
    font-weight: bold;
    font-size: 0.59rem;
    color: #FFFFFF;
    text-shadow: 0rem 0rem 0rem #000D71;
    margin-bottom: 0.52rem;
}
.nov{
    height: 7rem;
}
.common_close{ width: 0.85rem; height: 0.85rem; position: absolute; bottom: -1.17rem; background-image: url(../img/close.png); background-size: 100%; background-repeat: no-repeat; background-position: center; left: 0; right: 0; margin: auto;}
.common_btn{
    height: 1.37rem;
    background: #f1dada;
    border-radius: 1rem;
    position: absolute;
    bottom: 0.4rem;
    left: 0;
    right: 0;
    margin: auto;
    font-size: 0.53rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    line-height: 1.37rem;
    width: 7rem;
    color: #d95314;
    background: #f9e400;
    }



/*------------------------------*/
.rbt .ti:nth-child(1){ background-image: url('../img/b/a20.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;}
.rbt .ti:nth-child(2){ background-image: url('../img/d2.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;}
.rbt .ti:nth-child(3){ background-image: url('../img/d6.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;}


/*.rbt .ti:nth-child(1){ background-image: url('../img/d1.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;}
.rbt .ti:nth-child(2){ background-image: url('../img/d2.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;}
.rbt .ti:nth-child(3){ background-image: url('../img/d3.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;}*/




/*背景音乐*/
.def { height: 35px; width: 3rem; text-align: right; position: fixed; top: 25px; right: 20px; z-index: 99999; z-index: 10; color: #fff; font-size: 16px; font-weight: bolder; display: none; }
.def i { font-size: 14px; }
.ius { width: 30px; height: 30px; position: fixed; top: 0.1rem; left: 0.1rem; background-image: url(../img/c4.png); background-size: 100%; background-repeat: no-repeat; background-position: center; z-index: 999; display: none; }
.xuanzhun { -webkit-transition-property: -webkit-transform; -webkit-transition-duration: 1s; -moz-transition-property: -moz-transform; -moz-transition-duration: 1s; -webkit-animation: rotate 3s linear infinite; -moz-animation: rotate 3s linear infinite; -o-animation: rotate 3s linear infinite; animation: rotate 3s linear infinite; }
@-webkit-keyframes rotate { from {
    -webkit-transform: rotate(0deg)
}
    to { -webkit-transform: rotate(360deg) }
}
@-moz-keyframes rotate { from {
    -moz-transform: rotate(0deg)
}
    to { -moz-transform: rotate(359deg) }
}
@-o-keyframes rotate { from {
    -o-transform: rotate(0deg)
}
    to { -o-transform: rotate(359deg) }
}
@keyframes rotate { from {
    transform: rotate(0deg)
}
    to { transform: rotate(359deg) }
}

/*------*/
.vitxt{
    line-height: 0.48rem; color: #fff; padding: 0.1rem 0.2rem;
    background-color: rgba(78, 78, 78, 0.7);
    position: absolute; left: 0; bottom: 0; max-width: 3.5rem;
}
.uvmm{     font-size: 0.34rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3A3A3A;
    line-height: 0.48rem;
    width: 3.7rem;
    text-align: center;
    margin: auto;
}

.uvmm em{ display: block; width: 1.7rem; text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}

.uvmm em:nth-child(1){ float: left;}
.uvmm em:nth-child(2){ float: right;}
.uvmm b{ display: block; clear: both; }

.ell{ text-overflow: ellipsis; white-space: nowrap; overflow: hidden;}/*单行溢出点点*/
.timg{ width: 100%; margin-top: 0.89rem;}
.xqtx{ font-size: 0.43rem; margin-bottom: 0.25rem; text-align: center;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3A3A3A;
    line-height: 0.73rem}

.kqmh{ background-image: url('../img/c8.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; width: 3.45rem; height: 0.78rem; margin: 1rem auto 0 auto;}



.zantt{ font-size: 0.53rem;     margin-bottom: 0.3rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #8a1111;
    line-height: 0.73rem; padding-top: 0.48rem;}
.zanmm{ width: 100%;  padding-top: 0.4rem;
    height: 5.09rem;
    }
.zanmm img{ width: 3.38rem; height: 3.37rem; margin-bottom: 0.3rem;}

.c99{ width: 100% !important; margin-top: 0.62rem !important;}


.k1 em{ display: block; width: 0.93rem; height: 0.54rem; position: absolute; top: -0.3rem; left: 0rem; z-index: 10;
    background-image: url('../img/h1.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;
}
.k2 em{ display: block; width: 0.93rem; height: 0.54rem; position: absolute; top: -0.3rem; left: 0rem; z-index: 10;
    background-image: url('../img/h2.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;
}
.k3 em{ display: block; width: 0.93rem; height: 0.54rem; position: absolute; top: -0.3rem; left: 0rem; z-index: 10;
    background-image: url('../img/h3.png'); background-size: 100%; background-repeat: no-repeat; background-position: center;
}



.page5{ position: fixed; z-index: 999999; top: 0; bottom: 0; left: 0; right: 0; margin: auto;
    width: 9rem;
    height: 11rem;
    background: #FFFFFF;
    border-radius: 0.21rem; display: none;
}
.pppimg{ width: 93%; margin: 1.44rem auto auto auto; display: block;}
.p5ts{ font-size: 0.45rem;
    font-family: Microsoft YaHei; text-align: center; margin-top: 1.28rem;
    color: #8a1111; font-weight: bold;}
.p5mm{
    font-size: 0.36rem; text-align: center; margin-top: 0.76rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3A3A3A;
}

.p5close{ width: 0.8rem; height: 0.8rem;  background-image: url('../img/close.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; position: absolute; top: 0.26rem; right: 0.26rem;}



.page6{ position: fixed; z-index: 999999; top: 0; bottom: 0; left: 0; right: 0; margin: auto;
    width: 9rem;
    height: 12.5rem;
    border-radius: 0.21rem; display: none;

    background-image: url(../img/bacg.png); background-size: 100% 100%; background-repeat: no-repeat; background-position: top center;
}
.p6close{ width: 0.8rem; height: 0.8rem;  background-image: url('../img/close.png'); background-size: 100%; background-repeat: no-repeat; background-position: center; position: absolute; top: 0.1rem; right: 0.1rem;}
.p6m{ font-size: 0.45rem; text-align: center; margin-top: 0.5rem;  margin-bottom: 0.5rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #8a1111;}
.page6 p{ padding-left: 0.4rem; box-sizing: border-box; font-size: 0.32rem; line-height: 0.6rem;
    font-family: Microsoft YaHei;
    font-weight: 400;
    color: #3A3A3A;}


/*-------保利云上-----*/
.seachSum{ width: 100%; margin-top: 0.48rem; margin-bottom: 0.64rem; font-size: 0.36rem;}
.seachSum input{
    width: 5.42rem;
    height: 1.08rem;
    border: 1px solid #0358CD;
    border-radius: 0.18rem; padding: 0 0.25rem; margin-left: 0.29rem; font-size: 0.36rem;
}
.seachSum span{
    width: 3.01rem;
    line-height: 1.06rem;
    background: #0358CD;
    border-radius: 0.18rem;
    display: inline-block;
    color: #fff; text-align: center;
}

.apd2{ display: none;}
.apd2 .vi{ margin-bottom: 0.1rem !important;}
/*--------------------*/

/*转盘*/
.popup12 { width: 100%; position: relative; height: 10rem; }
.luck { position: relative; top: 1rem; width: 9rem; height: 9rem; margin: auto; z-index: 9; }
.turntable { display: block; width: 9rem; height: 9rem; margin: 0 auto; }
.luckBtn { display: block; width: 1.8rem; height: 2.45rem; position: absolute; cursor: pointer; top: -0.5rem; bottom: 0; left: 0; right: 0; margin: auto; }
.kbtt { display: none; width: 1.8rem; height: 2.45rem; position: absolute; cursor: pointer; top: -0.5rem; bottom: 0; left: 0; right: 0; margin: auto; }
.luck_tp { width: 9rem; height: 0.8rem; margin: auto; z-index: 10; background-image: url(../img/b/a4.png); background-size: 100% 100%; background-repeat: no-repeat; background-position: center; position: absolute; bottom: -0.35rem; z-index: 1; }

.fhbtn{     background-color: #8e1111;
    color: #fff;
    text-align: center;
    width: 4rem;
    line-height: 0.85rem; font-size: 0.38rem;
    border-radius: 0.4rem;
    margin: 0.89rem auto auto auto;}
.hdjp{ display: block; width: 3rem; height: 3rem; margin: 0.5rem auto;}


.cjts{ width: 5rem; text-align: center; color: #8a1111; margin: auto; font-size: 0.42rem; height: 1rem; line-height: 0.9rem; border-radius: 0.5rem; background-image: url(../img/b/a19.png); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;}



.ytmm{ width: 100%;  text-align: center; max-height: 6.8rem; overflow-y: scroll;}
.ytmm img{ display: inline-block; width: 1.8rem; margin-right: 0.17rem;}
.ytmm img:nth-child(4n){ margin-right: 0;}




.zmain{ position: relative; left: 0; right: 0; top: 5.5rem; z-index: 333; margin: 0 auto;}
.zjdp{
    width: 100%;
    position: relative;
    top: -0.5rem;
    z-index: 1;
    padding-bottom: 1rem;
}
.jlist{
    width: 8.72rem;
    min-height: 3rem;
    background: #FFFFFF;
    border-radius: 0.19rem;
    padding: 0.36rem 0.32rem;
    margin: -1rem auto 0.51rem auto;
}
.jlist h3{
    font-weight: 400;
    font-size: 0.37rem;
    color: #303030;
    margin-bottom: 0.27rem;
}
.subbtn{
    width: 8.72rem;
    margin: auto;
    line-height: 1.11rem;
    text-align: center;
    color: #fff;
    font-size: 0.32rem;
    background-color: #FF7800;
    border-radius: 0.12rem;
}
.jli{
    width: 8.08rem;
    min-height: 2.76rem;
    background: #FFFFFF;
    border-radius: 0.13rem;
    border: 0.01rem solid #EDEDED;
    padding: 0.21rem;
    display: flex;
    margin-bottom: 0.16rem;
}
.jli img{
    width: 3.09rem;
    height: 2.31rem;
    border-radius: 0.05rem;
    margin-right: 0.17rem;
}
.jright{
    padding-top: 0.3rem;
    flex: 1;
    position: relative;
}
.jright i{
    display: inline-block;
    width: 0.37rem;
    height: 0.37rem;
    position: absolute;
    top: -0.1rem;
    right: -0.1rem;
    background-image: url('../img/a1.png'); background-size: 100% 100%; background-repeat: no-repeat; background-position: center;
}
.jright p:nth-child(1){
    font-weight: bold;
    font-size: 0.32rem;
    color: #303030;
    margin-bottom: 0.15rem;
}
.jright p:nth-child(2){
    font-weight: 400;
    font-size: 0.32rem;
    color: #696969;
    line-height: 0.37rem;
    margin-bottom: 0.15rem;
}
.jright p:nth-child(3){
    font-weight: 400;
    font-size: 0.32rem;
    color: #FF5200;
    line-height: 0.37rem;
}
.jia{
    background-image: url('../img/a1.png');
}
.jib{
    background-image: url('../img/a2.png') !important;
}
.ztbtn,.searchMD{
    width: 7.82rem;
    line-height: 1.2rem;
    text-align: center;
    font-size: 0.32rem;
    color: #303030;
    margin: 0.3rem auto auto auto;
    background-color: #FFE772;
    border-radius: 0.2rem;
}
.fixed{
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    margin: auto;
    z-index: 99999999;
}













