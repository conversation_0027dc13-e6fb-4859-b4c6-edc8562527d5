* {
	margin: 0;
	padding: 0;
}

body {}

img {
	width: 100%;
	height: 100%;
}

.page {}

li {
	list-style: none;
}

.page-cont{
	
margin-top: -4.7rem;
}

/* 顶部广告 */
.page-banner-img {
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/banner.png) top center;
	background-size: 100% 100%;
	height: 16.51rem;
}

/* 热度浏览量+时间控件 start*/
.msg-time-pageview {
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/bg2.png) top center;
	background-size: 100% 100%;
	padding: 0.31rem;
	border-radius: 0.21rem;
}

.msg-time-pageview-cont1 {
	overflow: hidden;
	border-bottom: 0.03rem solid #fff;
	padding-bottom: 0.26rem;
	margin-bottom: 0.26rem;
}

.msg-time-pageview-title {
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/heat.png) top center;
	background-size: 100% 100%;
	width: 2.72rem;
	height: 0.74rem;
	float: left;
}

.msg-time-pageview-time {
	float: right;
	margin-top: 0.18rem;
	font-size: 0.31rem;
}

.msg-time-pageview-cont2 {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-evenly;
}

.msg-time-pageview-cont2-2 {
	text-align: center;
}

.msg-time-pageview-text {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
	line-height: 0.31rem;
	margin-bottom: 0.3rem;
}

.msg-time-pageview-num {
	height: 0.41rem;
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: bold;
	font-size: 0.41rem;
	color: #FF4E00;
	line-height: 0.41rem;
}

/* 热度浏览量+时间控件  end*/

/* 助力控件 */
.help-cont-icon {
	display: block;
	width: 1.19rem;
	height: 1.59rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/mark.png);
	background-size: 100% 100%;
	position: absolute;
	right: 0;
	top: 0;
}

.help-cont {
	padding: 0.31rem;
	border-radius: 0.21rem;
	background: #fff;
}

.help-cont .help-cont2 img {
	width: auto;
	height: 1.1rem;
	margin: 0 auto;
	display: block;
	cursor: pointer;
	margin-bottom: 0.3rem;
}

.help-cont .help-cont3 {
	height: 0.31rem;
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: 400;
	font-size: 0.31rem;
	color: #303030;
	line-height: 0.31rem;
	text-align: center;
}


/* 图片切换控件--1大图+4缩略图 start*/
.page .img-swiper-thumbnail-4small-swiper1 {
	overflow: hidden;
	position: relative;
	padding: 0.31rem;
	width: 8.58rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
	background: #fff;
}

.img-swiper-4-big {
	height: 6.44rem;
	margin-bottom: 0.3rem;
}

.img-swiper-4-small {
	height: 1.44rem;
}

.img-swiper-4-small .swiper-slide {
	height: 94%;
}

.swiper-button-next:after,
.swiper-button-prev:after {
	content: '' !important;
}

.img-swiper-thumbnail-4small-swiper1 .swiper-wrapper {
	margin-bottom: 0.13rem;
}

.img-swiper-thumbnail-4small-swiper2 {}

.img-swiper-thumbnail-4small-swiper2 .swiper-wrapper {
	/* width: 102%; */
	overflow: hidden;
}

.img-swiper-thumbnail-4small-swiper2 .swiper-wrapper .swiper-slide {
	height: 1.9rem;
	width: 25%;
}

.img-swiper-thumbnail-4small-swiper2 .swiper-wrapper .swiper-slide img {}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-next {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-r.png) top center;
	background-size: 100% 100%;
	right: 20px;
	cursor: pointer;
	top: 56%;
}

.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-prev {
	width: 0.77rem;
	height: 0.77rem;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/sw-l.png) top center;
	background-size: 100% 100%;
	left: 20px;
	cursor: pointer;
	top: 56%;
}

.img-swiper-4-big-p {
	font-family: Microsoft YaHei, Microsoft YaHei;
	font-weight: bold;
	font-size: 0.36rem;
	color: #303030;
	line-height: 0.36rem;
	margin-bottom: 0.2rem;
}

.img-swiper-thumbnail-4small .mySwiper {
	width: 100%;
	height: 1.9rem;
	margin-left: auto;
	margin-right: auto;
}

.img-swiper-thumbnail-4small .img-swiper-thumbnail-4small-swiper1 {
	height: 80%;
	width: 100%;
}

.img-swiper-thumbnail-4small .mySwiper .swiper-slide {
	width: 25%;
	height: 100%;
}

.img-swiper-thumbnail-4small .mySwiper .swiper-slide-thumb-active {
	opacity: 1;
}

.img-swiper-thumbnail-4small .swiper-slide img {
	display: block;
	width: 100%;
	height: 100%;
	object-fit: cover;
}

/* 图片切换控件--1大图+4缩略图 end*/




/* Tab控件（新房楼盘列表+tab） start*/
.new-house-swiper {
	width: 100%;
	margin: 0 auto;
}

.new-house-swiper-list {}

.new-house-swiper-list li {
	padding: 0.26rem 0;
	border: hidden;
	border-bottom: 1px solid #ededed;
	width: 96%;
	margin: 0 auto;
}

.new-house-swiper-list li:last-child {
	border-bottom: none
}

.new-house-swiper-list li .newhouse_list {
	width: 100%;
	overflow: hidden;
	text-decoration: none;
	display: block;
}

.new-house-swiper-list li .newhouse_list .newhouse_left {
	position: relative;
	width: 2.87rem;
	height: 2.15rem;
	background: #434343;
	border-radius: 0.05rem;
	float: left;
}

.new-house-swiper-list li .newhouse_list .newhouse_right {
	margin-left: 0.38rem;
	width: 5.3rem;
	position: relative;
	top: 0.05rem;
	float: left;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_right_top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_title {
	font-size: 0.4rem;
	font-weight: 700;
	color: #222222;
	line-height: 0.4rem;
	margin-bottom: 0.15rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_address {
	font-size: 0.29rem;
	color: #222222;
	line-height: 0.31rem;
	margin-bottom: 0.18rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_address span {}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_address span+span {}

.new-house-swiper-list li .newhouse_list .newhouse_right .unique_list {
	margin-bottom: 0.2rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .unique_list .unique_item {
	font-size: 0.29rem;
	padding: 0.02rem 0.11rem;
	border-radius: 2px;
	text-align: center;
	margin-right: 0.21rem;
	height: 0.36rem;
	line-height: 0.36rem;
	display: inline-block;
	color: #555555;
	background: #F4F4F4;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_price .rise {
	color: #B0B0B0;
	font-size: 0.31rem;
	font-weight: normal;
	margin-left: 0.1rem;
}

.new-house-swiper-list li .newhouse_list .newhouse_right .newhouse_price .newhouse_unit {
	font-weight: normal;
	font-size: 0.31rem;
}

.new-house-swiper-nav {
	/* position: absolute; */
	/* left: 0; */
	/* width: 100%; */
	/* top: 1.1rem; */
	/* overflow: hidden; */
	margin-left: -0.3rem !important;
}

.new-house-swiper-nav .new-house-swiper-cont {
	overflow-x: scroll;
	white-space: nowrap;
	padding-right: 0.5rem;
}

.new-house-swiper-nav  .swiper-slide{
	text-align: center;
	height: 0.77rem;
	background: #EBEBEB;
	padding: 0 0.26rem;
	width: auto !important;
	line-height: 0.77rem;
	border-radius: 0.05rem;
	margin-left: 0.31rem;
	margin-right: 0 !important;
	font-family: PingFang SC, PingFang SC;
	font-weight: 400;
	font-size: 0.41rem;
	color: #555555;
	display: inline-block;
	cursor: pointer;
}

/* Tab控件（新房楼盘列表+tab） end*/


/* 新房房源列表 */
.new-house-list {
	width: 100%;
	margin: 0 auto;
}

.new-house-list li {
	padding: 0.26rem 0;
	border: hidden;
	border-top: 1px solid #ededed;
	width: 100%;
	margin: 0 auto;
}
.new-house-list li:first-child {border-top: none}
.new-house-list>ul+ul{display: none;}
.new-house-list li:last-child {
	
}

.new-house-list li .newhouse_list {
	width: 100%;
	overflow: hidden;
	text-decoration: none;
	display: block;
}

.new-house-list li .newhouse_list .newhouse_left {
	position: relative;
	width: 2.87rem;
	height: 2.15rem;
	background: #434343;
	border-radius: 0.12rem;
	float: left;
	overflow: hidden;
}

.new-house-list li .newhouse_list .newhouse_right {
	margin-left: 0.38rem;
	width: 5.3rem;
	position: relative;
	top: 0.05rem;
	float: left;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_right_top {
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 100%;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_title {
	font-size: 0.4rem;
	font-weight: 700;
	color: #222222;
	line-height: 0.4rem;
	margin-bottom: 0.18rem;
	text-overflow: ellipsis;
	overflow: hidden;
	white-space: nowrap;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_address {
	font-size: 0.29rem;
	color: #222222;
	line-height: 0.31rem;
	margin-bottom: 0.18rem;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_address span {}

.new-house-list li .newhouse_list .newhouse_right .newhouse_address span+span {}

.new-house-list li .newhouse_list .newhouse_right .unique_list {
	margin-bottom: 0.15rem;
}

.new-house-list li .newhouse_list .newhouse_right .unique_list .unique_item {
	font-size: 0.29rem;
	padding: 0.02rem 0.11rem;
	border-radius: 2px;
	text-align: center;
	margin-right: 0.21rem;
	height: 0.36rem;
	line-height: 0.36rem;
	display: inline-block;
	color: #555555;
	background: #F4F4F4;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_price .rise {
	color: #B0B0B0;
	font-size: 0.31rem;
	font-weight: normal;
	margin-left: 0.1rem;
}

.new-house-list li .newhouse_list .newhouse_right .newhouse_price .newhouse_unit {
	font-weight: normal;
	font-size: 0.31rem;
}



/* 二手房房源列表  start*/
.second-house {}

.second-house-list {
	margin-top: -0.5rem;
}

.second-house-list li:first-child a {
	border-top: none
}

.second-house-link {
	display: block;
	color: #000;
	text-decoration: none;
	position: relative;
	padding: 15px 0 !important;
	padding-bottom: 12px !important;
	border-top: 1px solid #e8e8e8;
}

.second-house-img {
	float: left;
	width: 108px;
	height: 82px;
	overflow: hidden;
	position: relative;
	margin-right: 10px;
	border-radius: 5px;
}

.second-house-right {
	height: auto;
	float: left;
	width: 55%;
}

.left_tu {}

.second-house-title {
	font-size: 0.4rem;
	font-weight: 700;
	color: #222222;
	line-height: 0.5rem;
	margin-bottom: 0.18rem;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: normal;
}

.second-house-area {
	font-size: 0.29rem;
	color: #222222;
	line-height: 0.31rem;
	margin-bottom: 0.18rem;
}

.second-house-charact {
	margin-bottom: 0.15rem;
}

.second-house-charact-li {
	font-size: 0.29rem;
	padding: 0.02rem 0.11rem;
	border-radius: 2px;
	text-align: center;
	margin-right: 0.1rem;
	height: 0.36rem;
	line-height: 0.36rem;
	display: inline-block;
	color: #555555;
	background: #F4F4F4;
}

.second-house-bottom {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.55rem;
}

.second-house-price {
	font-size: 0.46rem;
	font-weight: 900;
	color: #FF6F28;
	font-family: arial;
	margin-top: 0.1rem;
	line-height: 0.46rem;
	float: left;
	margin-right: 0.2rem;
}

.second-house-price i {
	font-weight: 400;
	font-style: normal;
	font-size: 12px;
}

.second-house-info {
	font-size: 0.28rem;
	line-height: 0.36rem;
	color: #545454;
	margin-top: 9px;
	float: left;
	font-family: "微软雅黑";
	font-weight: normal;
}

.address_span span {
	float: left;
	font-size: 11px;
}

.cl {
	clear: both;
}

/* 二手房房源列表  end*/



.help-cont3 b {
	color: #F84D6B;
	font-size: 0.41rem;
	padding: 0 0.05rem;
}




.fxe_alert span {
	display: inline-block;
	background: url(https://static.fangxiaoer.com/web/images/ico/sign/b50.png);
	padding: 6px 16px;
	border-radius: 4px;
	font-size: 12pt;
	color: #fff;
}

.fxe_alert {
	text-align: center;
	margin: 0 auto;
	display: inline-block;
	width: 6.6rem;
	position: fixed;
	top: 40%;
	z-index: 999999;
	display: none;
	left: 50%;
	margin-left: -3.3rem;
}

body {
	background-image: linear-gradient(to bottom, #FFE3A5, #ffffff);
}

.pageW {
	width: 9.18rem;
	margin: 0 auto;
	margin-bottom: 0.41rem;
}

.new-house-list {
	width: 9.18rem;
}

.new-house-swiper .page-title {
	/* padding-bottom: 0.91rem; */
}

.new-house-swiper-nav div.hover {
	background: #FF8C07;
	color: #fff;
}

.new-house-swiper {
	position: relative;
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
}

.img-swiper-thumbnail-4small {
	margin-bottom: 0.41rem;
	overflow: hidden;
	background: #fff;
	padding: 0.31rem;
	width: 8.58rem;
	border-radius: 0.21rem;
}

.new-house-swiper,
.second-house {
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
	padding: 0.31rem;
	width: 8.58rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
	overflow: hidden;
}
.cont-13{
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
	padding: 0.31rem;
	width: 8.58rem;
	border-radius: 0.21rem;
	margin-bottom: 0.41rem;
}
.new-house-list {
    overflow: hidden;
}

.page-title {
	margin-bottom: 0.31rem;
	font-size: 0.41rem;
	font-weight: bold;
	line-height: 0.42rem;
}

.page-title img {
	height: 0.54rem;
	width: auto;
}

.help-cont {
	width: 8.56rem;
	margin-bottom: 0.41rem;
	position: relative;
}

.swiper-slide-thumb-active {
	border: 2px solid #ff5200;
}

.swiper-button-prev:after,
.swiper-button-next:after {
	content: ''
}

.msg-time-pageview {
	width: 8.58rem;
}

/* ::-webkit-scrollbar {
	display: none;
} */

@media (min-width: 750px) {
	.new-house-swiper .page-title {
		/* padding-bottom: 115px; */
	}
.new-house-list li .newhouse_list .newhouse_right{
    width: 600px;
}

}


.more-new,
.more-sec {
	display: none;
	width: 2.72rem;
	display: block;
	height: 0.92rem;
	margin: 0 auto;
	background: url(https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more1.png) top center;
	background-size: 100% 100%;
	cursor: pointer;
}

/* 现场图片-6 东坡 start*/
.module-6-container {
	display: flex;
	flex-direction: column;
	background-color: white;
	height: auto;
	border-radius: 0.21rem;
	width: 8.58rem;
	padding: 0.31rem;
}

.module-gradient {
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);
}

.module-6-content {
	border-radius: 0.21rem;
	background-color: white;
	height: auto;
	padding-bottom: 0.1rem;
}

.module-6-grid-content {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	gap: 0.31rem;
	height: auto;
}

.module-6-page>div:nth-child(n+7) {
	display: none;
}
.module-6-morebtn {}

.module-8-page>li:nth-child(n+7) {
	display: none;
}
.module-8-morebtn {}

.module-6-grid-item {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: space-evenly;
	height: auto;
	overflow: hidden;
}

.xc-container-item-img {
	width: 100%;
	max-height: 3.1rem;
	overflow: hidden;
	border-radius: 0.1rem;
	flex-shrink: 1;
	object-fit: cover;
}

.xc-container-item-title {
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	overflow: hidden;
	text-overflow: ellipsis;
	word-break: break-all;
	font-weight: 400;
	font-size: 0.31rem;
	font-family: Microsoft YaHei, Microsoft YaHei;
	color: #303030;
	margin-top: 0.1rem;
	flex: 1;
}


/* 现场图片-6 东坡 end */

/* 参展品牌-17、参展项目-18、参展中间-19 东坡 start */
.canzhan-container {
	display: grid;
	grid-template-columns: repeat(3, 1fr);
	gap: 0.33rem;
	/* margin: 0rem 0.31rem 0rem 0.31rem; */
	/* margin-top: -0.31rem; */
}

.canzhan-container-item {
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	/* height: 1.09rem; */
	border-radius: 0.1rem;
	border: 0.03rem solid #EDEDED;
}

.canzhan-container-item-container-text {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	border-radius: 0.1rem;
	border: 0.03rem solid #EDEDED;
	/* min-height: 1rem; */
}

.canzhan-item-container {
	display: flex;
	align-items: center;
	justify-content: center;
	overflow: hidden;
	border-radius: 8rpx;
	border: 2rpx solid #EDEDED;
	min-height: 1.2rem;
}

.canzhan-container-item-text {
	-webkit-line-clamp: 2;
	text-align: center;
	margin: 0.2rem;
}

.canzhan-more-btn {
	height: 0.95rem;
	width: 2.85rem;
	margin: 22rpx 0rpx 0rpx 0rpx;
	cursor: pointer;
}

.canzhan-more-padding {
	margin-top: 0.31rem;
}

.pinpai>div:nth-child(n+16) {
	display: none;
}

	{}

/* 参展品牌显示个数限制15个 */
.xiangmu>div:nth-child(n+16) {
	display: none;
}

	{}

/* 参展项目显示个数限制 */
.zhongjie>div:nth-child(n+16) {
	display: none;
}

	{}

/* 参展中介显示个数限制 */
.pinpai-item {}

.xiangmu-item {}

.zhongjie-item {}

.pinpai-morebtn {}

/* 参展品牌更多按钮 */
.xiangmu-morebtn {}

/* 参展项目更多按钮 */
.zhongjie-morebtn {}

/* 参展中介更多按钮 */
/* 参展品牌-17、参展项目-18、参展中间-19 东坡 end */

.zhiji-padding {
	/* width: 8.6rem; */
	/* height: 7rem; */
	margin: 0 auto;
}

/* 现场直击 - 10 东坡 start */



/* 小程序码 - 14 东坡 start */
.mini-qrcode-14-container {
	display: flex;
	flex-direction: row;
	align-items: center;
	height: 100%;
	overflow: hidden;
	background: linear-gradient(134deg, #FFFFFF 0%, #FFF9EA 81%, #FFFFFF 100%);

}

.mini-qrcode-14-text {
	flex: 1;
	height: 100%;
	font-weight: bold;
	font-size: 0.36rem;
	color: #303030;
	text-align: left;
	font-style: normal;
	text-transform: none;
	line-height: 0.8rem;
	margin-left: 0.65rem;
}

.mini-qrcode-14-img {
	margin: 0.31rem 0.87rem 0.31rem 0.31rem;
	height: 2.26rem;
	width: 2.26rem;
}

/* 领导讲话卡片墙 - 9 东坡 start */
.module-9-content {
	border-radius: 0.21rem;
	background-color: white;
	height: auto;
}

.swiper-container-card-wall {
	width: 8.06rem;
	height: 12.09rem;
	overflow: hidden;
}

.swiper-slide-active {
	transform: rotateY(0deg);
}

.swiper-slide-prev,
.swiper-slide-next {
	/* transform: rotateY(90deg); */
	backface-visibility: hidden;
}

.card-wall-img {
	border-radius: 0.1rem 0.1rem 0.1rem 0.1rem;
	overflow: hidden;
	object-fit: cover;
}

/* 抽奖 - 24 东坡 start */
.choujiang-image {
	width: 100%;
	height: 100%;
}

.choujiang-container {
	margin: 0.31rem;
}

.choujiang-container-14 {
	margin: 0.31rem;
}

.cont-24-qrcode {
	position: absolute;
	display: none;
	/* width: 3rem; */
	height: 3.65rem;
	margin-left: 3.1rem;
	margin-top: -2.5rem;
}
.code-img-24 {
	position: absolute;
	margin: 0.3rem;
	top: 0rem;
	left: 0rem;
	height: 2.3rem;
	width: 2.3rem;
}
.cont-14-qrcode {
	position: absolute;
	display: none;
	/* width: 3rem; */
	height: 3.65rem;
	margin-left: 3.1rem;
	margin-top: -2.5rem;
}
.code-img-14 {
	position: absolute;
	margin: 0.3rem;
	top: 0rem;
	left: 0rem;
	height: 2.3rem;
	width: 2.3rem;
}

/* 抽奖 - 24 东坡 end */



/* 适配pc端 */

@media (min-width: 750px) {

	body,
	.page {
		max-width: 100%;
	}
	.page-cont{
		margin-top:0
	}
	.page .pageW {
		width: 1106px;
		margin-bottom: 48px;
		padding: 35px;
	}

	.page-banner-img {
		background: url(https://event.fangxiaoer.com/static/2024/m0820/img/banner2.png) top center;
		background-size: 100% 100%;
		height: 240px;
		min-width: 1170px;
	}

	.img-swiper-4-big {
		height: 774px;
	}

	.img-swiper-4-small {
		height: 174px;
	}

	.img-swiper-4-small .swiper-slide {
		height: 94%;
	}

	.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-next,
	.img-swiper-thumbnail-4small-swiper1 .img-swiper-thumbnail-4small-swiper1-prev {
		width: 94px;
		height: 94px;
	}

	.msg-time-pageview {}

	body .page-title img {
		height: 59px
	}

	.help-cont .help-cont3 {
		font-size: 32px;
	}

	.more-new,
	.more-sec {
		width: 327px !important;
		height: 108px !important;
		margin-top: 20px !important;
	}

	.help-cont .help-cont3 b {
		font-size: 43px;
		color: #F84D6B;
	}

	.page .msg-time-pageview {
		margin-top: 0;
	}

	.new-house-swiper-nav  .swiper-slide {
		height: 94px;
		line-height: 94px;
		padding: 0 55px;
		font-size: 44px  !important;
		margin-left: 34px;
	}

	.new-house-list li .newhouse_list .newhouse_left,
	.second-house-img {
		width: 346px;
		height: 258px;
		margin-right: 30px;
	}

	.second-house-link {
		padding: 30px 0 !important;
	}

	.page .new-house-list {
		width: 100%;
	}

	.new-house-swiper-nav {
		/* top: 120px; */
		margin-left: -34px !important;
	}

	.new-house-swiper .page-title {}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_title,
	.second-house-title {
		font-size: 44px;
		line-height: 44px;
		margin-bottom: 25px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_address,
	.second-house-area {
		font-size: 32px;
		line-height: 32px;
		margin-bottom: 25px;
		white-space: nowrap;
	}

	.new-house-list li .newhouse_list .newhouse_right .unique_list .unique_item,
	.second-house-charact-li {
		line-height: 50px;
		height: 50px;
		font-size: 27px;
		padding: 0 19px;
		margin-bottom: 20px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_price,
	.second-house-price {
		font-size: 50px;
		line-height: 50px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_price .newhouse_unit,
	.second-house-price i {
		font-size: 32px;
	}

	.new-house-list li .newhouse_list .newhouse_right .newhouse_price .rise {
		font-size: 32px;
		margin-left: 010px;
	}

	.new-house-list li，.second-house-link {
		padding: 32px 0;
		border-bottom: 5px solid #ededed;
	}

	.second-house-info {
		line-height: 58px;
		font-size: 32px;
	}

	.img-swiper-4-big-p {
		font-size: 39px;
		line-height: 39px;
		margin-bottom: 20px;
	}

	.msg-time-pageview-time,
	.msg-time-pageview-text {
		font-size: 32px;
		line-height: 32px;
		margin-bottom: 26px;
	}

	.msg-time-pageview-time {
		margin-bottom: 0;
	}

	.msg-time-pageview-num {
		font-size：44px;
		font-size: 44px;
		line-height: 44px;
	}



	.module-6-container {
		padding-bottom: 1.23rem;
	}


	.xc-container-item-img {
		max-height: 9.57rem;
	}

	.xc-container-item-title {
		font-size: 0.78rem;
		margin-top: 0.2rem;
	}
	.page-title{
    font-size: 1.13rem;
    line-height: 1.15rem;
    margin-bottom: 0.5rem;
}
	.canzhan-container {
		gap: 1.05rem;
		/* margin: 0rem 0.92rem 0rem 0.92rem; */
		/* margin-top: -0.92rem; */
	}

	.canzhan-container-item>a {
		height: 3.2rem;
	}

	.canzhan-container-item-text {
		margin: 0.4rem;
	}

	.canzhan-more-btn {
		height: 2.9rem;
		width: 8.38rem;
		margin: 22rpx 0rpx 0rpx 0rpx;
	}

	.choujiang-container {
		margin: -0.7rem;
	}

	.choujiang-container-14 {
		margin: -0.7rem;
	}

	.cont-24-qrcode {
		width: auto;
		height: 4.8rem;
		margin-left: 7.6rem;
		margin-top: -4.1rem;
	}

	.code-img-24 {
		position: absolute;
		margin: 0.44rem;
		height: 3rem;
		width: 3rem;
	}

	.cont-14-qrcode {
		width: auto;
		height: 4.8rem;
		margin-left: 7.6rem;
		margin-top: -4.1rem;
	}

	.code-img-14 {
		position: absolute;
		margin: 0.44rem;
		height: 3rem;
		width: 3rem;
	}

	.canzhan-more-padding {
		margin-top: 0.51rem;
	}

	.canzhan-container-item-container-text {
		display: flex;
		flex-direction: column;
		align-items: center;
		justify-content: center;
		justify-items: center;
		overflow: hidden;
		border-radius: 0.1rem;
		border: 0.03rem solid #EDEDED;
		/* min-height: 1rem; */
	}

	.canzhan-item-container {
		min-height: 2rem;
	}

	.mini-qrcode-14-text {
		font-size: 1rem;
		line-height: 2.5rem;
		margin-left: 1.05rem;
	}

	.mini-qrcode-14-img {
		margin: 0.95rem 2rem 0.95rem 0.95rem;
		height: 5rem;
		width: 5rem;
	}

	.swiper-container-card-wall {
		width: 18.4rem;
		height: 27.6rem;
		margin: 0 auto;
	}

}





/* 政策start 文天 */
.image-full {
	flex-grow: 1;
	width: 100%;
	height: 100%;
}

.module-bgContainer-list1 {
	display: flex;
	height: auto;
	flex-direction: row;
	background-color: white;
	border-radius: 0.21rem;
	padding: 0.31rem;
	width: 8.58rem;
	cursor: pointer;
}

.list-item-container1 {
	margin-top: 24px;
	margin-bottom: 24px;
	margin-left: 24px;
	display: flex;
	height: auto;
	background: #FFFFFF;
	width: auto;
}

.list-item-image1 {
	width: 3.59rem;
	height: 2.69rem;
	overflow: hidden;
	border-radius: 4px;
}

.list-item-right1 {
	flex: 1;
	/* margin-top: 5px; */
	flex-direction: column;
	margin-left: 0.31rem;
}

.list-item-right-title1 {
	font-weight: bold;
	font-size: 0.41rem;
	color: #303030;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
}

.list-item-right-subtitle1 {
	margin-top: 12px;
	font-weight: 500;
	font-size: 0.31rem;
	color: #303030;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	width: 95%;
}

/* 政策end */


/* 现场政策start */

.module-bgPolicy {
	background-image: linear-gradient(#FFF9EA, #FFFFFF);
	border-radius: 15rpx;
	margin: 0.41rxm;
}

.module-bgContainer2 {
	background-image: linear-gradient(#FFF9EA, #FFFFFF);
	border-radius: 0.21rem;
	padding: 0.31rem;
	width: 8.58rem;
}

.module-item-bgPolicy {
	display: flex;
	align-items: flex-start;
	width: auto;
	flex: 1;
	height: auto;
	cursor: pointer;
}

.list-item-title-policy {
	font-weight: bold;
	font-size: 0.36rem;
	color: #303030;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 5;
	-webkit-box-orient: vertical;
	flex: 1;
	margin-top: 0.26rem;
}

.list-item-image-policy {
	width: 4rem;
	height: 3rem;
	overflow: hidden;
	margin-left: 15px;
	border-radius: 4px;
}

.line-view {
	margin-bottom: 0.26rem;
	margin-top: 0.26rem;
	background-color: #EDEDED;
	height: 1px;
}

/* 现场政策end */



/* 发评论start */
.input_bgContainer {
	display: flex;
	flex-direction: row;
	margin-top: 0.4rem;
}

.input-style {
	flex-grow: 2;
	border: 1px solid #EBEBEB;
	/* 边框样式 */
	border-top-left-radius: 0.15rem;
	border-bottom-left-radius: 0.15rem;
	background-color: #FFFFFF;
	/* 设置背景色 */
	color: #333333;
	height: auto;
	padding-left: 0.3rem;
	padding-top: 0.2rem;
	padding-bottom: 0.2rem;
	font-size: 0.3rem;
}

.send-bg {
	flex-grow: 1;
	background-color: #FF5200;
	color: #FFFFFF;
	font-size: 0.3rem;
	padding-top: 0.2rem;
	padding-bottom: 0.2rem;
	border-top-right-radius: 0.15rem;
	border-bottom-right-radius: 0.15rem;
	display: flex;
	justify-content: center;
	align-items: center;
}
.cont-15-ul{
    margin-top: -0.2rem;
    margin-bottom: 0.21rem;
}
.cont-15-ul li:first-child{border-top: none}
.cont-15-li{
    overflow: hidden;
    padding: 0.21rem 0;
    border-top: 1px solid  #ededed;
}
.cont-15-img{
    width: 1.31rem;
    height: 1.31rem;
    display: block;
    float: left;
    border-radius: 1.31rem;
    overflow: hidden;
    margin-right: 0.3rem;
}
.cont-15-img img{}
.cont-15-div{
    float: left;
    width: 6rem;
}
.cont-15-h5{
    font-size: 0.36rem;
    line-height: 0.4rem;
    margin-bottom: 0.2rem;
}
.cont-15-p{
    font-size: 0.31rem;
    line-height: 0.4rem;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.input_bg{
    width: 100%;
    height: 0.92rem;
    background: url(../img/input-bg.png)  top center;
    background-size: 100% 100%;
    cursor: pointer;
    position: relative;
}
.input_msg{
    position: absolute;
    bottom: 2rem;
    left: 50%;
    width: 8rem;
    margin-left: -4rem;
    background: #000000db;
    text-align: center;
    border-radius: 0.5rem;
    padding: 0.8rem 0.5rem;
    color: #fff;
    display: none;
}
.input_msg h5{font-size: 0.4rem;margin-bottom: 0.2rem;}
.input_msg p{
    font-size: 0.32rem;
}


/* 发评论end */
@media (min-width: 750px) {
	.input_msg{
    width: 14rem;
    margin-left: -7rem;
    border-radius: 0.5rem;
    padding: 0.8rem 0.5rem;
    bottom: 4rem;
}
	.input_msg h5{font-size: 0.8rem;margin-bottom: 0.8rem;}
	.input_msg p{
    font-size: 0.5rem;
}
	.cont-15-ul{
	   
	}
	.cont-15-li{
	  padding: 0.5rem 0;
	}
	.cont-15-img{
	   
width: 4.1rem;
	   
height: 4.1rem;
	   
border-radius: 4.1rem;
	}
	.cont-15-img img{}
	.cont-15-div{
	    
width: 14rem;
	}
	.cont-15-h5{
	 
font-size: 1rem;
	 
line-height: 1.2rem;
	}
	.cont-15-p{
	  
font-size: 0.82rem;
	  
line-height: 1.02rem;
	}
	
	.input_bg{
	   height: 2.2rem;
	}
}




/* 小二帮帮忙start */
.module-bgContainer3 {
	background-image: linear-gradient(#FFF9EA, #FFFFFF);
	border-radius: 0.21rem;
	padding: 0.31rem;
	width: 8.58rem;
}

.module-bgContainer4 {
	/* display: flex; */
	/* flex-direction: row; */
	width: 100%;
}
.module-bgContainer4 img{
	display: inline-block;
	width: 47%;
	cursor: pointer;
}

.image-buy {
	/* flex-grow: 1; */
	/* width: 100%; */
	/* height: 100%; */
}

.image-sale {
	/* flex-grow: 1; */
	/* margin-left: 16px; */
	/* width: 100%; */
	/* height: 100%; */
	float: right;
}

/* 适配pc 雷文天 */
@media (min-width: 750px) {
	.list-item-image1 {
		width: 433px;
		height: 325px;
	}

	.list-item-right1 {
		margin-top: 17px;
		margin-left: 37px;
	}

	.list-item-right-title1 {
		font-size: 43px;
	}

	.list-item-right-subtitle1 {
		margin-top: 12px;
		font-size: 32px;
	}


	.list-item-title-policy {
		font-size: 39px;
		margin-top: 0.26rem;
	}

	.list-item-image-policy {
		width: 496px;
		height: 372px;
		margin-left: 100px;
		border-radius: 4px;
	}

	.line-view {
		margin-bottom: 30px;
		margin-top: 30px;
		background-color: #EDEDED;
		height: 1px;
	}

	.input_bgContainer {
			display: flex;
			flex-direction: row;
			margin-top: 40px;
		}
	
		.input-style {
			padding-left: 40px;
			padding-top: 30px;
			padding-bottom: 30px;
			font-size: 30px;
		}
	
		.send-bg {
			font-size: 32px;
		}

	.image-sale {
		margin-left: 40px;
	}

	/* 适配pc 雷文天 */
}

/* 小二帮帮忙end 文天 */


/* 高光时刻 洪斌 start*/
.highlight-video-list {
	width: 100%;
	margin: 0 auto;
}

.highlight-video-list li:first-child {
	border-top: none
}

.highlight-line {
	border-top: 1px solid #e8e8e8;
}

.highlight-item-title {
	padding-top: 0.15rem;
	color: #303030;
	font-size: 0.36rem;
	font-weight: bold;
}

.highlight-item-des {
	margin-top: 0.15rem;
	margin-bottom: 0.15rem;
	color: #303030;
	font-size: 0.31rem;
}

.highlight-img-container {
	position: relative;
	border-radius: 0.1rem;
	overflow: hidden;
	margin-bottom: 0.15rem;
}

.highlight-item-img {
	display: block;
	width: 100%;
}

.highlight-item-play {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 0.79rem;
	height: 0.79rem;
	margin: auto;
}

/* 高光时刻  end*/

/* 领导讲话  start*/
.leader-speak-video-list {
	display: grid;
	grid-template-columns: repeat(2, 1fr);
	grid-gap: 0.31rem;
}

.leader-speak-item-describe {
	margin-top: 0.15rem;
	color: #303030;
	font-size: 0.31rem;
}

.leader-speak-video-container {
	position: relative;
	border-radius: 4px;
	overflow: hidden;
	margin-bottom: 10px;
}

.leader-speak-item-img {
	display: block;
	width: 100%;
	height: 2.74rem;
	object-fit: cover;
}

.leader-speak-item-play {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	width: 0.79rem;
	height: 0.79rem;
	margin: auto;
}

/* 领导讲话  end*/

/* 政策咨询  start*/
.policy-item-box {
	position: relative;
}

.policy-item-img {
	display: block;
	width: 100%;
}

.policy-action-view {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 1rem;
	display: flex;
}

.policy-action-view-call {
	width: 50%;
	position: relative;
}
.policy-action-view-call:hover .showTel {display: block;}
.showTel {
	display: none;
	font-size: 0.3rem;
	position: absolute;
	left: 20%;
	top: 100%;
	background-color: #333;
	color: #fff;
	padding: 8px;
	white-space: nowrap;
}

.policy-action-view-preview {
	width: 50%;
}

.full-corver {
	display: block;
	width: 100%;
	height: 100%;
}


/* 政策咨询  end*/


/* 往期回顾  start*/
.review-swiper {
	overflow: hidden;
	position: relative;
}

.review-swiper-item-border {
	border-radius: 5px;
	overflow: hidden;
}

.review-swiper-item-img {
	display: block;
	height: 100%;
	width: 100%;
	object-fit: cover;
}

/* 往期回顾  end*/


/* 特价房源推荐  start*/
.discount-house-box {
	position: relative;
	margin-bottom: 0.4rem;
}

.full-corver-1 {
	display: block;
	width: 100%;
	height: 100%;
}

.full-corver-2 {
	display: block;
	width: 100%;
	height: 100%;
}

.video-item-img {
	display: block;
	width: 100%;
}

.action-call-view-top {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 1.5rem;
	display: flex;
}

.action-call-view-top:hover .showHouseTel {display: block;}

.action-call-view-bottom {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	width: 100%;
	height: 1.5rem;
	display: flex;
}

.action-call-view-bottom:hover .showHouseTel {display: block;}

.showHouseTel {
	display: none;
	font-size: 0.4rem;
	position: absolute;
	right: 20%;
	top: 30%;
	background-color: #333;
	color: #fff;
	padding: 8px;
	white-space: nowrap;
}
/* 特价房源推荐  end*/

/* 视频播放  end*/
.video-view {
	position: fixed;
	left: 0;
	top: 0;
	background: rgb(0 0 0 / 100%);
	width: 100vw;
	height: 100vh;
	z-index: 9999;
	display: none;
	justify-content: center;
	align-items: center;
}

#video-view {}

.top-back {
	width: 100%;
	height: 43px;
	background-color: #fff;
	color: #000;
	text-align: center;
	font-size: 15px;
	line-height: 43px;
	z-index: 99;
	position: fixed;
	top: 0;
}

.top-back .return {
	background: url(https://static.fangxiaoer.com/m/static/images/headNav/page_fddh_nav_btn_rtn.png) top center;
	background-size: 100% 100%;
	;
	display: block;
	width: 20px;
	height: 20px;
	float: left;
	;
	;
	;
	;
	;
	;
	;
	cursor: pointer;
	;
	;
}

/* 视频播放  end*/

@media (min-width: 750px) {

	/* 高光时刻  start*/
	.highlight-item-title {
		/* padding-top: 0.15rem; */
		font-size: 43px;
	}

	.highlight-item-des {
		margin-top: 20px;
		margin-bottom: 20px;
		/* margin-bottom: 0.15rem; */
		font-size: 37px;
	}

	.highlight-img-container {
		border-radius: 4px;
		margin-bottom: 0.15rem;
	}

	.highlight-item-img {
		display: block;
		width: 100%;
	}

	.highlight-item-play {
		width: 95px;
		height: 95px;
	}

	/* 高光时刻  end*/

	/* 领导讲话  start*/
	.leader-speak-item-describe {
		margin-top: 17px;
		font-size: 32px;
	}

	.leader-speak-video-container {
		border-radius: 4px;
		margin-bottom: 10px;
	}

	.leader-speak-item-img {
		height: 405px;
	}

	.leader-speak-item-play {
		width: 95px;
		height: 95px;
	}

	/* 领导讲话  end*/

	/* 政策咨询  start*/
	.policy-action-view {
		height: 120px;
	}
	/* 政策咨询  end*/


	/* 特价房源推荐  start*/
	.action-call-view-top {
		height: 190px;
	}

	.action-call-view-bottom {
		height: 190px;
	}
	/* 特价房源推荐 洪斌  end*/
}




.new-house-swiper-nav .swiper-slide {
    text-align: center;
    font-size: 18px;
    display: flex;
    justify-content: center;
    align-items: center;
}


.loader-container {
	height: 100%;
	width: 100%;
	position: relative;
}

.loader {
	left: 0;
	top: 0;
	position: fixed;
	width: 100vw;
	height: 100vh;
	background-color: #FFE3A5;
	display: flex;
	flex-direction: column;
	justify-content: center;
	align-items: center;
	z-index:99999;
}

#loader-ld {
	display: flex;
	flex-direction: row;
}

#loader-ld div {
	height: 20px;
	width: 5px;
	background: #FE4A49;
	margin: 3px;
	border-radius: 25px;
}

#loader-ld div:nth-child(1) {
	animation: loader-ld 1s ease-in-out infinite 0s;
}

#loader-ld div:nth-child(2) {
	animation: loader-ld 1s ease-in-out infinite 0.1s;
}

#loader-ld div:nth-child(3) {
	animation: loader-ld 1s ease-in-out infinite 0.2s;
}

#loader-ld div:nth-child(4) {
	animation: loader-ld 1s ease-in-out infinite 0.3s;
}

#loader-ld div:nth-child(5) {
	animation: loader-ld 1s ease-in-out infinite 0.4s;
}

#loader-ld div:nth-child(6) {
	animation: loader-ld 1s ease-in-out infinite 0.5s;
}

#loader-ld div:nth-child(7) {
	animation: loader-ld 1s ease-in-out infinite 0.6s;
}

@keyframes loader-ld {
	0% {
		transform: scaleY(1);
		background: #FED766;
	}

	25% {
		background: #009FB7;
	}

	50% {
		transform: scaleY(2);
		background: #59CD90;
	}

	75% {
		background: #FE4A49;
	}

	100% {
		transform: scaleY(1);
		background: #D91E36;
	}
}
