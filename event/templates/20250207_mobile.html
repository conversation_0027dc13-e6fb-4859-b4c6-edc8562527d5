<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
		<meta name="apple-mobile-we b-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta name="format-detection" content="telphone=no, email=no" />
		<meta name="format-detection" content="telephone=no" />
		<title>元宵好运来</title>
		<meta name=" format-detection" content="telephone=no">
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
		<link href="../static/2025/m0207/css/allheader.css" rel="stylesheet" type="text/css">
		<script src="../static/2025/m0207/js/flexible.js"></script>
		<link rel="stylesheet" href="../static/2025/m0207/css/m1218.css?v=12">
		<link rel="stylesheet" href="../static/2025/m0207/css/m1207.css?v=77">

		<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
		<script src="../static/2025/m0207/js/share.js" type="text/javascript"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/eventtest/sd/jquery.md5.js"></script> 
		<script src="../static/2025/m0207/js/howler.min.js" type="text/javascript" charset="utf-8"></script>
	
		<link rel="stylesheet" href="../static/2025/m0207/css/swiper.min.css">
		<script src="../static/2025/m0207/js/swiper.min.js"></script>
		<link rel="stylesheet" href="../static/2025/m0207/css/aliplayer.css?v=69" />
		<script type="text/javascript" charset="utf-8" src="../static/2025/m0207/js/aliplayer.js"></script>
		<script type="text/javascript" src="../static/2025/m0207/js/jquery.rotate.min.js"></script>
<!--		<script type="text/javascript" src="../static/2018/m0510/js/verify.js"></script>-->
		<script src="../static/2025/m0207/js/verify.js?v=11"></script>
		<script>
			var title = '元宵好运来，小二送好礼';
			var imgUrl = 'https://event.fangxiaoer.com/static/2025/m0207/img/share.jpg';
			var desc = '元宵佳节将至，房小二网为大家准备了一场超好玩的抽奖活动，快来参与，把好运带回家！';
		</script>

	</head>
	
	<body>


		{% include "header_m_csrf.html" %}


		<div class="tpb hide"></div>

		<!--抽奖说明-->
		<div class="explain">活动说明</div>
		<!--活动规则-->
		<div class="guize activeInfo" style="top: 2.5rem;">
			<div class="gzc"></div>
			<div class="gtit">活动说明</div>
			<div class="gtm">
				<div class="gtma">
					<p>抽奖资格：所有已注册并登录的用户均可获得一次抽奖机会，快来试试手气吧！</p>
					<p>分享助力：您还可以将活动转发分享给好友，邀请他们一同参与抽奖，分享越多，乐趣越多！</p>
					<p>奖品领取：若您幸运中奖，请在活动页面选择您希望领取奖品的门店，届时请凭相关凭证前往指定门店领取。</p>
					<p>电影票说明：本次抽奖中的电影票为2D电影票。若您选择观看其他类型电影，可能需要根据影城要求补足差价，具体详情请提前咨询影城。</p>
					<p>领奖时间：奖品的领取时间为13号13:30--16:00,14号-16号10:00--15:00。请务必在规定时间内前往领取，逾期未领的奖品将视为作废。</p>
					<p>其他说明：奖品图片仅供参考，具体以实物为准。本活动最终解释权归房小二所有。</p>
				</div>
			</div>
			<div class="ztbtn">知道了</div>
		</div>





		<div class="zmain">
				<!--抽奖-->
				<div class="cjm">
					<!--标题-->
					<div class="hti"></div>
					<!--箭头-->
					<div class="choujiang"></div>
					<!--立即抽奖-->
					<div class="fbt1"></div>
					<!--我的奖品-->
					<div class="fbt2"></div>
				</div>

				<div class="zjdp" style="display: none;">
					<div id="section1"></div><!--锚点-->
					<div class="jlist" style="position: relative; z-index: 5;">
						<h3>选择奖品领取门店 (咨询：************)</h3>
						<div class="jul"></div>
					</div>
					<div class="subbtn">提交领取门店</div><!--fixed-->
					<div style="position: absolute; top: 1.4rem; left: 0; width: 100%; height: 5rem; background-color: #FEDD9D; z-index: 1;"></div>
				</div>

		</div>



		<!--背景-->
		<div class="bg"></div>
		<!--我的礼品-暂无礼品-->
		<div class="zanw page3" style="height: 9rem;">
			<div class="zantt" style="margin-top: 0.3rem;">我的奖品</div>
			<div class="zanmm" style="margin-top: 1rem;">
				<img src="../static/2025/m0207/img/a3.png">
				<span>什么都没有</span>
			</div>
			<!--			<div class="zcls">返回首页</div>-->
			<div class="zclose"></div>
		</div>
		<!--我的礼品-有礼品-->
		<div class="yli page4" style="padding-top: 0.2rem; height: 14rem;">
			<div class=" ytop gtit" style=" margin-bottom: 0.5rem;">我的奖品</div>

			<div class="gtm2">
				<div class="gtma2">
					<div class="tkm">
						<div class="ytit" style="font-weight: bold;"></div>
						<div class="ytmm">
						</div>
					</div>
					<div class="ytm">
						<p class="jp zjStatus"></p>
<!--						<p class="jp">领取规则：请选择您所在区域的中介门店，确认后您可以到选中的中介门店领取</p>-->
						<div class="jpzj"></div>
					</div>
				</div>
			</div>

			<div class="zclose"></div>
		</div>

		<!--抽奖次数用完-->
		<div class="cied">
			<em>您的抽奖机会用完了，快分享给您的好友 看看他的运气吧~</em>
			<div class="common_btn cih">分享给好友</div>
			<div class="common_close cis"></div>
		</div>

		<!--没中奖-->
		<div class="nov common_kuang">
					<span>
						<i>没中奖，别遗憾</i>
						分享给你的好友，让他/她试试运气
					</span>
			<div class="common_btn novbt">分享给好友</div>
			<div class="common_close novclose"></div>
		</div>

		<!--盲盒  - 无礼品-->
		<div class="page5">
			<div class="p5close"></div>
			<div class="p5ts">没中奖别遗憾</div>
			<img src="../static/2025/m0207/img/a18.png" style="width: 5rem; height: 5rem; display: block; margin: 1rem auto;">
			<div class="cjts"></div><!--可继续抽奖   明日继续-->
		</div>
		<!--盲盒  - 有礼品-->
		<div class="page6">
			<div class="p6close"></div>
			<div class="gkk">
				<div class="p6m"></div>
				<img src="../static/2025/m0207/img/p/1.png" class="hdjp">
				<p>领取规则：</p>
				<p>中奖者当天15:00前展馆房小二服务台领取，领取时请先下载房小二App，奖品不可以代领，超出时间后可视为自动放弃领奖资格</p>
			</div>
		</div>
		<!--盲盒  - 有礼品  新增-->
		<div class="guize zjInfo" style="height: 6.8rem;">
			<div class="gzc"></div>
			<div class="gtm" style="margin-top: 1.3rem;">
				<div class="gtma">
					<h3 style="text-align: center;font-size: 0.43rem;color: #831900;line-height: 0.67rem; margin-bottom: 0.48rem;">恭喜您，已抽奖奖品<br/> 您可以前往我的奖品中查看</h3>
					<p>领取规则：请点击下方按钮，选择您所在区域的 中介门店，确认后您可以到选中的中介门店领取</p>
				</div>
			</div>
			<a href="#section1"><div class="searchMD">选择中介门店</div></a>
		</div>

		<!--分享-->
		<div class="share"><img src="../static/2025/m0207/img/share3.png"></div>
		<!--登录-->
		<div class="dbox common_kuang" style="padding-top: 0.5rem;">
			<div class="TTCClose"></div>
			<h4 class='dboxH4'>参与活动</h4>
			<div class="lput">
				<div class="inh">
					<!-- <em>手机号</em> -->
					<input type="number" placeholder="请输入手机号" maxlength="11" class="phone2" oninput="if(value.length>11)value=value.slice(0,11)">
				</div>
				<div class="inm">
					<!-- <em>验证码</em> -->
					<input type="number" placeholder="请输入验证码" maxlength="6" class="code2" oninput="if(value.length>6)value=value.slice(0,6)">
					<i class="tt yzk getc2 codeFn2">点击获取验证码</i>
					<i class="tt yzk setc2"></i>
				</div>
			</div>
			<div class="common_btn subBox">登录</div>
<!--			<div class="common_close boxclose"></div>-->
		</div>
		<!----------->
		<div class="fxe_alert"><span></span></div>
		<script>
		        var time = 2;//消失秒数
		        function time1() {//alert计时器
		            time--;
		            if (time >= 0) {
		                setTimeout("time1()", 1000);
		            } else {
		                $(".fxe_alert").fadeOut();
		            }
		        }
		        //alert显示内容
		        function fxe_alert(text1) {
		            time = 2;
		            $(".fxe_alert span").text(text1);
		            var wid = $(".fxe_alert").width();
		            $(".fxe_alert").fadeIn();
		            setTimeout("time1()", 1000);
		        }
		</script>
		<!--分享-->
		<script src="https://unpkg.com/vconsole@latest/dist/vconsole.min.js"></script>
		<script>

			// var vConsole = new window.VConsole();

			/*var bid = 66
			var dustType = 168
			var openId = ""
			// var ltapi = "http://********:8083";
			var ltapi = "http://************:8083";*/





			//线上
			var bid = 66//线上67  测试66
			var dustType = 168
			var openId = ""
			// var ltapi = "https://ltapi.fangxiaoer.com";
			var ltapi = "http://********:8083";


			// openId = getCookie("openId");
			var sessionId = getCookie("aspxUserTicket")

			sessionId = '56d70bac1b681140cad54570d1f7b760'


			//使用 小程序的 sessionId
			/*if(getQueryString('jty')==1 && getQueryString('sessionId')){
				sessionId=getQueryString('sessionId')
			}*/



			openId=openId+''
			sessionId=sessionId+''
			if(openId==''){
				openId='null'
			}
			if(sessionId=='' || sessionId==undefined){
				sessionId='null'
			}

			console.log('sessionId:===='+sessionId)



			//中介店铺列表
			var dianpu
			$(".jul").empty()
			$.ajax({
				type: "POST",
				url: ltapi+'/apiv1/house/fetchParent',
				data: {
					dustType: dustType,
				},
				async: false,
				success: function(data) {
					if(data.status == 1) {
						var data = data.content
						dianpu = data.content
						for(var i=0;i<data.length;i++){
							var str = `
						<div class="jli" d="${data[i].id}">
							<img src="${data[i].pic}" alt=""/>
							<div class="jright">
								<p>${data[i].store}</p>
								<p>门店地址：${data[i].address}</p>
								<i class="lii" d="${data[i].id}"></i>
							</div>
						</div>
						`
							$(".jul").append(str)
						}
					}else{
						fxe_alert(data.msg)
					}
				}
			})


			//获取验证码
			let wait2 = 60;
			let wTime2=null
			$(".codeFn2").click(function(){
				if(!$(".phone2").val().match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)){
					fxe_alert('请正确输入您的手机号!')
					return;
				}
				var phtel2=$(".phone2").val()
				if(sy_confirm.Code(phtel2) == true){
					time2(wait2)//倒计时
				}
			})
			//倒计时
			function time2(o) {
				if (wait2 == 0) {
					$(".getc2").show()
					$(".setc2").hide()
					$(".tt").html("重新获取");
					wait2 = 60;
					$(".tt").css({ "color": "#3a3a3a","background-color":"#f1dada"});
				} else {
					$(".getc2").hide()
					$(".setc2").show()
					$(".tt").html("在" + wait2 + "秒后重发");
					$(".tt").css({"color":"#fff",'background-color':'#9a9ea2'})
					wait2--;
					wTime2=setTimeout(function () {
						time2(o);
					},1000);
				}
			}
			
			// 登录
			$('.subBox').click(function(){
				var mobile2 = $(".phone2").val()
				var code2 = $(".code2").val()
				if (!mobile2) {
					fxe_alert("请输入手机号")
				} else if (!code2) {
					fxe_alert("请输入验证码")
				} else {
					$.ajax({
						type: "POST",
						url: '/fxrLogin/',
						data: {
							mobile: mobile2,
							code: code2,
						},
						async: false,
						success: function(data) {
							data = jQuery.parseJSON(data)
							if(data.status == 1) {
								sessionId = data.content.sessionId;
								setCookie("aspxUserTicket", sessionId);
								$('.bg,.dbox').hide()
							} else {
								fxe_alert(data.msg)
							}
						}
					})
				}
			})

			//抽奖
			function prizeDraw(){
				$.ajax({
					type: "POST",
					url:ltapi+'/apiv1/other/drawForTopicOnce',
					data: {
						baseId: bid,
						sessionId: sessionId!='null'?sessionId:'',
						openId: openId!='null'?openId:'',
					},
					async: false,
					success: function(data) {
						if(data.status == 1) {
							console.log(data.content.displayId)
							switch(data.content.displayId){
								case "1":
									rotateFunc(0, 265,1);//《哪吒》电影票
									setTimeout(showTXT, 3050);
									break;
								case "2":
									rotateFunc(0, 325,1);//精品大米
									setTimeout(showTXT, 3050);
								case "3":
									rotateFunc(0, 30,1);//精品元宵
									setTimeout(showTXT, 3050);
									break;
								case "4":
									rotateFunc(0, 210,1);//楼市文旅地图1份+帆布手袋1个
									setTimeout(showTXT, 3050);
									break;
								case "5":
									rotateFunc(0, 160,1);//楼市文旅地图1份+纸袋1个
									setTimeout(showTXT, 3050);
									break;
								case "6":
									rotateFunc(0, 100,1);//地图
									setTimeout(showTXT, 3050);
									// fxe_alert('谢谢参与！')
									break;
							}
							data = data.content
							var showTel = data.mobile
							function showTXT(){
								$(".moox,.dbox").hide()
								$(".p6m").html('恭喜您获得 <strong>"'+data.gift+'"</strong>')
								$(".bg,.page6").show()//有礼品
								$(".hdjp").attr('src','../static/2025/m0207/img/p/'+data.displayId+'.png')
							}
						}else if(data.status==-1){
							setCookie("aspxUserTicket", 'null');
							// fxe_alert(data.msg)
							$(".bg,.dbox").show()
						} else {
							setCookie("aspxUserTicket", 'null');
							fxe_alert(data.msg)
						}
					},
					error(){
						fxe_alert('404')
					}
				})
			}
			// 点击抽奖按钮
			$(".fbt1").click(function(){
				if(sessionId == 'null' && openId == 'null'){
					// fxe_alert('每日登录开启幸运抽奖')
					$(".bg,.dbox").show()
				}else{
					prizeDraw()//开始抽奖
				}
			})
			//转盘
			function rotateFunc(awards, angle,base) {
				$(".choujiang").stopRotate();
				$(".choujiang").rotate({
					angle: 0,
					duration: 3000,
					animateTo: angle + 1440,
				});
			}

			

			
			$("body").on('click', '.TTCClose,.fullTc,.TTC2', function(event) {
				$(".TTC1,.TTC2,.fullTc").hide()
				$("#phone,#code").val("")
			})
			

			function getCookie(name) {
				var arr = document.cookie.match(new RegExp("(^| )" + name + "=([^;]*)(;|$)"));
			
				if(arr != null) return unescape(arr[2]);
				return null;
			}
			function setCookie(name, value) {
				var exp = new Date();
				exp.setTime(exp.getTime() + 1 * 60 * 60 * 200); //有效期10分钟
				document.cookie = name + "=" + escape(value) + ";path=/;expires=" + exp.toGMTString();
			}
			

			//我的奖品
			$(".fbt2").click(function(){
				console.log(sessionId)
				console.log(openId)
				if(sessionId != 'null' || openId != 'null'){
					$.ajax({
						type: "POST",
						url:ltapi+'/apiv1/other/viewPrizesInfo',
						data: {
							baseIds: bid,
							sessionId: sessionId!='null'?sessionId:'',
							openId: openId!='null'?openId:'',
						},
						// async: false,
						success: function(res) {
							if(res.status == 1) {
								let data = res.content

								if(data == ''){
									//没中奖
									$(".page3,.bg").show()//无礼品
									// 禁止滚动
									document.body.style.overflow = 'hidden';
								}else{
									//中奖
									$(".ytmm").empty()
									$(".ytit").text('恭喜您获得')
									if(data.length==1){
										$(".page4").css('height','11.5rem')
										$(".gtm2").css('height','8.3rem')
									}else{
										$(".page4").css('height','14.3rem')
										$(".gtm2").css('height','11.4rem')
									}
									/*for(var i=0;i<data.length;i++){
										// let a=data[i].addTime.split(' ')[0].split('-')
										// let b=a[1]+'月'+a[2]+'日'
										// data[i].displayId=4
										var ss=`<div class="zli">
													<span style="font-weight: bold;">`+data[i].giftName+`</span>
													<img src="../static/2025/m0207/img/p/`+data[i].displayId+`.png" style="${data[i].displayId==3?'width:3.7rem':''} margin-top: 0.3rem; margin-bottom: 0.3rem;">
												</div>`
										$(".ytmm").append(ss)

									}*/

									var ss=`<div class="zli">
													<span style="font-weight: bold;">`+data[0].giftName+`</span>
													<img src="../static/2025/m0207/img/p/`+data[0].displayId+`.png" style="${data[0].displayId==3?'width:3.7rem':''} margin-top: 0.3rem; margin-bottom: 0.3rem;">
												</div>`
									$(".ytmm").append(ss)

									$.ajax({
										type: "POST",
										url: ltapi+'/apiv1/house/fetchParent',
										data: {
											dustType: dustType,
										},
										async: false,
										success: function(hh) {
											if (hh.status == 1) {
												let t=hh.content.filter((item)=> item.id==data[0].comments)
												let aa=`
												<p>门店地址：${t[0].address}</p>
												<p>咨询电话：************</p>
												`
												$(".jpzj").html(aa)
											}
										}
									})

									$(".zjStatus").html(`中奖号码：${data[0].mobile} (${data[0].state==0?'未核销':'已核销'})`)



									if(data[0].comments!=null){
										$(".page4,.bg").show()//有礼品
										// 禁止滚动
										document.body.style.overflow = 'hidden';
									}else{
										$(".page6,.bg").show()
									}

								}
								$(".luckLogin").hide()
							}else if(res.status==-1){
								setCookie("aspxUserTicket", 'null');
								// fxe_alert(res.msg)
								$(".bg,.dbox").show()
							}else {
								setCookie("aspxUserTicket", 'null');
								fxe_alert(res.msg)
							}
						}
					})
				}else{
					$(".bg,.dbox").show()
				}

			})
			//登录关闭
			$(".TTCClose").click(function(){
				$(".bg,.dbox").hide()
			})
			//关闭弹窗
			$(".zclose,.p6close,.p5close,.novclose").click(function(){
				$(".bg,.page4,.page3,.zjInfo,.page5,.page6,.nov,.cied,.share").hide()
				// 当需要滚动时
				document.body.style.overflow = 'auto';
			})
			
			$(".novbt,.cih").click(function(){
				$(".nov,.cied").hide()
				$(".share").show()
			})
			

			//活动规则
			$(".explain").click(function(){
				$('.bg,.activeInfo').show()
				// 禁止滚动
				document.body.style.overflow = 'hidden';
			})
			$('.gzc,.ztbtn').click(function(){
				$('.bg,.activeInfo,.zjInfo').hide()
				// 当需要滚动时
				document.body.style.overflow = 'auto';
			})

			//选择中介店铺
			let storeId=false;
			$(document).on('click','.jli',function(){
				$(this).find('i').addClass('jib')
				$(this).siblings().find('i').removeClass('jib')

				storeId=$(this).attr('d')
				$(".subbtn").addClass('fixed')
			})
			//提交领取门店
			$('.subbtn').click(function(){
				if(!storeId){
					fxe_alert('请选择门店')
				}else{
					console.log(storeId)
					$.ajax({
						type: "POST",
						url: ltapi + '/apiv1/other/viewPrizesInfo',
						data: {
							baseIds: bid,
							sessionId: sessionId!='null'?sessionId:'',
							openId: openId != 'null' ? openId : '',
						},
						async: false,
						success: function(res) {
							if(res.status==1){
								$.ajax({
									type: "POST",
									url: ltapi + '/apiv1/other/selectShippingAddress',
									data: {
										sessionId: sessionId!='null'?sessionId:'',
										openId: openId != 'null' ? openId : '',
										historyId: res.content[0].id,
										dustId: storeId
									},
									async: false,
									success: function(dd) {
										if(dd.status==1){
											$(".lii,.subbtn").hide()
											$(".subbtn").removeClass('fixed')
											fxe_alert('领取门店已提交！')
											$("html,body").animate({scrollTop:0}, 500)//滚动到顶部
										}else{
											fxe_alert(dd.msg)
										}
									}
								})
							}else{
								fxe_alert(res.msg)
							}
						}
					})


				}
			})

			//选择中介门店
			$(".searchMD").click(function(){
				$('.bg,.zjInfo').hide()
				$(".lii,.subbtn").show()
			})
			$(".lii,.subbtn").hide()


			//获取参数
			function getQueryString(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
				var r = window.location.search.substr(1).match(reg);
				if (r != null) return unescape(r[2]);
				return null;
			}

		</script>
	</body>
</html>


<!--

转盘抽奖：https://event.fangxiaoer.com/20250207.htm
核销：https://event.fangxiaoer.com/20250207writeOff_mobile.htm?storeId=1

-->
