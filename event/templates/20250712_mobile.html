<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
				content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<meta name="apple-mobile-we b-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black" />
	<meta name="format-detection" content="telphone=no, email=no" />
	<title>沈阳三好楼盘评选</title>
	<meta name="keywords" content="" />
	<meta name="description" content="" />
	<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
	<script src="../static/2025/m0712/js/flexible.js"></script>
	<link rel="stylesheet" href="../static/2025/m0712/css/swiper-bundle.min.css" />
	<script src="../static/2025/m0712/js/swiper-bundle.min.js"></script>
	<link rel="stylesheet" href="../static/2025/m0712/css/aliplayer.css?v=1" />
	<script type="text/javascript" charset="utf-8" src="../static/2024/m0820/js/aliplayer-min.js"></script>
	<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
	<script src="../static/2025/m0712/js/share.js" type="text/javascript"></script>
	<script type="text/javascript" src="https://static.fangxiaoer.com/eventtest/sd/jquery.md5.js"></script>
	<link rel="stylesheet" href="../static/2025/m0712/css/m0712.css?v=2025">
	<script>
		window.alert = function (name) {
			var iframe = document.createElement("IFRAME");
			iframe.style.display = "none";
			iframe.setAttribute("src", 'data:text/plain,');
			document.documentElement.appendChild(iframe);
			window.frames[0].window.alert(name);
			iframe.parentNode.removeChild(iframe);
		};
	</script>
</head>

<body>
	<script>
		var imgUrl = ""
		var title = "../static/2025/m0712/img/2.png";
		var desc = "";
	</script>
<!--	{% include "header_m_csrf.html" %}-->

	<div class="mtop">
		<img src="../static/2025/m0712/img/<EMAIL>" />
	</div>


	<div class="tab-bar">
		<div class="tab-btn tab-btn-active"></div>
		<div class="tab-btn"></div>
	</div>
	<div class="tab-content">
		<div class="tab-panel" id="panel-exhibit"></div>
		<div class="tab-panel" id="panel-rank" style="display:none;">
			<div class="rank-list">
				<div class="card2">
					<!--<div class="rank-item rank-1">
						<span class="rank-icon"><img src="../static/2025/m0712/img/<EMAIL>" style="width:1.6em;vertical-align:middle;"><span class="rank-num"></span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">1987</span>票</span>
					</div>
					<div class="rank-item rank-2">
						<span class="rank-icon"><img src="../static/2025/m0712/img/<EMAIL>" style="width:1.6em;vertical-align:middle;"><span class="rank-num"></span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">965</span>票</span>
					</div>
					<div class="rank-item rank-3">
						<span class="rank-icon"><img src="../static/2025/m0712/img/<EMAIL>" style="width:1.6em;vertical-align:middle;"><span class="rank-num"></span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">546</span>票</span>
					</div>
					<div class="rank-item">
						<span class="rank-icon"><span class="rank-num">4</span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">128</span>票</span>
					</div>-->

				</div>
			</div>
		</div>
	</div>
	<!-- 投票成功弹窗 -->
	<div id="voteMask" style="display:none;"></div>
	<div id="voteDialog" style="display:none;">
		<div class="vote-dialog-icon">
			<svg width="48" height="48" viewBox="0 0 48 48"><circle cx="24" cy="24" r="24" fill="url(#grad)"/><linearGradient id="grad" x1="0" y1="0" x2="1" y2="1"><stop offset="0%" stop-color="#ffb86c"/><stop offset="100%" stop-color="#ff6b3b"/></linearGradient><polyline points="14,25 22,33 34,17" fill="none" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>
		</div>
		<div class="vote-dialog-title">投票成功</div>
		<div class="vote-dialog-sub">今天还可以投2票</div>
		<button class="vote-dialog-btn" id="voteDialogBtn">我知道了</button>
	</div>
	<!-- 投票次数已用完弹窗 -->
	<div id="voteLimitDialog">
		<div class="vote-limit-dialog-title">
			今天投票次数已用完
		</div>
	</div>
	
	
	
	

	<script>
		// var ltapi = "https://ltapi.fangxiaoer.com"
		var ltapi = "http://********:8083"


		//项目列表
		$.ajax({
			type: "POST",
			url: ltapi + "/apiv1/house/threeGoodsList",
			data: {},
			success: function(data) {
				if (data.status == 1) {
					let res = data.content
					for(var i=0;i<res.length;i++){
						// 构建图片轮播的HTML
						let swiperSlides = '';
						let imageCount = 0;

						// 循环threeGoodsPic数组，使用picUrl字段
						if(res[i].threeGoodsPic && res[i].threeGoodsPic.length > 0) {
							for(var j=0; j<res[i].threeGoodsPic.length; j++) {
								if(res[i].threeGoodsPic[j].picUrl) {
									swiperSlides += `<div class="swiper-slide">
														<img class="card-img" src="${res[i].threeGoodsPic[j].picUrl}" alt="">
													</div>`;
									imageCount++;
								}
							}
						}

						// 如果没有图片，使用默认图片
						if(imageCount === 0) {
							swiperSlides = `<div class="swiper-slide">
												<img class="card-img" src="https://static.fangxiaoer.com/m/images/sload.jpg" alt="">
											</div>`;
							imageCount = 1;
						}

						let t=`<div class="card" data-card-index="${i}">
									<div class="card-img-wrapper">
										<div class="swiper card-swiper-${i}">
											<div class="swiper-wrapper">
												${swiperSlides}
											</div>
											<span class="img-index img-index-${i}" style="position:absolute;right:0.43rem;bottom:0.43rem;background:rgba(0,0,0,0.5);color:#fff;border-radius:0.32rem;padding:0.053rem 0.27rem;font-size:0.37rem;z-index:2;">1/${imageCount}</span>
										</div>
									</div>
									<div class="card-content">
										<div class="card-title-row">
											<span class="card-title" style="flex: 1;">${res[i].buildingName}</span>
											<span class="card-area" style="text-align: center;">${res[i].regionName}</span>
										</div>
										<div class="card-desc">${res[i].introduce}</div>
										<div class="card-footer">
											<span class="like">
												<img class="like-icon" src="../static/2025/m0712/img/<EMAIL>" alt="thumb-up">
												<span style="font-weight:bold;color:#333333;">${res[i].voteNumber}</span>票
											</span>
											<button class="vote-btn">投票</button>
										</div>
									</div>
								</div>`
						$("#panel-exhibit").append(t)
					}

					// 初始化每个卡片的轮播
					initializeCardSwipers(res.length);
				}
			}
		})

		//排行榜
		$.ajax({
			type: "POST",
			url: ltapi + "/apiv1/house/threeGoodsCharts",
			data: {},
			success: function(data) {
				if (data.status == 1) {
					for (var i = 0; i < data.content.length; i++) {
						var t = `<div class="rank-item">
											<span class="rank-icon"><span class="rank-num">6</span></span>
											<span class="rank-title">保利·和光屿湖</span>
											<span class="rank-vote"><span class="rank-vote-num">88</span>票</span>
										</div>`
						$(".card2").append(t)
					}
				}
			}
		})



		// 初始化卡片轮播的函数
		function initializeCardSwipers(cardCount) {
			for(var i = 0; i < cardCount; i++) {
				new Swiper('.card-swiper-' + i, {
					loop: false,
					autoplay: {
						delay: 3000,
						disableOnInteraction: false
					},
					on: {
						slideChange: function () {
							// 获取当前swiper对应的卡片索引
							var cardIndex = this.el.classList[1].split('-')[2]; // 从class名获取索引
							var realIndex = this.realIndex + 1;
							var total = this.slides.length;
							$('.img-index-' + cardIndex).text(realIndex + '/' + total);
						}
					}
				});
			}
		}

		// tab切换逻辑
		$('.tab-btn').click(function() {
			$('.tab-btn').removeClass('tab-btn-active');
			$(this).addClass('tab-btn-active');
			var idx = $('.tab-btn').index(this);
			if(idx === 0) {
				$('#panel-exhibit').show();
				$('#panel-rank').hide();
				$('.tab-bar').removeClass('tab-bar-active');
			} else {
				$('#panel-exhibit').hide();
				$('#panel-rank').show();
				$('.tab-bar').addClass('tab-bar-active');
			}
		});

		// 投票弹窗逻辑
		$('.vote-btn').on('click', function() {
			$('#voteMask').show();
			$('#voteDialog').show();
		});
		$('#voteMask, #voteDialogBtn').on('click', function() {
			$('#voteMask').hide();
			$('#voteDialog').hide();
		});
		// 投票次数已用完弹窗逻辑
		$('.card-title').on('click', function() {
			$('#voteLimitDialog').fadeIn(120);
			setTimeout(function(){
				$('#voteLimitDialog').fadeOut(200);
			}, 1800);
		});
		$('#voteLimitDialog').on('click', function() {
			$(this).fadeOut(120);
		});
	</script>
</body>
</html>