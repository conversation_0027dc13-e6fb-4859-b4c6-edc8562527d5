<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
				content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<meta name="apple-mobile-we b-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black" />
	<meta name="format-detection" content="telphone=no, email=no" />
	<title>沈阳三好楼盘评选</title>
	<meta name="keywords" content="" />
	<meta name="description" content="" />
	<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
	<script src="../static/2025/m0712/js/flexible.js"></script>
	<link rel="stylesheet" href="../static/2025/m0712/css/swiper-bundle.min.css" />
	<script src="../static/2025/m0712/js/swiper-bundle.min.js"></script>
	<link rel="stylesheet" href="../static/2025/m0712/css/aliplayer.css?v=1" />
	<script type="text/javascript" charset="utf-8" src="../static/2024/m0820/js/aliplayer-min.js"></script>
	<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
	<script src="../static/2025/m0712/js/share.js" type="text/javascript"></script>
	<script type="text/javascript" src="https://static.fangxiaoer.com/eventtest/sd/jquery.md5.js"></script>
	<link rel="stylesheet" href="../static/2025/m0712/css/m0712.css?v=2025">
	<script>
		window.alert = function (name) {
			var iframe = document.createElement("IFRAME");
			iframe.style.display = "none";
			iframe.setAttribute("src", 'data:text/plain,');
			document.documentElement.appendChild(iframe);
			window.frames[0].window.alert(name);
			iframe.parentNode.removeChild(iframe);
		};
	</script>
</head>

<body>
	<script>
		var imgUrl = ""
		var title = "../static/2025/m0712/img/2.png";
		var desc = "";
	</script>
<!--	{% include "header_m_csrf.html" %}-->

	<div class="mtop">
		<img src="../static/2025/m0712/img/<EMAIL>" />
	</div>


	<div class="tab-bar">
		<div class="tab-btn tab-btn-active"></div>
		<div class="tab-btn"></div>
	</div>
	<div class="tab-content">
		<div class="tab-panel" id="panel-exhibit"></div>
		<div class="tab-panel" id="panel-rank" style="display:none;">
			<div class="rank-list">
				<div class="card2">
					<div class="rank-item rank-1">
						<span class="rank-icon"><img src="../static/2025/m0712/img/<EMAIL>" style="width:1.6em;vertical-align:middle;"><span class="rank-num"></span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">1987</span>票</span>
					</div>
					<div class="rank-item rank-2">
						<span class="rank-icon"><img src="../static/2025/m0712/img/<EMAIL>" style="width:1.6em;vertical-align:middle;"><span class="rank-num"></span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">965</span>票</span>
					</div>
					<div class="rank-item rank-3">
						<span class="rank-icon"><img src="../static/2025/m0712/img/<EMAIL>" style="width:1.6em;vertical-align:middle;"><span class="rank-num"></span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">546</span>票</span>
					</div>
					<div class="rank-item">
						<span class="rank-icon"><span class="rank-num">4</span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">128</span>票</span>
					</div>
					<div class="rank-item">
						<span class="rank-icon"><span class="rank-num">5</span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">96</span>票</span>
					</div>
					<div class="rank-item">
						<span class="rank-icon"><span class="rank-num">6</span></span>
						<span class="rank-title">保利·和光屿湖</span>
						<span class="rank-vote"><span class="rank-vote-num">88</span>票</span>
					</div>
				</div>
			</div>
		</div>
	</div>
	<!-- 投票成功弹窗 -->
	<div id="voteMask" style="display:none;"></div>
	<div id="voteDialog" style="display:none;">
		<div class="vote-dialog-icon">
			<svg width="48" height="48" viewBox="0 0 48 48"><circle cx="24" cy="24" r="24" fill="url(#grad)"/><linearGradient id="grad" x1="0" y1="0" x2="1" y2="1"><stop offset="0%" stop-color="#ffb86c"/><stop offset="100%" stop-color="#ff6b3b"/></linearGradient><polyline points="14,25 22,33 34,17" fill="none" stroke="#fff" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/></svg>
		</div>
		<div class="vote-dialog-title">投票成功</div>
		<div class="vote-dialog-sub">今天还可以投2票</div>
		<button class="vote-dialog-btn" id="voteDialogBtn">我知道了</button>
	</div>
	<!-- 投票次数已用完弹窗 -->
	<div id="voteLimitDialog">
		<div class="vote-limit-dialog-title">
			今天投票次数已用完
		</div>
	</div>
	
	
	
	

	<script>
		// var ltapi = "https://ltapi.fangxiaoer.com"
		var ltapi = "http://********:8083"


		//项目列表
		$.ajax({
			type: "POST",
			url: ltapi + "/apiv1/house/threeGoodsList",
			data: {},
			success: function(data) {
				if (data.status == 1) {
					let res = data.content
					for(var i=0;i<res.length;i++){
						let t=`<div class="card">
									<div class="card-img-wrapper">
										<div class="swiper">
											<div class="swiper-wrapper">
												<div class="swiper-slide">
													<img class="card-img" src="https://images.unsplash.com/photo-1506744038136-46273834b3fb?auto=format&fit=crop&w=800&q=80" alt="">
												</div>
												
											</div>

											<span class="img-index" style="position:absolute;right:0.43rem;bottom:0.43rem;background:rgba(0,0,0,0.5);color:#fff;border-radius:0.32rem;padding:0.053rem 0.27rem;font-size:0.37rem;z-index:2;">1/5</span>
										</div>
									</div>
									<div class="card-content">
										<div class="card-title-row">
											<span class="card-title" style="flex: 1;">${res[i].buildingName}</span>
											<span class="card-area" style="text-align: center;">${res[i].regionName}</span>
										</div>
										<div class="card-desc">${res[i].introduce}</div>
										<div class="card-footer">
											<span class="like">
												<img class="like-icon" src="../static/2025/m0712/img/<EMAIL>" alt="thumb-up">
												<span style="font-weight:bold;color:#333333;">${res[i].voteNumber}</span>票
											</span>
											<button class="vote-btn">投票</button>
										</div>
									</div>
								</div>`
						$("#panel-exhibit").append(t)
					}
				}
			}
		})

		//排行榜
		$.ajax({
			type: "POST",
			url: ltapi + "/apiv1/house/threeGoodsCharts",
			data: {},
			success: function(data) {
				if (data.status == 1) {
				}
			}
		})



		var mySwiper = new Swiper('.swiper', {
			loop: false,
			autoplay: {
				delay: 3000,
				disableOnInteraction: false
			},
			on: {
				slideChange: function () {
					// 获取真实索引（去掉loop的克隆slide）
					var realIndex = this.realIndex + 1;
					var total = this.slides.length ; // loop模式下前后各有1个克隆slide
					$('.img-index').text(realIndex + '/' + total);
				}
			}
		});

		// 初始化时设置索引
		$('.img-index').text('1/5');

		// tab切换逻辑
		$('.tab-btn').click(function() {
			$('.tab-btn').removeClass('tab-btn-active');
			$(this).addClass('tab-btn-active');
			var idx = $('.tab-btn').index(this);
			if(idx === 0) {
				$('#panel-exhibit').show();
				$('#panel-rank').hide();
				$('.tab-bar').removeClass('tab-bar-active');
			} else {
				$('#panel-exhibit').hide();
				$('#panel-rank').show();
				$('.tab-bar').addClass('tab-bar-active');
			}
		});

		// 投票弹窗逻辑
		$('.vote-btn').on('click', function() {
			$('#voteMask').show();
			$('#voteDialog').show();
		});
		$('#voteMask, #voteDialogBtn').on('click', function() {
			$('#voteMask').hide();
			$('#voteDialog').hide();
		});
		// 投票次数已用完弹窗逻辑
		$('.card-title').on('click', function() {
			$('#voteLimitDialog').fadeIn(120);
			setTimeout(function(){
				$('#voteLimitDialog').fadeOut(200);
			}, 1800);
		});
		$('#voteLimitDialog').on('click', function() {
			$(this).fadeOut(120);
		});
	</script>
</body>
</html>