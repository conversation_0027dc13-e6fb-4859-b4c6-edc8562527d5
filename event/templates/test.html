<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<title>签名请求测试页</title>
	<script src="https://code.jquery.com/jquery-2.1.1.min.js"></script>
	<script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
</head>
<body>
<h3>签名请求测试</h3>
<button id="sendRequest">发送请求</button>
<pre id="result"></pre>

<script>
	(function($) {
		var SIGN_SECRET = '123456'; // 替换为你的密钥

		function generateSignature(data, secret) {
			var hash = CryptoJS.HmacSHA256(data, secret);
			return CryptoJS.enc.Base64.stringify(hash);
		}

		function generateNonce(length) {
			var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
			var nonce = '';
			for (var i = 0; i < length; i++) {
				nonce += chars.charAt(Math.floor(Math.random() * chars.length));
			}
			return nonce;
		}

		$('#sendRequest').click(function() {
			var url = 'http://********:8000/services/share-promotion-service/api/v1/public/getPrizeStatus';
			var method = 'GET';
			var path = '/api/v1/public/getPrizeStatus';
			var query = 'promotionId=1&phone=13804033048';
			var timestamp = Date.now().toString();
			var nonce = generateNonce(16);
			var body = '';

			var dataToSign = method + '\n' + path + '\n' + query + '\n' + body + '\n' + timestamp + '\n' + nonce;
			console.log("待签名数据：", dataToSign);
			var signature = generateSignature(dataToSign, SIGN_SECRET);

			$.ajax({
				url: url + '?' + query,
				method: method,
				beforeSend: function(xhr) {
					xhr.setRequestHeader('X-Signature', signature);
					xhr.setRequestHeader('X-Timestamp', timestamp);
					xhr.setRequestHeader('X-Nonce', nonce);
				},
				success: function(data) {
					$('#result').text("成功返回：\n" + JSON.stringify(data, null, 2));
				},
				error: function(xhr) {
					$('#result').text("请求失败：\n" + xhr.status + " - " + xhr.responseText);
				}
			});
		});
	})(jQuery);
</script>
</body>
</html>