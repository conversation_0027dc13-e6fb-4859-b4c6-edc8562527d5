<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
				content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<meta name="apple-mobile-we b-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black" />
	<meta name="format-detection" content="telphone=no, email=no" />
	<title>专业代办服务</title>
	<meta name="keywords" content="" />
	<meta name="description" content="" />
	<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
	<script src="../static/2025/m0604/js/flexible.js"></script>
	<link rel="stylesheet" href="../static/2025/m0604/css/swiper-bundle.min.css" />
	<script src="../static/2025/m0604/js/swiper-bundle.min.js"></script>
	<link rel="stylesheet" href="../static/2025/m0604/css/aliplayer.css?v=1" />
	<script type="text/javascript" charset="utf-8" src="../static/2024/m0820/js/aliplayer-min.js"></script>
	<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
	<script src="../static/2025/m0604/js/share.js" type="text/javascript"></script>
	<script type="text/javascript" src="https://static.fangxiaoer.com/eventtest/sd/jquery.md5.js"></script>
	<link rel="stylesheet" href="../static/2025/m0604/css/m0604.css?v=2025">
	<script>
		window.alert = function (name) {
			var iframe = document.createElement("IFRAME");
			iframe.style.display = "none";
			iframe.setAttribute("src", 'data:text/plain,');
			document.documentElement.appendChild(iframe);
			window.frames[0].window.alert(name);
			iframe.parentNode.removeChild(iframe);
		};
	</script>
</head>

<body>
	<script>
		var imgUrl = ""
		var title = "";
		var desc = "";
	</script>

	{% include "header_m_csrf.html" %}

	<div class="server">
		<h2>请选择您需要的服务</h2>
		<div class="server_list">
			<div class="server_item">
				<i></i><p>买卖过户：材料齐全且无产权纠纷</p>
			</div>
			<div class="server_item">
				<i></i><p>房产继承：材料齐全且无产权纠纷</p>
			</div>
			<div class="server_item">
				<i></i><p>按揭贷款：符合市场成交价及银行审批标准</p>
			</div>
			<div class="server_item">
				<i></i><p>公积金贷款：符合公积金贷款政策</p>
			</div>
			<div class="server_item">
				<i></i><p>个人信用&小微企业贷：符合银行审批标准</p>
			</div>
		</div>
	</div>

	<div class="contact">
		<h2>联系方式</h2>
		<div class="coli">
			<i>姓名</i>
			<input type="text" id="name" placeholder="请输入真实姓名"/>
		</div>
		<div class="coli">
			<i>手机号</i>
			<input type="number" id="phone" placeholder="请输入手机号" oninput="value=value.replace(/[^\d]/g,'')" maxlength="11" />
		</div>
		<div class="submit">提交申请</div>
	</div>
	<div class="notes">*最终解释权归房小二网所有</div>


	<div class="popp">
		<div class="popp_content">
			<p>您的提交已完成 </p>
			<p>稍后会有工作人员与您联系 </p>
			<div class="popp_btn">关闭</div>
		</div>
	</div>

	<script>
		var ltapi = "https://ltapi.fangxiaoer.com"
		// var ltapi = "http://************:8083"
		var s = ""

		$(function () {
			$(".server_item").click(function () {
				$(this).addClass("sv").siblings().removeClass("sv")
				s = $(this).find("p").text()
			})


			$(".submit").click(function () {
				let name = $("#name").val()
				let phone = $("#phone").val()
				let tm = new Date().getTime()

				if (s === "") {
					alert("请选择您需要的服务")
				}else if (name === "" || name.trim().length < 2 || !/^[\u4e00-\u9fa5]+$/.test(name)) {
					alert("请输入正确输入中文姓名")
				}else if(!phone.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)){
					alert("请输入正确的手机号")
				}else{
					$.ajax({
						type: "POST",
						url: ltapi + "/apiv1/active/optionalForm",
						data: {
							type: '专业代办服务',
							customerName: name,
							customerPhone: phone,
							optional: s,
							md5: $.md5("abcd#1234"+phone + tm),
							timeMillis: tm
						},
						success: function (res) {
							if(res.status == 0){
								alert(res.msg)
								return
							}
							$(".popp").show()
							s = ''
							$("#name").val('')
							$("#phone").val('')
							$(".server_item").removeClass("sv")
						},
						error: function (err) {
							alert("提交失败，请稍后重试")
						}
					})
				}
			})

			$(".popp_btn").click(function () {
				$(".popp").hide()
			})

		})
	</script>
</body>
</html>