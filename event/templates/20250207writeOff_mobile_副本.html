<!DOCTYPE html>
<html lang="en">
<head>
	<meta charset="UTF-8">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<meta name="apple-mobile-we b-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black" />
	<meta name="format-detection" content="telphone=no, email=no" />
	<meta name="format-detection" content="telephone=no" />
	<title></title>
	<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
	<script src="../static/2025/m0207/js/flexible.js"></script>
	<link href="../static/2025/m0207/css/writeOff.css?v=1" rel="stylesheet" type="text/css">

</head>
<body>
<h1>房小二网活动核销页面</h1>
<div class="wmain">
	<div class="">请输入中奖手机号码：</div>
	<div class="wirteoff">
		<input class="phone2" type="number" oninput="if(value.length>11)value=value.slice(0,11)" />
		<i class="codeWrite">号码核销</i>
	</div>

	<div class="infos"></div>
	<div class="confirmOff">确认核销</div>

	<div class="footmore">
		<p>使用说明：</p>
		<p>1、输入中介号码，点击号码核验。</p>
		<p>—核验状态：</p>
		<p>未中奖：表示号码未中奖（不能核销）</p>
		<p>已中奖、已核销：表示号码已中奖，且已领奖（不能核销）</p>
		<p>已中奖、未核销：表示号码已中奖，尚未领奖（可以核销）</p>
		<p>—获取奖品：展示该号码获得的奖品名称</p>
		<p style="margin-top: 0.7rem;">2、核验状态是已中奖，未核销时，出现确认核销按钮。</p>
		<p>点击确认核销后，提供奖品给用户，即完成奖品核销工作。</p>
	</div>
</div>


<div class="fxe_alert"><span></span></div>
<script>
	var time = 2;//消失秒数
	function time1() {//alert计时器
		time--;
		if (time >= 0) {
			setTimeout("time1()", 1000);
		} else {
			$(".fxe_alert").fadeOut();
		}
	}
	//alert显示内容
	function fxe_alert(text1) {
		time = 2;
		$(".fxe_alert span").text(text1);
		var wid = $(".fxe_alert").width();
		$(".fxe_alert").fadeIn();
		setTimeout("time1()", 1000);
	}
</script>

<script>
	$(function(){
		var bid = 66
		// var ltapi = "http://************:8083";
		var ltapi = "https://ltapi.fangxiaoer.com";


		//号码核销
		var zjData=[]
		$('.codeWrite').click(function(){
			let phe2=$(".phone2").val()
			if(!phe2.match(/^1[3,4,5,6,7,8,9]{1}[0-9]{1}[0-9]{8}$/)){
				fxe_alert('请正确输入中奖手机号码')
			}else{

				$.ajax({
					type: "POST",
					url: ltapi + "/apiv1/other/checkPrizeDrawHistoryMobile",
					data: {
						baseId:bid,
						mobile:phe2
					},
					success: function (res) {
						if (res.status == 1) {
							if(res.content){
								zjData=res.content
								$(".infos").html(`<p>核销状态：已中奖，${res.content.state==0?'未核销':'已核销'}。</p><p>获取奖品：${res.content.giftname}</p>`)
								if(res.content.state==0){
									$(".infos,.confirmOff").show()
								}else{
									$(".infos").show()
									$(".confirmOff").hide()
								}
							}else{
								fxe_alert('没有中奖记录')
							}
						}else{
							fxe_alert(res.msg)
						}
					}
				})
			}
		})
		//确认核销
		$('.confirmOff').click(function(){
			let a=getQueryString('activityId')
			let b=getQueryString('dustId')
			let phe2=$(".phone2").val()
			if(a && b && zjData && phe2){
				$.ajax({
					type: "POST",
					url: ltapi + "/apiv1/other/prizeVerificationByQRCode",
					data: {
						historyId: zjData.id,
						dustId: b,
						activityId: a,
						mobile:phe2
					},
					success: function(res) {
						if (res.status == 1) {
							fxe_alert('核销成功')
							$(".confirmOff").hide()
						} else {
							fxe_alert(res.msg)
						}
					}
				})
			}else {
				fxe_alert("参数不全")
			}
		})
	})

	//获取参数
	function getQueryString(name) {
		var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
		var r = window.location.search.substr(1).match(reg);
		if (r != null) return unescape(r[2]);
		return null;
	}
</script>
</body>
</html>