<!DOCTYPE html>
<html>
<head>
	<meta charset="UTF-8">
	<meta name="viewport"
				content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
	<meta name="apple-mobile-we b-app-capable" content="yes" />
	<meta name="apple-mobile-web-app-status-bar-style" content="black" />
	<meta name="format-detection" content="telphone=no, email=no" />
	<meta name="format-detection" content="telephone=no" />
	<meta name="viewport" content="width=device-width, initial-scale=1.0">
	<meta name=" format-detection" content="telephone=no">
	<title>[在沈阳·住好房——2024沈阳金秋购房季活动]房小二网</title>
	<meta name="keywords" content="房小二网,房小二,沈阳市房产局,沈阳市房地产协会" />
	<meta name="description" content="2024沈阳秋季房交会将在辽宁工业展览馆进行线下集中展示。房小二网让买房卖房更简单" />
	<meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />

	<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
	<link href="../static/2024/m0820/css/allheader.css" rel="stylesheet" type="text/css">
	<script src="../static/2024/m0820/js/flexible.js"></script>
	<!-- <link rel="stylesheet" href="../static/2024/m0820/css/swiper.min.css"> -->
	<link rel="stylesheet" href="../static/2024/m0820/css/m0820.css?t=7">


	<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
	<script src="../static/2024/m0820/js/share.js" type="text/javascript"></script>
	<script type="text/javascript" src="https://static.fangxiaoer.com/eventtest/sd/jquery.md5.js"></script>


	<link rel="stylesheet" href="../static/2024/m0820/css/swiper-bundle.min.css" />
	<script src="../static/2024/m0820/js/swiper-bundle.min.js"></script>


	<!-- 预览图片 -->
	<link rel="stylesheet" type="text/css" href="../static/2024/m0820/dist/skin.css" />
	<!-- 阿里播放器 -->
	<link rel="stylesheet" href="../static/2024/m0820/css/aliplayer.css?v=69" />
	<script type="text/javascript" charset="utf-8" src="../static/2024/m0820/js/aliplayer-min.js"></script>


</head>

<body>
<script>
	var imgUrl = 'https://event.fangxiaoer.com/static/2024/m0820/img/shareImgwx.jpg'
	var title = '在沈阳·住好房——2024沈阳金秋购房季活动重磅来袭'
	var desc = '沈阳秋季房交会 时间：9月10日-9月13日'
</script>
{% include "header.html" %}

<div class="page" id="app">
	<!--1 背景图片   -->
	<div class="page-banner-img cont-1"></div>

	<div class="page-cont">
		<!--2 信息 -->
		<div class="msg-time-pageview pageW  cont-2">
			<div class="msg-time-pageview-cont1">
				<div class="msg-time-pageview-title"></div>
				<div class="msg-time-pageview-time"></div>
			</div>
			<div class="msg-time-pageview-cont2">
				<div class="msg-time-pageview-cont2-2">
					<div class="msg-time-pageview-text">参展企业</div>
					<div class="msg-time-pageview-num msg-time-pageview-num1"></div>
				</div>
				<div class="msg-time-pageview-cont2-2">
					<div class="msg-time-pageview-text">参展项目</div>
					<div class="msg-time-pageview-num msg-time-pageview-num2"></div>
				</div>
				<div class="msg-time-pageview-cont2-2">
					<div class="msg-time-pageview-text">浏览量</div>
					<div class="msg-time-pageview-num msg-time-pageview-num3"></div>
				</div>
			</div>
		</div>


		<!--3 助力 -->
		<div class="help-cont pageW cont-3">
			<i class="help-cont-icon"></i>
			<div class="page-title page-title-3"></div>
			<div class="help-cont2"><img src="https://event.fangxiaoer.com/static/2024/m0820/img/help.png" alt="" />
			</div>
			<div class="help-cont3">已为沈城地产加油助力<b>11001</b>次</div>
		</div>

		<!-- 抽奖跳转小程序或h5 -24 -->
		<div class="cont-24 pageW">
			<div class="cont-24-qrcode">
				<!-- <img src="../static/2024/0531/img/z/a21.png"> -->
				<!-- <img class="code-img-24" src="../static/2024/0531/img/2.jpg"> -->
			</div>
			<div class="choujiang-container">
				<img class="choujiang-image" src="https://event.fangxiaoer.com/static/2024/m0820/img/raffle.png" alt="" />
			</div>
		</div>

		<!--4 政策 文天 -->
		<div class="cont-4 pageW module-bgContainer-list1">
			<div class="list-item-image1">
				<img class="img-policy" src="" />
			</div>
			<div class="list-item-right1">
				<div class="list-item-right-title1"></div>
				<div class="list-item-right-subtitle1"></div>
			</div>
		</div>

		<!--5 图片切换控件--1大图+4缩略图 -->
		<div class="cont-5 img-swiper-thumbnail-4small-swiper1 pageW">
			<div class="page-title page-title-5"></div>
			<div class="swiper img-swiper-4-big">
				<div class="swiper-wrapper">
				</div>
				<div class="swiper-button-next img-swiper-thumbnail-4small-swiper1-next"></div>
				<div class="swiper-button-prev img-swiper-thumbnail-4small-swiper1-prev"></div>
			</div>
			<div thumbsSlider="" class="swiper img-swiper-4-small">
				<div class="swiper-wrapper"></div>
			</div>
		</div>

		<!--6 现场图片 -->
		<div class="cont-6 module-6-container pageW">
			<div class="page-title page-title-6"></div>
			<div class="module-6-grid-content module-6-page"></div>
			<div class="canzhan-more-padding"
					 style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
				<div class="canzhan-more-btn module-6-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more1.png" alt="" />
					<!-- <p>查看更多</p> -->
				</div>
			</div>
		</div>

		<!--7 高光时刻 -->
		<div id="ceshi" class="cont-7 pageW second-house">
			<div class="page-title page-title-7"></div>
			<ul class="highlight-video-list"></ul>
		</div>

		<!-- 8 领导讲话 -->
		<div class="cont-8 pageW second-house">
			<div class="page-title page-title-8"></div>
			<ul class="leader-speak-video-list module-8-page"></ul>
			<div class="canzhan-more-padding"
					 style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
				<div class="canzhan-more-btn module-8-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more1.png" alt="" />
					<!-- <p>查看更多</p> -->
				</div>
			</div>
		</div>

		<!--9 领导发言 -->
		<div class="cont-9 module-6-container pageW">
			<div class="module-9-content">
				<div class="page-title page-title-9"></div>

				<div class="swiper-container-card-wall" id="lingdaofayanswiper">
					<div class="swiper-wrapper">

					</div>
				</div>
			</div>
		</div>

		<!--10 现场直击 -->
		<div class="cont-10 module-6-container pageW module-gradient">
			<!-- <div class="module-6-content "> -->
			<div class="page-title module-6-title-padding page-title-10"></div>
			<div class="zhiji-padding">
				<img src="https://event.fangxiaoer.com/static/2024/m0820/img/img-show-cont.png?t=1" alt="" />
			</div>
			<!-- </div> -->
		</div>

		<!-- 现场政策11 -->
		<div class="cont-11 pageW module-bgContainer2">
			<div class="page-title page-title-11"></div>
			<ul id="policy-list">
			</ul>
		</div>

		<!--12 秋交会看点 -->
		<div class="cont-12 pageW module-bgContainer3">
			<div class="page-title page-title-12"></div>
			<img class="image-full" src="https://event.fangxiaoer.com/static/2024/m0820/img/dialog.png?t=7" />
		</div>

		<!--13 政策咨询 -->
		<div class="cont-13 pageW">
			<div class="page-title page-title-13"></div>
			<div class="policy-item-box">
				<div>
					<img class="policy-item-img"
							 src="https://event.fangxiaoer.com/static/2024/m0820/img/policy-advice.png" alt="">
				</div>

				<div class="policy-action-view">
					<div class="policy-action-view-call">
						<a class="full-corver" href="tel:************"></a>
					</div>
					<div class="policy-action-view-preview">
						<a class="full-corver"
							 href="https://event.fangxiaoer.com/static/2024/m0820/js/fangxiaoer2024.pdf"
							 target="downloadFile" download></a>
					</div>
				</div>
			</div>
		</div>

		<!--14 小程序码及跳转 -->
		<div class="cont-14 pageW">
			<div class="cont-14-qrcode">
				<!-- <img src="../static/2024/0531/img/z/a21.png"> -->
				<!-- <img class="code-img-14" src="../static/2024/0531/img/2.jpg"> -->
			</div>
			<div class="choujiang-container-14">
				<img loading="lazy" class="choujiang-image-14" src="https://event.fangxiaoer.com/static/2024/m0820/img/smtz.png"
						 alt="" />
			</div>
		</div>
		<!-- <div class="cont-14 pageW">
            <div class="module-6-content module-gradient choujiang-container" style="overflow: hidden;">
                <div class="mini-qrcode-14-container ">
                    <div class="mini-qrcode-14-text">
                        了解沈阳最新房产动态<br />少三方提供图片
                    </div>
                    <img class="mini-qrcode-14-img"
                        src="https://img.pconline.com.cn/images/upload/upc/tx/itbbs/2101/25/c1/251135935_1611532823091_mthumb.jpg"
                        alt="" />
                </div>
            </div>
        </div> -->

		<!-- 发评论15 -->
		<div class="cont-15 pageW module-bgContainer3">
			<div class="page-title page-title-15"></div>
			<ul class="cont-15-ul">
				<!-- <li class="cont-15-li">
					<div class="cont-15-img"><img src="https://images1.fangxiaoer.com/oss_images/event/2024/08/28/20240828104512920.png" alt="" /></div>
					<div class="cont-15-div">
						<h5 class="cont-15-h5">神秘人啊</h5>
						<p class="cont-15-p">房产局主办的，应该规模很大，正好最近在看房子，看看有什么优惠</p>
					</div>
				</li>
				<li class="cont-15-li">
					<div class="cont-15-img"><img src="https://images1.fangxiaoer.com/oss_images/event/2024/08/28/20240828104512920.png" alt="" /></div>
					<div class="cont-15-div">
						<h5 class="cont-15-h5">神秘人啊</h5>
						<p class="cont-15-p">房产局主办的，应该规模很大，正好最近在看房子，看看有什么优惠</p>
					</div>
				</li> -->
			</ul>
			<div class="input_bg">
				<div class="input_msg">
					<h5>提示:</h5>
					<p>请使用微信小程序搜索“房小二网小程序”，进入小程序后通过首屏房交会活动入口参与评论，感谢您的支持~!</p>
				</div>
			</div>

			<!-- <img class="image-full" src="https://event.fangxiaoer.com/static/2024/m0820/img/comment.png" /> -->
			<!-- <div class="input_bgContainer">
				<input class="input-style" type="text" id="commentInput" placeholder=" 请输入内容" maxlength="50"
					   cursor-spacing="15" confirm-type="send" @confirm="sendMessage" @input="onInput" />
				<div class="send-bg" @click="sendMessage">发送</div>
			</div> -->

		</div>

		<!-- 16（新房楼盘列表+tab）（需要后台）  -->
		<div class="cont-16 new-house-swiper pageW">
			<div class="page-title page-title-16"></div>
			<div class="new-house-swiper-nav swiper">
				<div class="swiper-wrapper"></div>
			</div>
			<ul class="new-house-list"></ul>
			<a href="" class="more-new" target="_blank"></a>
		</div>

		<!--17 参展品牌 -->
		<div class="cont-17 module-6-container pageW module-gradient">
			<div class="page-title page-title-17"></div>
			<div class="canzhan-container pinpai">

			</div>
			<div class="canzhan-more-padding"
					 style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
				<div class="canzhan-more-btn pinpai-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more2.png" alt="" />
				</div>
			</div>
		</div>

		<!--18 参展项目  -->
		<div class="cont-18 module-6-container pageW module-gradient">
			<div class="page-title page-title-18"></div>
			<div class="canzhan-container xiangmu">

			</div>
			<div class="canzhan-more-padding"
					 style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
				<div class="canzhan-more-btn xiangmu-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more2.png" alt="" />
				</div>
			</div>
		</div>

		<!--19 参展中介 -->
		<div class="cont-19 module-6-container pageW module-gradient">
			<div class="page-title page-title-19"></div>
			<div class="canzhan-container zhongjie">

			</div>
			<div class="canzhan-more-padding"
					 style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
				<div class="canzhan-more-btn zhongjie-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more2.png" alt="" />
				</div>
			</div>
		</div>

		<!-- 20 往期回顾 -->
		<div class="cont-20 pageW second-house">
			<div class="page-title page-title-20"></div>

			<div class="swiper review-swiper" id="showul001">
				<div class="swiper-wrapper">

				</div>
			</div>
		</div>

		<!-- 21 特价房源推荐 -->
		<div class="cont-21 pageW second-house">
			<div class="page-title page-title-21"></div>

			<div id="img-top-1" class="discount-house-box">
				<img src="https://event.fangxiaoer.com/static/2024/m0820/img/specialHouse1.png?t=7" alt="">
				<div class="action-call-view-top">
					<a class="full-corver-1" href=""></a>
				</div>
			</div>
			<div id="img-top-2" class="discount-house-box">
				<img src="https://event.fangxiaoer.com/static/2024/m0820/img/specialHouse2.png?t=7" alt="">
				<div class="action-call-view-bottom">
					<a class="full-corver-2" href=""></a>
				</div>
			</div>
		</div>

		<!--22 小二帮帮忙 -->
		<div class="cont-22 help-cont pageW module-bgContainer3">
			<div class="page-title page-title-22"></div>
			<div class="module-bgContainer4">
				<img class="image-buy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/btn-buy.png" />
				<img class="image-sale"
						 src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/btn-sale.png" />
			</div>
		</div>

		<!--23 精选房源  二手房房源列表（需要后台）  -->
		<div class="cont-23 pageW second-house">
			<div class="page-title page-title-23"></div>
			<ul class="second-house-list"></ul>
			<a class="more-sec" target="_blank" href=""></a>
		</div>

	</div>

	<!-- 视频播放窗口 -->
	<div class="video-view">
		<div class="top-back">
			<div class="return"></div>
		</div>
	</div>
</div>

<div class="loader-container">
	<div class="loader">
		<div id="loader-ld">
			<div></div>
			<div></div>
			<div></div>
			<div></div>
			<div></div>
			<div></div>
			<div></div>
		</div>
	</div>
</div>


<div class="fxe_alert"><span></span></div>
{% include "footer.html" %}
<script>
	var time = 2 //消失秒数
	function time1() { //alert计时器
		time--
		if (time >= 0) {
			setTimeout('time1()', 1000)
		} else {
			$('.fxe_alert').fadeOut()
		}
	}

	//alert显示内容
	function fxe_alert(text1) {
		time = 2
		$('.fxe_alert span').text(text1)
		var wid = $('.fxe_alert').width()
		$('.fxe_alert').fadeIn()
		setTimeout('time1()', 1000)
	}
</script>

<script>
	var ltapi = 'https://ltapi.fangxiaoer.com'
	var activityId = 95
	var dustId = 14751


	/*var ltapi = "http://********:8083"
 var activityId = 96
 var dustId = 11127*/


	var pageWidth = window.innerWidth

	var newProjectLsit = []
	var saleHouseList = []

	var defHideList = [6, 9, 10, 14, 17, 18, 19, 24]
	for (const key in defHideList) {
		$('.cont-' + defHideList[key]).hide()
	}

	var currentDate = new Date()

	function formatLocalDate(date) {
		return (
			date.getFullYear() + '-' +
			(date.getMonth() + 1).toString().padStart(2, '0') + '-' +
			date.getDate().toString().padStart(2, '0')
		)
	}

	var formattedDate = formatLocalDate(currentDate)
	$('.msg-time-pageview-time').html('更新时间：' + formattedDate)


	$(document).ready(function() {
		$.ajax({
			type: 'POST',
			url: ltapi + '/apiv1/wechat/viewVoteComments',
			data: {
				activityId: activityId,
				dustId: dustId,
				page: 1,
				pageSize: 6
			},
			success: function(data) {
				console.log(data)
				if (data.status == 1) {
					data = data.content
					for (var i = 0; i < data.length; i++) {
						inputMsg = `<li class="cont-15-li">
										<div class="cont-15-img"><img src="` + data[i].fromMemberPic + `" alt="" /></div>
										<div class="cont-15-div">
											<h5 class="cont-15-h5">` + data[i].memberName + `</h5>
											<p class="cont-15-p">` + data[i].commentDetail + `</p>
										</div>
									</li>`
						$('.cont-15-ul').append(inputMsg)
					}
				}

			}
		})


		// 雷文天
		//现场政策item点击事件
		$('body').on('click', '.policyItemClick', function(event) {
			var newsId = $(this).attr('news-Id')
			if (pageWidth < 750) { //手机
				window.open('https://m.fangxiaoer.com/news/' + newsId + '.htm')
			} else {
				window.open('https://sy.fangxiaoer.com/news/' + newsId + '.htm')
			}

		})
		$('.module-bgContainer-list1').click(function() {
			var newsId = $(this).attr('data')
			//政策点击
			if (pageWidth < 750) { //手机
				window.open('https://m.fangxiaoer.com/news/' + newsId + '.htm')
			} else {
				window.open('https://sy.fangxiaoer.com/news/' + newsId + '.htm')
			}
		})

		//我要买房
		$('.image-buy').click(function() {

			if (pageWidth < 750) { //手机
				window.open('https://m.fangxiaoer.com/normalnew.htm')
			} else {
				window.open('https://sy.fangxiaoer.com/helpSearch')
			}
		})

		//我要卖房
		$('.image-sale').click(function() {
			//政策点击

			if (pageWidth < 750) { //手机
				window.open('https://m.fangxiaoer.com/entrustSale')
			} else {
				window.open('https://sy.fangxiaoer.com/static/saleHouse/saleHouse.htm')
			}

		})

		// 发评论
		$('.input_bg').click(function() {
			$('.input_msg').fadeIn(100, function() {
				setTimeout(function() {
					$('.input_msg').fadeOut(300)
				}, 3000)
			})


		})
		// 雷文天


		// 助力交互
		$('.help-cont2').click(function() {
			$.ajax({
				type: 'POST',
				url: ltapi + '/apiv1/wechat/cheerForActivity',
				data: {
					activityId: activityId
				},
				success: function(data) {
					canClick = false
					setTimeout(function() {
						canClick = true
					}, 100)
					if (data.status == 1) {
						$('.help-cont3 b').text(data.content)
						fxe_alert('感谢您为沈城地产助力')
					} else {
						fxe_alert('感谢您为沈城地产助力')
					}
				}
			})
		})

		$('.module-6-morebtn').hide() // 第6个更多按钮
		$('.module-8-morebtn').hide() // 第8个更多按钮
		$('.pinpai-morebtn').hide()
		$('.xiangmu-morebtn').hide()
		$('.zhongjie-morebtn').hide()

		// 浏览量
		$.ajax({
			type: 'POST',
			url: ltapi + '/apiv1/wechat/housingFairTopicPv',
			data: {
				activityId: activityId
			},
			success: function(data) {
				if (data.status == 1) {
					console.log(data)
					$('.msg-time-pageview-num3').text(data.content.pvTotal)
					$('.help-cont3 b').text(data.content.cheerTotal)
				} else {
					console.log(data)
				}
			}
		})


		// 页面数据
		$.ajax({
			type: 'POST',
			url: ltapi + '/apiv1/wechat/housingFairTopicIndex',
			data: {
				activityId: activityId
			},
			success: (data) => {
				$('.loader-container').hide()
				if (data.status == 1) {
					data = data.content
					console.log(data)
					tabName = data.tabName
					for (const key in tabName) {
						let keytext
						keytext = key.replace(/^tab(\d+)/, '$1')

						$('.page-title-' + keytext).text(tabName[key])
					}
					$('.msg-time-pageview-num1').text(data.redu.companyCount)
					$('.msg-time-pageview-num2').text(data.redu.projectCount)

					newProjectType = data.newProjectType.list


					saleHouse = data.saleHouse
					secProjectLsitFun(saleHouse)
					xianchangzhiji = data.xianchangzhiji.list


					for (const index in newProjectType) {
						let parttext = newProjectType[index].id
						let partBum = 'part' + parttext
						var newProjectLsitChange = data.newProject[partBum]
						newProjectLsitFun(newProjectLsitChange, parttext)
					}

					if (pageWidth < 750) {
						$('.more-new').attr('href', 'https://m.fangxiaoer.com/fang1l/r' + newProjectType[0].id)
					} else {
						$('.more-new').attr('href', 'https://sy.fangxiaoer.com/houses/r' + newProjectType[0].id)
					}


					// 判断每个模块的显隐
					tabStatus = data.tabStatus

					for (const key in tabStatus) {
						let keytext
						keytext = key.replace(/^tab(\d+)/, '$1')
						if (tabStatus[key] === '1') {
							$('.cont-' + keytext).show()
						} else {
							$('.cont-' + keytext).hide()
						}
					}


					// 文天 start
					//政策
					$('.list-item-right-title1').text(data.zhengce.title)
					$('.list-item-right-subtitle1').text(data.zhengce.description)
					$('.img-policy').attr('src', data.zhengce.pic)
					$('.module-bgContainer-list1').attr('data', data.zhengce.newsId)

					//现场政策
					for (var i = 0; i < data.xianchangzhengce.field.length; i++) {
						imgList = `<li class="policyItemClick" news-Id="` + data.xianchangzhengce.field[i].newsId + `">
									   <div class="module-item-bgPolicy">
										<div class="list-item-title-policy">` + data.xianchangzhengce.field[i].title + `</div>
										<div class="list-item-image-policy">
										   <img loading="lazy" src="` + data.xianchangzhengce.field[i].pic + `"/>
										</div>
									   </div>
									   <div class="line-view"></div>
									   </a >
									  </li>`
						$('.module-bgContainer2').append(imgList)
					}

					// 文天 end


					// 东坡 start
					$('.choujiang-image').click(function() {

						let appid = data.lottery.appid
						let appPageRouting = data.lottery.appPageRouting
						let appUserName = data.lottery.appUserName

						/*let appid = 'wxcd26a86449fb9028'
						let appPageRouting = 'pages/index/index'
						let appUserName = 'gh_eddf6fd9df38'*/

						console.log(appid)


						let h5Url = data.lottery.h5Url

						if (appid != null && appid != undefined && appid != '') { // 需要跳转小程序
							var pragmdata = {
								'wxappId': data.lottery.appid,
								'path': appPageRouting,
								'userName': appUserName
							}

							var userAgent = navigator.userAgent
							if (userAgent.match(/MicroMessenger/i)) {
								//当前页面在微信小程序中
								window.open('weixin://dl/business/?appid=' + appid +
									'&path=' + appPageRouting, '_blank')
							} else if (window.html_show && typeof window.html_show.appNeedFun ===
								'function') {
								if (appUserName != '' && appUserName != null && appUserName != undefined && appPageRouting != '' && appPageRouting != null && appPageRouting != undefined) {
									//当前页面在安卓设备app中
									try {
										window.html_show.appNeedFun(JSON.stringify(pragmdata))
									} catch (e) {
										//TODO handle the exception
										window.location.href = 'weixin://dl/business/?appid=' + appid +
											'&path=' + appPageRouting
									}
								}

							} else if (window.webkit != undefined) {
								if (appUserName != '' && appUserName != null && appUserName != undefined && appPageRouting != '' && appPageRouting != null && appPageRouting != undefined) {
									//当前页面在iOS app中
									try {
										window.webkit.messageHandlers.appNeedFun.postMessage(JSON
											.stringify(pragmdata))
									} catch (e) {
										//TODO handle the exception
										window.location.href = 'weixin://dl/business/?appid=' + appid +
											'&path=' + appPageRouting
									}
								}

							} else if (/Mobile|Android|webOS|iPhone|iPad|BlackBerry/i.test(navigator
								.userAgent)) {
								//当前页面在移动端浏览器中
								window.open('weixin://dl/business/?appid=' + appid +
									'&path=' + appPageRouting, '_blank')
							} else {
								// pc端显示小程序二维码图片
								// window.open(data.lottery.appQrCode, '_blank');
							}


						} else if (h5Url != null && h5Url != undefined && h5Url !=
							'') { // h5链接
							window.open(h5Url, '_blank')
						}

					})
					// 抽奖模块PC二维码悬浮窗 start
					if (/Mobile|Android|webOS|iPhone|iPad|BlackBerry/i.test(navigator.userAgent) == false) {
						//去领券
						let qrCodeImg = ''
						if (data.lottery.appQrCode != '' && data.lottery.appQrCode != null && data.lottery.appQrCode != undefined) {
							qrCodeImg = data.lottery.appQrCode
						} else if (data.lottery.qrCode != '' && data.lottery.qrCode != null && data.lottery.qrCode != undefined) {
							qrCodeImg = data.lottery.qrCode
						}
						if (qrCodeImg != '' && qrCodeImg != undefined && qrCodeImg != null) {
							//去领券
							$('.choujiang-container').hover(
								function() {
									$('.cont-24-qrcode').show()
								},
								function() {
									$('.cont-24-qrcode').hide()
								}
							)
							img = `<img class="code-img-24" src="` + qrCodeImg + `">`
							$('.cont-24-qrcode').append(img)
						}
					}
					$('.cont-24-qrcode').hide()
					// 抽奖模块PC二维码悬浮窗 end

					// 抽奖-14 start
					$('.choujiang-image-14').click(function() {

						let appid = data.thisLottery.h5AppId
						let appPageRouting = data.thisLottery.appPageRouting
						let appUserName = data.thisLottery.appUserName

						let h5Url = data.thisLottery.h5Url

						if (appid != null && appid != undefined && appid != '') { // 需要跳转小程序
							var pragmdata = {
								'wxappId': appid,
								'path': appPageRouting,
								'userName': appUserName
							}

							var userAgent = navigator.userAgent
							if (userAgent.match(/MicroMessenger/i)) {
								//当前页面在微信小程序中
								window.open('weixin://dl/business/?appid=' + appid +
									'&path=' + appPageRouting, '_blank')
							} else if (window.html_show && typeof window.html_show.appNeedFun ===
								'function') {
								if (appUserName != '' && appUserName != null && appUserName != undefined && appPageRouting != '' && appPageRouting != null && appPageRouting != undefined) {
									//当前页面在安卓设备app中
									try {
										window.html_show.appNeedFun(JSON.stringify(pragmdata))
									} catch (e) {
										//TODO handle the exception
										window.location.href = 'weixin://dl/business/?appid=' + appid +
											'&path=' + appPageRouting
									}
								}

							} else if (window.webkit != undefined) {
								if (appUserName != '' && appUserName != null && appUserName != undefined && appPageRouting != '' && appPageRouting != null && appPageRouting != undefined) {
									//当前页面在iOS app中
									try {
										window.webkit.messageHandlers.appNeedFun.postMessage(JSON
											.stringify(pragmdata))
									} catch (e) {
										//TODO handle the exception
										window.location.href = 'weixin://dl/business/?appid=' + appid +
											'&path=' + appPageRouting
									}
								}

							} else if (/Mobile|Android|webOS|iPhone|iPad|BlackBerry/i.test(navigator
								.userAgent)) {
								//当前页面在移动端浏览器中
								window.open('weixin://dl/business/?appid=' + appid +
									'&path=' + appPageRouting, '_blank')
							} else {
								// pc端显示小程序二维码图片
								// window.open(data.thisLottery.appQrCode, '_blank');
							}


						} else if (h5Url != null && h5Url != undefined && h5Url != '') { // h5链接
							if (/Mobile|Android|webOS|iPhone|iPad|BlackBerry/i.test(navigator
								.userAgent)) {
								//当前页面在移动端浏览器中
								window.open(h5Url, '_self')
							} else {
								// pc端显示小程序二维码图片
								// window.open(data.thisLottery.appQrCode, '_blank');
							}
							// window.open(h5Url, '_blank');
						}

					})
					// 抽奖模块14-PC二维码悬浮窗 start
					if (/Mobile|Android|webOS|iPhone|iPad|BlackBerry/i.test(navigator.userAgent) == false) {
						//去领券
						let qrCodeImg = ''
						if (data.thisLottery.appQrCode != '' && data.thisLottery.appQrCode != null && data.thisLottery.appQrCode != undefined) {
							qrCodeImg = data.thisLottery.appQrCode
						} else if (data.thisLottery.qrCode != '' && data.thisLottery.qrCode != null && data.thisLottery.qrCode != undefined) {
							qrCodeImg = data.thisLottery.qrCode
						}
						if (qrCodeImg != '' && qrCodeImg != undefined && qrCodeImg != null) {
							//去领券
							$('.choujiang-container-14').hover(
								function() {
									$('.cont-14-qrcode').show()
								},
								function() {
									$('.cont-14-qrcode').hide()
								}
							)
							img = `<img class="code-img-14" src="` + qrCodeImg + `">`
							$('.cont-14-qrcode').append(img)
						}
					}
					$('.cont-14-qrcode').hide()
					// 抽奖模块14-PC二维码悬浮窗 end

					if (data.xianchangtupian.list.length > 6) {
						$('.module-6-morebtn').show()
					}
					// 现场图片
					for (var i = 0; i < data.xianchangtupian.list.length; i++) {
						let item = data.xianchangtupian.list[i]
						imgList = `<div class="module-6-grid-item ">
								<div class="xc-container-item-img"><img
									src="` + item.pic + `"
									alt="" /></div>
								<div class="xc-container-item-title">` + item.title + `</div>
								</div>`
						$('.module-6-grid-content').append(imgList)
					}

					$('.module-6-morebtn').click(function() { // 现场图片更多按钮
						$('.module-6-page>div').show()
						$('.module-6-morebtn').hide()
					})

					vm.img2.splice(0, vm.img2.length)
					// 领导发言
					for (var i = 0; i < data.lingdaofayan.list.length; i++) {
						let item = data.lingdaofayan.list[i]
						lingdaofayanswiper = `<div class="swiper-slide">
									<img loading="lazy" class="card-wall-img" preview="2" src="` + item.pic + `" alt=""/>
								</div>`
						$('#lingdaofayanswiper .swiper-wrapper').prepend(lingdaofayanswiper)
						vm.img2.push(item.pic)
						// console.log('领导发言index' + i)
					}

					new Swiper('#lingdaofayanswiper', {
						effect: 'coverflow',
						slidesPerView: 2,
						spaceBetween: -140,
						centeredSlides: true,
						loop: true,
						speed: 500,
						coverflowEffect: {
							rotate: 0, // slide做3d旋转时Y轴的旋转角度。默认50。
							stretch: 3, // 每个slide之间的拉伸值，越大slide靠得越紧。
							depth: 200, // slide的位置深度。值越大z轴距离越远，看起来越小。
							modifier: 1, // depth和rotate和stretch的倍率，相当于depth*modifier、rotate*modifier、stretch*modifier，值越大这三个参数的效果越明显。默认1。
							slideShadows: false // 开启slide阴影。默认 true。
						}
					})

					// 第10个直击现场
					$('.cont-10').click(function() {
						targetUrl = data.zhijixianchang.obj.href
						if (targetUrl != '' & targetUrl != undefined & targetUrl != null) {
							window.open(targetUrl, '_blank')
						}
					})

					$('.pinpai-morebtn').show()
					// 参展品牌
					for (var i = 0; i < data.brandCompany.length; i++) {
						let item = data.brandCompany[i]
						var AHrefStart = ''
						var AHrefEnd = ''
						if (item.id != '' && item.id != undefined) {//去品牌馆
							if (pageWidth > 750) {// 品牌展馆 sy站
								AHrefStart = `<a href='https://sy.fangxiaoer.com/brandCompany/` + item.id + `.htm'' target="_blank">`
								AHrefEnd = `</a>`

							} else {// 品牌展馆 m站
								AHrefStart = `<a href='https://m.fangxiaoer.com/brandCompany/` + item.id + `.htm''>`
								AHrefEnd = `</a>`
							}
						} else if (item.projectInfo != '' && item.projectInfo != undefined) {
							if (pageWidth > 750) {// 品牌展馆 sy站
								AHrefStart = `<a href='https://sy.fangxiaoer.com/house/` + item.projectInfo + `.htm'' target="_blank">`
								AHrefEnd = `</a>`

							} else {// 品牌展馆 m站
								AHrefStart = `<a href='https://m.fangxiaoer.com/fang1/` + item.projectInfo + `.htm''>`
								AHrefEnd = `</a>`
							}
						}
						imgList = `<div class="canzhan-container-item pinpai-item" >` + AHrefStart + `<img loading="lazy" style="object-fit: cover;" src="` + item.pic + `" alt="" />` + AHrefEnd + `
									</div>`
						$('.pinpai').append(imgList)
					}


					$('.pinpai-morebtn').click(function() { // 参展品牌更多按钮
						$('.pinpai>div').show()
						$('.pinpai-morebtn').hide()
					})

					$('.xiangmu-morebtn').show()
					// 参展项目
					for (var i = 0; i < data.projectList.length; i++) {
						let item = data.projectList[i]
						imgList =
							`<div class="canzhan-container-item-container-text xiangmu-item" data="` +
							item
								.id + '-' + item.type + `">
									<div class="canzhan-item-container">
										<div class="xc-container-item-title canzhan-container-item-text">
											` + item.title + `
										</div>
									</div>
								</div>
								</div>`
						$('.xiangmu').append(imgList)
					}
					$('.xiangmu-item').click(function() { // 参展项目点击
						data = $(this).attr('data')
						brandId = data.split('-')[0]

						type = data.split('-')[1]
						if (brandId != '' && type != '') {
							var pageWidth = window.screen.width
							if (brandId != '') {
								if (type != '' && type != null && type != undefined) {
									brandId = brandId + '-' + type
								}
								let url = ''
								if (pageWidth > 750) {
									url = 'https://sy.fangxiaoer.com/house/' + brandId +
										'.htm' //新房项目 sy站
								} else {
									url = 'https://m.fangxiaoer.com/fang1/' + brandId +
										'.htm' //新房项目 m站
								}
								window.open(url, '_blank')
							}
						}

					})
					$('.xiangmu-morebtn').click(function() { // 参展项目更多按钮
						$('.xiangmu>div').show()
						$('.xiangmu-morebtn').hide()
					})

					$('.zhongjie-morebtn').show()
					// 参展中介
					for (var i = 0; i < data.agency.length; i++) {
						let item = data.agency[i]
						imgList = `<div class="canzhan-container-item-container-text">
									<div class="canzhan-item-container">
										<div class="xc-container-item-title canzhan-container-item-text">
											` + item.title + `
										</div>
									</div>
								</div>`
						$('.zhongjie').append(imgList)
					}
					$('.zhongjie-morebtn').click(function() { // 参展中介更多按钮
						$('.zhongjie>div').show()
						$('.zhongjie-morebtn').hide()
					})
					// 东坡 end


					// 洪斌 start
					//高光时刻
					for (var i = 0; i < data.gaoguangshike.list.length; i++) {
						var itemShow = `<li class="highlight-line" data-video="` + data.gaoguangshike
							.list[i]
							.video + `" data-img="` + data.gaoguangshike.list[i].pic + `">
													<div  class="highlight-item-title">` + data.gaoguangshike.list[i].title + `</div>
													<div  class="highlight-item-des">` + data.gaoguangshike.list[i].description + `</div>
													<div  class="highlight-img-container">
														<img loading="lazy" class="highlight-item-img"
													src="` + data.gaoguangshike.list[i].pic + `" alt="">
														<img loading="lazy" class="highlight-item-play" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/play.png"
													alt="">
													</div>
												</li>`
						$('.highlight-video-list').append(itemShow)
					}
					if (data.lingdaojianghua.list.length > 6) {
						$('.module-8-morebtn').show()
					}
					//领导讲话
					for (var i = 0; i < data.lingdaojianghua.list.length; i++) {
						var itemShow = `<li class="leader-speak-item" data-video="` + data
							.lingdaojianghua.list[
							i].video + `" data-img="` + data.lingdaojianghua.list[i].pic + `">
												    <div class="leader-speak-video-container">
												    <img loading="lazy" class="leader-speak-item-img" src="` + data.lingdaojianghua.list[i].pic + `" alt="">
												    <img loading="lazy" class="leader-speak-item-play" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/play.png" alt="">
											        </div>
											        <div class="leader-speak-item-describe">` + data.lingdaojianghua.list[i].title + `</div>
												  </li>`
						$('.leader-speak-video-list').append(itemShow)
					}
					$('.module-8-morebtn').click(function() { // 领导讲话更多按钮
						$('.module-8-page>li').show()
						$('.module-8-morebtn').hide()
					})

					//往期回顾
					for (var i = 0; i < data.wangqihuigu.list.length; i++) {
						showul001 =
							'<div class="swiper-slide review-swiper-item-border"><img loading="lazy" class="highlight-item-img" preview="1" src="' +
							data.wangqihuigu.list[i].pic + '" alt="" class=""></div>'
						$('#showul001 .swiper-wrapper').prepend(showul001)
						vm.img1.push(data.wangqihuigu.list[i].pic)
					}

					var reviewSwiper = new Swiper('#showul001', {
						slidesPerView: 1.5,
						spaceBetween: 15,
						loop: true,
						pagination: {
							el: '.swiper-pagination',
							clickable: true
						}
					})

					$('.full-corver-1').attr('href', 'tel:' + data.tejiafangyuan.phone1)
					$('.full-corver-2').attr('href', 'tel:' + data.tejiafangyuan.phone2)
					if (pageWidth > 750) {
						$('.policy-action-view-call a').append('<div class=\'showTel\'>************</div>')
						$('.policy-action-view-call a').removeAttr('href')

						topCallImg = `<div class='showHouseTel'>` + data.tejiafangyuan.phone1 + `</div>`
						$('.action-call-view-top a').append(topCallImg)
						$('.action-call-view-top a').removeAttr('href')

						bottomCallImg = `<div class='showHouseTel'>` + data.tejiafangyuan.phone2 + `</div>`
						$('.action-call-view-bottom a').append(bottomCallImg)
						$('.action-call-view-bottom a').removeAttr('href')
					}

					if (data.tejiafangyuan.phone1 == null || data.tejiafangyuan.phone1 ==
						undefined || data.tejiafangyuan.phone1 == '') {
						$('#img-top-1').hide()
					}

					if (data.tejiafangyuan.phone2 == null || data.tejiafangyuan.phone2 ==
						undefined || data.tejiafangyuan.phone2 == '') {
						$('#img-top-2').hide()
					}
					// 洪斌 end


					if (xianchangzhiji.length != 0) {
						for (var i = 0; i < xianchangzhiji.length; i++) {
							biglist = `<div class="swiper-slide"><p class="img-swiper-4-big-p">` +
								xianchangzhiji[i].title + `</p><img loading="lazy" src="` + xianchangzhiji[i].pic +
								`" /></div>`
							smalllist = `<div class="swiper-slide"><img loading="lazy" src="` + xianchangzhiji[i].pic +
								`" /></div>`
							$('.img-swiper-4-big .swiper-wrapper').append(biglist)
							$('.img-swiper-4-small .swiper-wrapper').append(smalllist)
						}
					} else {
						$('.cont-5').hide()
					}

					$('.more-sec').hide()
					if (data.saleHouse['house-total'] > 3) {
						$('.more-sec').show()
						if (pageWidth < 750) {
							$('.more-sec').attr('href', 'https://m.fangxiaoer.com/fang2')
						} else {
							$('.more-sec').attr('href', 'https://sy.fangxiaoer.com/saleHouses/')
						}
					}


					for (var i = 0; i < newProjectType.length; i++) {
						newProjectTypeLsit = ` <div class="swiper-slide" data="` + newProjectType[i].id + `">` +
							newProjectType[i].name + `</div>`
						$('.new-house-swiper-nav .swiper-wrapper').append(newProjectTypeLsit)
					}
					$('.new-house-swiper-nav>div>div').eq(0).addClass('hover')


					var swiperNav = new Swiper('.new-house-swiper-nav', {
						slidesPerView: 4.3,
						spaceBetween: 10,
						resistanceRatio: 0,
						loop: true
					})


					var swiperSlides = document.querySelectorAll('.new-house-swiper-nav .swiper-slide')
					swiperSlides.forEach(function(slide) {
						slide.addEventListener('click', function() {
							$('.new-house-swiper-nav .swiper-slide').removeClass('hover')
							// 获取 slide 上的属性
							var newTypeId = this.getAttribute('data')

							// 可以在这里做更多事情，比如显示信息

							$(this).addClass('hover')
							if ($('.new-house-list-' + newTypeId).find('li').length > 5) {
								$('.new-house-list-' + newTypeId).find('li').slice(6).hide()
								$('.more-new').show()
							} else {
								$('.more-new').hide()
							}

							if (pageWidth < 750) {
								$('.more-new').attr('href', 'https://m.fangxiaoer.com/fang1l/r' + newTypeId)
							} else {
								$('.more-new').attr('href', 'https://sy.fangxiaoer.com/houses/r' + newTypeId)
							}
							$('.new-house-list ul').hide()
							$('.new-house-list-' + newTypeId).show()

							$('.more-new').attr('href', 'https://m.fangxiaoer.com/fang1l/r' + newTypeId)


						})
					})


					vm.$previewRefresh()

					var swiper4Small = new Swiper('.img-swiper-4-small', {
						loop: true,
						spaceBetween: 10,
						slidesPerView: 4,
						freeMode: true,
						watchSlidesProgress: true
					})
					var swiper4Big = new Swiper('.img-swiper-4-big', {
						loop: true,
						spaceBetween: 10,
						navigation: {
							nextEl: '.img-swiper-thumbnail-4small-swiper1-next',
							prevEl: '.img-swiper-thumbnail-4small-swiper1-prev'
						},
						thumbs: {
							swiper: swiper4Small
						}
					})


				}
			}

		})

		function secProjectLsitFun(data) {
			$('.second-house-list').html('')
			data = data['house-list']
			var priceMoney
			var secHref
			for (var i = 0; i < data.length; i++) {
				if (data[i].title != undefined && data[i].title != '') {

					if (data[i].price != null) {
						priceMoney = data[i].price + '<i>万</i>'
					} else {
						priceMoney = '待定'
					}

					var houseTrait = (data[i].houseTrait).split(',')
					var sec_charact = ''
					for (var j = 0; j < 2; j++) {
						if (houseTrait[j] != '' && houseTrait[j] != null) {
							sec_charact += '<div class="second-house-charact-li">' + houseTrait[j] + '</div>'
						}
					}

					if (pageWidth < 750) {
						secHref = `https://m.fangxiaoer.com/fang2/` + data[i].houseId + `.htm`
					} else {
						secHref = `https://sy.fangxiaoer.com/salehouse/` + data[i].houseId + `.htm`
					}

					secList = `<li class="">
										<a href="` + secHref + `" class="second-house-link" target="_blank">
											<div class="second-house-img">
												<img loading="lazy" src="` + data[i].pic + `" class="left_tu">
											</div>
											<div class="second-house-right">
												<div class="second-house-title">` + data[i].title + `</div>
												<div class="second-house-area">` + data[i].regionName + `·` + data[i].plantName + `</div>
												<ul class="second-house-charact">` + sec_charact + `</ul>
												<div class="second-house-bottom">
													<span class="second-house-price">` + priceMoney + `</span>
													<div class="second-house-info">
														<span>` + data[i].room + `室` + data[i].hall + `厅 </span>
														<span>` + data[i].area + `㎡ </span>
														<span>` + data[i].forward + `</span>
													</div>
												</div>
											</div>
											<div class="cl"></div>
										</a>
									</li>`
					$('.second-house-list').append(secList)
				} else {
					$('.cont-23').hide()
				}
			}
		}


		function newProjectLsitFun(data, key) {
			$('.new-house-list').append(`<ul class="new-house-list-${key}"></ul>`)
			for (var i = 0; i < data.length; i++) {
				var priceTypeShow
				var priceMoney
				var newHref
				var priceTypeNew = data[i].mPrice
				let Txt1 = '',
					Txt2 = '',
					Txt3 = '',
					Txt4 = '',
					Txt5 = '',
					Txt6 = ''


				if (data[i].isbs == 1) {
					Txt1 = '<div class="unique_item">别墅</div>'
				}
				if (data[i].isgy == 1) {
					Txt2 = '<div class="unique_item">公寓</div>'
				}
				if (data[i].ispz == 1) {
					Txt3 = '<div class="unique_item">普宅</div>'
				}
				if (data[i].issp == 1) {
					Txt4 = '<div class="unique_item">商铺</div>'
				}
				if (data[i].isxzj == 1) {
					Txt5 = '<div class="unique_item">写字间</div>'
				}
				if (data[i].isyf == 1) {
					Txt6 = '<div class="unique_item">洋房</div>'
				}

				if (pageWidth < 750) {
					newHref = `https://m.fangxiaoer.com/fang1/` + data[i].projectId + `-` + data[i].type + `.htm`
				} else {
					newHref = `https://sy.fangxiaoer.com/house/` + data[i].projectId + `-` + data[i].type +
						`.htm`
				}

				if (priceTypeNew != null) {
					if (priceTypeNew.priceType == '均价') {
						priceTypeShow = '<span class="rise">均</span>'
					} else {
						priceTypeShow = '<span class="rise">起</span>'
					}
					priceMoney = data[i].mPrice.priceMoney + '<span class="newhouse_unit">元/㎡</span>'
				} else {
					priceTypeShow = ''
					priceMoney = '待定'
				}

				let showArea = ''
				if (data[i].area != null) {
					showArea = '丨' + data[i].area.minArea + '~' + data[i].area.maxArea + 'm²'
				}


				newList = `<li>
										<a class="newhouse_list" href="` + newHref + `" target="_blank">
											<div class="newhouse_left">
												<img loading="lazy"  src="` + data[i].indexPic + `" class="cover_pic">
											</div>
											<div class="newhouse_right">
												<div class="newhouse_right_top">
													<div class="newhouse_title">` + data[i].projectName + `</div>
												</div>
												<div class="newhouse_address">
													<span>` + data[i].regionName + `·` + data[i].showPlate + showArea + `</span>
												</div>
												<div class="unique_list">` + Txt1 + Txt2 + Txt3 + Txt4 + Txt5 + Txt6 + `</div>
												<div class="newhouse_price">` + priceMoney + priceTypeShow + `</div>
											</div>
										</a>
									</li>`

				$('.new-house-list-' + key).append(newList)
			}
			if ($('.new-house-list-' + key).find('li').length > 5) {
				$('.new-house-list-' + key).find('li').slice(6).hide()
				$('.more-new').show()
			}
		}


	})
	// 洪斌 start
	$('body').on('click', '.highlight-line', function(event) { //height: 7.5rem
		$('.video-view').append('<div id="video-view" style="width: 100%; height: 100%;"></div>')
		var videoSrc = $(this).attr('data-video')
		var videoPic = $(this).attr('data-img')
		aliPlayer('video-view', 'video-view', videoSrc, videoPic)
		$('.video-view').css('display', 'flex')
	})

	$('body').on('click', '.leader-speak-item', function(event) {
		$('.video-view').append('<div id="video-view" style="width: 100%; height: 100%;"></div>')
		var videoSrc = $(this).attr('data-video')
		var videoPic = $(this).attr('data-img')
		aliPlayer('video-view', 'video-view', videoSrc, videoPic)
		$('.video-view').css('display', 'flex')
	})

	$('body').on('click', '.return', function(event) {
		$('.video-view #video-view').remove()
		$('.video-view').css('display', 'none')
	})

	function aliPlayer(vm, dm, vurl, img) {
		var vm = new Aliplayer({
			id: dm,
			width: '100%',
			height: '100%',
			autoplay: true,
			source: vurl,
			cover: img,
			playsinline: false, //是否全屏
			controlBarVisibility: 'always',
			useH5Prism: true
		}, function(player) {
		})
	}

	// 洪斌 end

</script>

<script src="../static/2024/m0820/js/vue.min.js"></script>
<script src="../static/2024/m0820/dist/vue-photo-preview.js" type="text/javascript" charset="utf-8"></script>
<script type="text/javascript">
	var options = {
		fullscreenEl: false, //关闭全屏按钮
		bgOpacity: 0.8,
		preloaderDelay: 4000,
		counterEl: false
	}

	Vue.use(vuePhotoPreview, options)

	var vm = new Vue({
		el: '#appx',
		data: {
			img1: [],
			img2: []
		},
		mounted: function() {
			//异步插入的图片
			setTimeout(() => {
				// vm.img1.push('https://img01.yzcdn.cn/vant/cat.jpeg')
				// vm.img1.push('https://img.yzcdn.cn/vant/apple-1.jpg')
				// vm.img1.push('https://img.yzcdn.cn/vant/apple-2.jpg')
				vm.$previewRefresh()
			}, 2000)

			//图片查看器打开后，打印本次查看器的实例（事件、方法、属性的示例）
			this.$preview.on('imageLoadComplete', (e, item) => {
				// console.log(this.$preview.self)
			})
		}
	})


</script>

</body>

</html>