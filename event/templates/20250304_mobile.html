<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8">
		<meta name="viewport"
			content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
		<meta name="apple-mobile-we b-app-capable" content="yes" />
		<meta name="apple-mobile-web-app-status-bar-style" content="black" />
		<meta name="format-detection" content="telphone=no, email=no" />
		<title>元气满满开学季 安家好房不焦虑</title>
		<meta name="keywords" content="沈阳春季房交会2025, 近名校楼盘推荐, 沈阳房小二网, 购房优惠政策, 开学季安家指南" />
		<meta name="description" content="2025沈阳春季房交会火热开启！房小二网为您精选近名校优质楼盘，政策专家现场解读购房新政，助力家长安家无忧，孩子入学更轻松！活动期间享专属优惠，省心选房、安心置业，抢购黄金教育房源的绝佳机会！立即参与，开启幸福生活新篇章！" />
		
		
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
		<link href="../static/2025/m0304/css/allheader.css" rel="stylesheet" type="text/css">
		<script src="../static/2025/m0304/js/flexible.js"></script>
		<link rel="stylesheet" href="../static/2025/m0304/css/m0304.css?t=4">
		<script src="../static/2025/m0304/js/m0304.js"></script>

		<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
		<script src="../static/2025/m0304/js/share.js" type="text/javascript"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/eventtest/sd/jquery.md5.js"></script>

		<link rel="stylesheet" href="../static/2025/m0304/css/swiper-bundle.min.css" />
		<script src="../static/2025/m0304/js/swiper-bundle.min.js"></script>
		<link rel="stylesheet" href="../static/2025/m0304/css/aliplayer.css" />
		<script type="text/javascript" charset="utf-8" src="https://static.fangxiaoer.com/m/resources/common/js/aliplayer.js"></script>
		<script src="../static/2025/m0304/js/verify.js"></script>
		
	</head>

	<body>
		<script>
			var imgUrl = "https://event.fangxiaoer.com/static/images/promotion/0304-share-f.png"
			var title = "元气满满开学季 安家好房不焦虑";
			var desc = "开学季近名校楼盘优惠集锦，书包房选购全攻略，请查收～";
		</script>

		{% include "header_m_csrf.html" %}


		<div class="page">
			<!--1 背景图 -->
			<div class="cont-1"><img class="plate1-img" src="../static/2025/m0304/img/b/top.png" alt="" /></div>

			<div class="pageMa">
				<!--政策解读 -->
				<!-- <div class="pagebgs"></div>
				<div class="cont-3 pageW second-house">
					<div class="page-title"><span>政策解读</span><i style="width: 2.35rem;"></i></div>
					<ul class="leader-speak-video-list module-8-page">
						<li class="leader-speak-item">
							<div class="leader-speak-video-container">
								<img loading="lazy" class="leader-speak-item-img" src="https://images1.fangxiaoer.com/oss_images/event/2024/11/11/20241111084417536.jpg" alt="">
							</div>
							<div class="leader-speak-item-describe">11•11沈阳市房产•家居联动活动盛大启动</div>
						</li>
						<li class="leader-speak-item">
							<div class="leader-speak-video-container">
								<img loading="lazy" class="leader-speak-item-img" src="https://images1.fangxiaoer.com/oss_images/event/2024/11/11/20241111084417536.jpg" alt="">
							</div>
							<div class="leader-speak-item-describe">11•11沈阳市房产•家居联动活动盛大启动</div>
						</li>
						<li class="leader-speak-item">
							<div class="leader-speak-video-container">
								<img loading="lazy" class="leader-speak-item-img" src="https://images1.fangxiaoer.com/oss_images/event/2024/11/11/20241111084417536.jpg" alt="">
							</div>
							<div class="leader-speak-item-describe">11•11沈阳市房产•家居联动活动盛大启动</div>
						</li>
						<li class="leader-speak-item">
							<div class="leader-speak-video-container">
								<img loading="lazy" class="leader-speak-item-img" src="https://images1.fangxiaoer.com/oss_images/event/2024/11/11/20241111084417536.jpg" alt="">
							</div>
							<div class="leader-speak-item-describe">11•11沈阳市房产•家居联动活动盛大启动</div>
						</li>
					</ul>
					<div class="listMM">
						<a href="https://m.fangxiaoer.com/news/99309.htm">11·11辽宁省房产·家居联动促销活动将于11月9-23日举行<i>></i></a>
						<a href="https://m.fangxiaoer.com/news/99309.htm">11·11辽宁省房产·家居联动促销活动将于11月9-23日举行<i>></i></a>
						<a href="https://m.fangxiaoer.com/news/99309.htm">11·11辽宁省房产·家居联动促销活动将于11月9-23日举行<i>></i></a>
					</div>
				</div> -->


				<!-- 楼盘项目 -->
			<!-- 	<div class="pagebgs"></div>
				<div class="cont-6 pageW">
					<div class="page-title">
						<span>春季房交会推荐近名校楼盘</span><i style="width: 6.45rem;"></i>
					</div>

					<ul class="cont-6-ul-item">
						<li class="cont-6-item">
							<div style="display: flex">
								<div class="newhouse_left">
									<img loading="lazy" src="https://images1.fangxiaoer.com/oss_images/event/2024/11/09/20241109170254667.png" class="cover_pic">
								</div>
								<div class="newhouse_right">
									<div class="newhouse_title">富禹金科繁梦里</div>
									<div class="nhinfo">大东区·东北大马路 | 54~164㎡</div>
									<ul class="itemIconshow"><li><i>普宅</i><i>普宅</i></li></ul>
									<ul class="itemIconshow"><li><p class="itemIconTxt">7000</p><span>元/㎡</span><b>均</b></li></ul>
								</div>
							</div>
							<div class="yhf">
								<div class="yhli">
									<img src="../static/2025/m0304/img/b/tag1.png" alt=""/>
									<div >
										<p>小学：尚品学校东越校区小学部</p>
										<p>中学：尚品学校东越校区中学校区</p>
									</div>
								</div>
								<div class="yhli">
									<img src="../static/2025/m0304/img/b/tag2.png" alt=""/>
									<div >
										<p>1、XXX优惠</p>
										<p>2、AAAA 优惠</p>
										<p>3、购房享九折</p>
									</div>
								</div>
							</div>
						</li>

						<li class="cont-6-item">
							<div style="display: flex">
								<div class="newhouse_left">
									<img loading="lazy" src="https://images1.fangxiaoer.com/oss_images/event/2024/11/09/20241109170254667.png" class="cover_pic">
								</div>
								<div class="newhouse_right">
									<div class="newhouse_title">富禹金科繁梦里</div>
									<div class="nhinfo">大东区·东北大马路 | 54~164㎡</div>
									<ul class="itemIconshow"><li><i>普宅</i><i>普宅</i></li></ul>
									<ul class="itemIconshow"><li><p class="itemIconTxt">7000</p><span>元/㎡</span><b>均</b></li></ul>
								</div>
							</div>
							<div class="yhf">
								<div class="yhli">
									<img src="../static/2025/m0304/img/b/tag1.png" alt=""/>
									<div >
										<p>小学：尚品学校东越校区小学部</p>
										<p>中学：尚品学校东越校区中学校区</p>
									</div>
								</div>
								<div class="yhli">
									<img src="../static/2025/m0304/img/b/tag2.png" alt=""/>
									<div >
										<p>1、XXX优惠</p>
										<p>2、AAAA 优惠</p>
										<p>3、购房享九折</p>
									</div>
								</div>
							</div>
						</li>

					</ul>
				</div>
 -->
				
			</div>
			<div class="finfo">
				<img src="../static/2025/m0304/img/b/gh.jpg" alt=""/>
				<p>3月14日-3月17日可前往辽宁工业展览馆<br>春季购房节活动现场参与抽奖</p>
			</div>
			
		</div>

		<div class="hua"></div>
		<!-- 视频播放窗口 -->
		<div class="video-view">
			<div class="top-back">
				<div class="return"></div>
			</div>
		</div>
	<!-- <script>
		// var ltapi = "https://ltapi.fangxiaoer.com"
		var ltapi = "http://*************:8081";
		var pageWidth = window.innerWidth;
		

		$(document).ready(function() {
			if (pageWidth < 750) {
				$(".plate1-img").attr("src",'../static/2025/m0304/img/b/top.png')
				
			} else {
				$(".plate1-img").attr("src",'../static/2025/m0304/img/b/top-pc.png')
			}
			
			$.ajax({
				type: "GET",
				url: ltapi + "/api/v1/layouts/school-house",
				success: function(data) {
					
					console.log(data);
					blocks = data.blocks
					$(".finfo img").attr("src",data.miniAppQrImageUrl)
					$(".finfo p").html(data.miniAppQrTitle)
					blocks.forEach(item => {
						if (item.type == "MEDIA_TEXT" ) {//资讯展示
							titleImageUrl = item.titleImageUrl 
							itemId = (item.id).slice(0, 6)
							$(".pageMa").append('<div class="cont cont' + itemId + '"></div>');
							
							$(".cont" + itemId).append('<i class="cont-line"></i>');
							$(".cont" + itemId).append(`<div class="cont-box">
															<div class="cont-title"><img src="` + item.titleImageUrl + `" alt="" /></div>
																<ul class="cont-main">
																	<div class="cont-main1"></div>
																	<div class="cont-main2"></div>
																</ul>
															</div>`);
								dataA = item.data;
								dataA.forEach(item => {
						 	    if (item.content != null) {
						 	        const $container1 = $(".cont" + itemId).find('.cont-main1');
						 	        const $container2 = $(".cont" + itemId).find('.cont-main2');
						 	        switch (item.content.mediaType) {
						 	            case 'IMAGE':
						 	                let imageHtml = '<li class="li1">';
						 	                if (item.content.mlink) {
						 	                    imageHtml += `<a href="${item.content.mlink}">`;
						 	                }
						 	                imageHtml += `<img src="${item.content.mediaUrl}" alt="" /><p class="p1">${item.content.title}</p>`;
						 	                if (item.content.mlink) {
						 	                    imageHtml += '</a>';
						 	                }
						 	                imageHtml += '</li>';
						 	                $container1.append(imageHtml);
						 	                break;
						 	
						 	            case 'TEXT':
						 	                const textHtml = `
						 	                    <li class="li2">
						 	                        <a href="${item.content.mlink}" target="_blank">
						 	                            <p class="p2">${item.content.title}</p>
						 	                            <s class="cont-icon-r"></s>
						 	                        </a>
						 	                    </li>`;
						 	                $container2.append(textHtml);
						 	                break;
						 	
						 	            case 'VIDEO':
						 	                const videoHtml = `
						 	                    <li class="li1 VIDEOli"  data="${item.content.mediaUrl}" data-img="${item.content.coverImgUrl}">
						 	                        <i class="cont-video"></i>
						 	                        <img src="${item.content.coverImgUrl}" alt="" />
						 	                        <p class="p1">${item.content.title}</p>
						 	                    </li>`;
						 	                $container1.append(videoHtml);
						 	                break;
						 	
						 	            default:
						 	                console.warn('Unknown mediaType:', item.content.mediaType);
						 	        }
						 	    }
						 	});
						 }else if (item.type == "HOUSE_LIST_BRAND" ){//房源
							$(".pageMa").append('<div class="cont cont6"></div>');
							$(".cont6").append(`<i class="cont-line"></i>
													<div class="cont-box">
														<div class="cont-title"><img src="`+item.titleImageUrl+`" alt="" /></div>
														<ul class="newhouse-list"></ul>	
													</div>`);
							
							newProjectLsitFun(item.data,1)
						 }
					})
				}	
				
			})
			// 视频播放
			$("body").on('click', '.VIDEOli', function(event) {
				$(".music").css("animation-play-state", "paused")
				$(".music").css("-webkit-animation-play-state", "paused")
				
				$(".video-view").append('<div id="video-view" style="width: 100%; height: 100%;"></div>')
				var videoSrc = $(this).attr('data')
				var videoPic = $(this).attr('data-img')
				aliPlayer('video-view', 'video-view', videoSrc, videoPic)
				$(".video-view").css('display', 'flex')
				
			})
			$("body").on('click', '.return', function(event) {
				$(".video-view #video-view").remove()
				$(".video-view").css('display', 'none')
			})
			function aliPlayer(vm, dm, vurl, img) {
				var vm = new Aliplayer({
					id: dm,
					width: '100%',
					height: '100%',
					autoplay: true,
					source: vurl,
					cover: img,
					playsinline: false, //是否全屏
					controlBarVisibility: "always",
					useH5Prism: true,
				}, function(player) {
				});
			}
			function newProjectLsitFun(data,key) {
				console.log(data)	
				$(".newhouse-list").append(`<ul class="new-house-list-${key}"></ul>`)
				for (var i = 0; i < data.length; i++) {
					// console.log(data[i].content)
					var priceTypeShow;
					var priceMoney;
					var newHref;
					var priceTypeNew = data[i].content.price;
				
					if (pageWidth < 750) {
						newHref = `https://m.fangxiaoer.com/fang1/` + data[i].content.projectId + `-` + data[i].content.projectType + `.htm`
					} else {
						newHref = `https://sy.fangxiaoer.com/house/` + data[i].content.projectId + `-` +data[i].content.projectType +
								`.htm`
					}
					
					
					tagsIocn = data[i].content.tags
					var list = ''
					for (var t = 0; t < tagsIocn.length; t++) {
						list += '<div class="unique_item">'+tagsIocn[t]+'</div>'
					}
					
					schools = data[i].content.schools
					policies = data[i].content.policies

					schoolsHtml= ''
					policiesHtml= ''
					if(schools != ''){
						var listSchools = ''
						for (var t = 0; t < schools.length; t++) {
							listSchools += '<p>'+schools[t]+'</p>'
						}
						schoolsHtml = `<div class="yhli">
										<img src="../static/2025/m0304/img/b/tag1.png" alt=""/><div>`+listSchools+`</div>
									</div>`
					}
					if(policies != ''){
						var listPolicies = ''
						for (var t = 0; t < policies.length; t++) {
							listPolicies+= '<p>'+policies[t]+'</p>'
						}
						policiesHtml = `<div class="yhli">
										<img src="../static/2025/m0304/img/b/tag2.png" alt=""/><div>`+listPolicies+`</div>
									</div>`
					}
					
					newList = `<li>
								<a class="newhouse_list" href="` + newHref + `" target="_blank">
									<div style='display: flex'>
										<div class="newhouse_left">
											<img loading="lazy"  src="` + data[i].content.imageUrl + `" class="cover_pic">
										</div>
										<div class="newhouse_right">
											<div class="newhouse_right_top">
												<div class="newhouse_title">` + data[i].content.title + `</div>
											</div>
											<div class="newhouse_address">
												<span>` + data[i].content.location + `丨` + data[i].content.areas  +`</span>
											</div><div class="unique_list">`+list+`</div>
											<div class="newhouse_price">` + data[i].content.price  + `<span class="newhouse_unit">` + data[i].content.priceUnit  + `</span><span class="rise">` + data[i].content.priceType  + `</span></div>
										</div>
									</div>	
									<div class="yhf">`+schoolsHtml+policiesHtml+`</div>
								</a>
							</li>`
							
											
					$(".new-house-list-"+key).append(newList)
				}
				if ($(".new-house-list-"+key).find("li").length > 5) {
					$(".new-house-list-"+key).find("li").slice(6).hide();
					$(".more-new").show()
				}
			}
			
			
			
			
			
		})
		
	</script>
 -->
	</body>
</html>






