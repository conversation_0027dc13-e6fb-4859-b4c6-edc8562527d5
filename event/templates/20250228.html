<!DOCTYPE html>
<html>
	<head>
		<meta charset="UTF-8" />
		<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />
		<meta http-equiv="X-UA-Compatible" content="ie=edge" />
		<script type="text/javascript" src="https://static.fangxiaoer.com/js/jquery.min.js"></script>
		<script src="../static/2025/m0228/js/flexible.js"></script>
		<link rel="stylesheet" href="../static/2025/m0228/css/m1104.css?t=4">

		<title>第三届辽宁省房产家居联动促销活动2025｜房小二网助力购房装修优惠盛宴</title>
		<meta name="keywords" content="辽宁省房产促销, 家居联动优惠, 2025购房补贴, 好房子好家居好生活, 房小二网, 沈阳房天下, 房地产开发企业, 家居建材折扣, 契税补贴政策, 家装焕新套餐" />
		<meta name="description" content="2025年3月11日-31日，辽宁省住建厅、商务厅联合举办第三届“房产·家居联动促销活动”，全省14市及沈抚示范区同步开启！房小二网作为官方支持平台，为您整合房地产开发企业“特别折扣”、家居建材企业优惠套餐、中介服务费减免等多重福利，更有政府购房补贴及契税补贴政策助力。立即参与，抢购优质房源，享一站式家装焕新优惠！" />
		
		<script src="https://res.wx.qq.com/open/js/jweixin-1.0.0.js"></script>
		<script src="../static/2025/m0228/js/share.js" type="text/javascript"></script>
		<script type="text/javascript" src="https://static.fangxiaoer.com/eventtest/sd/jquery.md5.js"></script>

		<link rel="stylesheet" href="../static/2025/m0228/css/swiper-bundle.min.css" />
		<script src="../static/2025/m0228/js/swiper-bundle.min.js"></script>
		<link rel="stylesheet" href="../static/2025/m0228/css/aliplayer.css" />
		<script type="text/javascript" charset="utf-8" src="https://static.fangxiaoer.com/m/resources/common/js/aliplayer.js"></script>
		<script src="../static/2025/m0228/js/verify.js"></script>
	</head>

	<body>
		
		{% include "header.html" %}
		<div class="page">
			<!--1 背景图 -->
			<div class="cont-1"><img class="plate1-img"  alt="" /></div>
			<!--2 活动启动仪式 -->
			<div class="cont-2 pageW img-swiper-thumbnail-4small-swiper1 ">
				<div class="page-title page-title-2"></div>
				<div class="swiper img-swiper-4-big">
					<div class="swiper-wrapper">
					</div>
					<div class="swiper-button-next img-swiper-thumbnail-4small-swiper1-next"></div>
					<div class="swiper-button-prev img-swiper-thumbnail-4small-swiper1-prev"></div>
				</div>
				<div thumbsSlider="" class="swiper img-swiper-4-small">
					<div class="swiper-wrapper"></div>
				</div>
			</div>
			<!--3 活动视频 -->
			<div class="cont-3 pageW second-house">
				<div class="page-title page-title-3"></div>
				<ul class="leader-speak-video-list module-8-page"></ul>
				<div class="canzhan-more-btn module-8-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more1.png" alt="" />
				</div>
			</div>
			
			<!-- 活动报道 -->
			<div class="cont-4 pageW">
				<div class="page-title page-title-4"></div>
				<ul class="cont-4-ul">
					
				</ul>
				<div class="canzhan-more-btn module-4-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more1.png" alt="" />
				</div>
			</div>	
			<!-- 参展城市 -->
			<div class="cont-5 pageW">
				<!-- <div class="page-title page-title-4">参展城市</div> -->
				<img src="../static/2025/m0228/img/cont-5-img.png" alt=""  class="cont-5-img"/>
			</div>	
			<!-- 优惠政策 -->
			<div class="cont-8 ">
				<a href="" class="cont-8-a" target="_blank">
					<img src="" alt=""  class="cont-8-img"/>
				</a>
			</div>	
			<!-- 楼盘项目 -->
			<div class="cont-6 pageW">
				<div class="page-title page-title-6"></div>
				<p class="cont-6-p"></p>
				<ul class="cont-6-ul"></ul>
				<ul class="cont-6-ul-item">	
					
				</ul>
				<div class="canzhan-more-btn module-6-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more1.png" alt="" />
				</div>
			</div>	
			<!-- 优惠信息 -->
			<div class="cont-9">
				<a href="" class="cont-9-a" target="_blank">
					<img src="" alt=""  class="cont-9-img"/>
				</a>
			</div>	
			<!-- 参展建材家居 -->
			<div class="cont-7 pageW">
				<div class="page-title page-title-7">参展建材家居</div>
				<ul class="cont-7-ul"></ul>
				<div class="canzhan-more-btn module-7-morebtn">
					<img loading="lazy" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/see-more1.png" alt="" />
				</div>
			</div>	
			
			
			
		</div>
		<!-- 视频播放窗口 -->
		<div class="video-view">
			<div class="top-back">
				<div class="return"></div>
			</div>
		</div>
		{% include "footer.html" %}
	<script>
		var ltapi = "https://ltapi.fangxiaoer.com"
		// var ltapi = "http://*************:8083"
		// var activityId = 97
		var dustType = 172
		
		/* var ltapi = "http://*************:8083"
		var activityId = 97 */
		
		$(document).ready(function() {
			// 页面数据
			$.ajax({
				type: "POST",
				// url: ltapi + "/apiv1/wechat/projectBenefitsTopicIndex",
				url: ltapi + "/apiv1/other/getSurveyInfosByDustType",
				data: {
					// activityId: activityId
					dustType: dustType
				},
				success: (data) => {
					data = data.content
					console.log(data)
					$(".cont-6-p").html(data.plate1.plate6Title)
					// 判断每个模块的显隐
					tabStatus = data.plateStatus
					for (let plateKey in tabStatus) {
					    if (tabStatus.hasOwnProperty(plateKey)) {
					        const plate = tabStatus[plateKey];
							const plateTitle = plateKey.replace("plate",'')
							$(".page-title-"+plateTitle).text(`${plate.plateName}`)
							
							if(plate.plateStatus == 0){
								$(".cont-"+plateTitle).hide()
							}else{
								$(".cont-"+plateTitle).show()
							}
					    }
					}
					
					
					$(".plate1-img").attr("src",data.plate1.pcBanner)
					$(".cont-8-img").attr("src",data.plate8.pic)
					$(".cont-9-img").attr("src",data.plate9.pic)
					if(data.plate8.newsId == ''){
						$(".cont-8-a").attr("href",data.plate8.url)
					}else{
						$(".cont-8-a").attr("href",'https://sy.fangxiaoer.com/news/'+data.plate8.newsId+'.htm')
					}	
					if(data.plate9.newsId == ''){
						$(".cont-9-a").attr("href",data.plate9.url)
					}else{
						$(".cont-9-a").attr("href",'https://sy.fangxiaoer.com/news/'+data.plate9.newsId+'.htm')
					}
					 
					plate2 = data.plate2
					if(plate2.length != 0){
						for (var i = 0; i < plate2.length; i++) {
							biglist = `<div class="swiper-slide"><p class="img-swiper-4-big-p">` + plate2[i].title + `</p><img loading="lazy" src="` + plate2[i].pic +`" /></div>`
							smalllist = `<div class="swiper-slide"><img loading="lazy" src="` + plate2[i].pic +`" /></div>`
							$(".img-swiper-4-big .swiper-wrapper").append(biglist)
							$(".img-swiper-4-small .swiper-wrapper").append(smalllist)
						}
					}else{
						$(".cont-5").hide()
					}
					
					plate3 = data.plate3
					for (var i = 0; i < plate3.length; i++) {
						var itemShow = `<li class="leader-speak-item" data-video="` + plate3[i].video + `" data-img="` + plate3[i].pic + `">
											<div class="leader-speak-video-container">
											<img loading="lazy" class="leader-speak-item-img" src="` + plate3[i].pic + `" alt="">
											<img loading="lazy" class="leader-speak-item-play" src="https://event.fangxiaoer.com/static/2024/m0820/img/icon/play.png" alt="">
											</div>
											<div class="leader-speak-item-describe">` + plate3[i].title + `</div>
										</li>`
						$(".leader-speak-video-list").append(itemShow)
					}
					if (plate3.length>4) {
						$(".module-8-morebtn").show()
					} else{
						$(".module-8-morebtn").hide()
					}
					$(".module-8-morebtn").click(function() { 
						$(".module-8-page>li").show()
						$(".module-8-morebtn").hide()
					})
					$("body").on('click', '.leader-speak-item', function(event) {
						$(".video-view").append('<div id="video-view" style="width: 100%; height: 100%;"></div>')
						var videoSrc = $(this).attr('data-video')
						var videoPic = $(this).attr('data-img')
						aliPlayer('video-view', 'video-view', videoSrc, videoPic)
						$(".video-view").css('display', 'flex')
					})
					
					$("body").on('click', '.return', function(event) {
						$(".video-view #video-view").remove()
						$(".video-view").css('display', 'none')
					})
					
					function aliPlayer(vm, dm, vurl, img) {
						var vm = new Aliplayer({
							id: dm,
							width: '100%',
							height: '100%',
							autoplay: true,
							source: vurl,
							cover: img,
							playsinline: false, //是否全屏
							controlBarVisibility: "always",
							useH5Prism: true,
						}, function(player) {
						});
					}
					
					
					
					plate4 = data.plate4
					for (var i = 0; i < plate4.length; i++) {
						var showA;
						if(plate4[i].newsId == ''){
							showA = `<a href="` + plate4[i].url + `" target="_blank">` + plate4[i].title + `</a>` 
						}else{
							showA = `<a href="https://sy.fangxiaoer.com/news/` + plate4[i].newsId + `.htm" target="_blank">` + plate4[i].title + `</a>`
						}
						plate4List = `<li class="cont-4-li"><a href="">` + showA + `</a></li>`
						$(".cont-4-ul").append(plate4List)
					}
					$(".module-4-morebtn").click(function() {
						$(".cont-4-ul li").show()
						$(".module-4-morebtn").hide()
					})
					if (plate4.length>6) {
						$(".module-4-morebtn").show()
					} else{
						$(".module-4-morebtn").hide()
					}
					
					
					plate6 = data.plate6
					// console.log(plate6);
					for (var i = 0; i < plate6.length; i++) {
						plate6List = `<li class="cont-6-li" data='`+plate6[i].tabId+`'>`+plate6[i].tabTitle+`</li>`
						$(".cont-6-ul").append(plate6List)
					} 
					$(".cont-6-ul li").eq(0).addClass('hover')
					showItem(1)
					function showItem(navId){
						var tabIdNum = 'tabId'+navId
						var showItem = data[tabIdNum]
						$(".cont-6-ul-item").html('')
						for (var i = 0; i < showItem.length; i++) {
							
							var priceList = showItem[i].priceList
							var showPriceLi = '';
							
							var priceVal = ''
							for (var f = 0; f < priceList.length; f++) {
								priceVal = priceList[f].price
								if(priceVal != ''){
									priceVal = `<p class="itemIconTxt">`+priceList[f].price+`</p><span>`+priceList[f].unit+`</span>`
									showPriceLi +=`<li ><i class="itemIcon itemIcon`+priceList[f].id+`"></i>`+priceVal+`</li>` 
								}
							}
							
							var  showPhone = ''
							if(showItem[i].phone != ''){
								showPhone = `<a>购房热线：` + showItem[i].phone + `</a>`
							}
							plate6List = ` <li class="cont-6-item">
												<div class="newhouse_left">
													<img loading="lazy" src="` + showItem[i].pic + `" class="cover_pic">
												</div>
												<div class="newhouse_right">
													<div class="newhouse_title">` + showItem[i].title + `</div><ul class="itemIconshow">`+showPriceLi+`</ul>`+showPhone+`
												</div>
											</li>`
							
							
											
							$(".cont-6-ul-item").append(plate6List)			
						}
						if (showItem.length>10) {
							$(".module-6-morebtn").show()
						} else{
							$(".module-6-morebtn").hide()
						}
					}
					
					
					$(".module-6-morebtn").click(function() {
						$(".cont-6-ul-item li").show()
						$(".module-6-morebtn").hide()
					})
					$('.cont-6-li').click(function(){
						var tabId = $(this).attr("data")
						$(".cont-6-ul li").removeClass('hover')
						$(this).addClass('hover')
						showItem(tabId)
					})
					
					
					
					
					plate7 = data.plate7
					for (var i = 0; i < plate7.length; i++) {
						var phoneShow = ''
						if(plate7[i].phone != ''){
							phoneShow = `<a href="tel:` + plate7[i].phone + `" class="cont-7-a">咨询：` + plate7[i].phone + `</a>`
						}
						plate4List = `<li>
										<img loading="lazy" src="` + plate7[i].pic + `" class="cover_pic">
										<div class="cont-7-title">` + plate7[i].title + `</div>
										<div class="cont-7-title2">` + plate7[i].price + `</div>`+phoneShow+`
									</li>`
						$(".cont-7-ul").append(plate4List)
					}
					if (plate7.length>7) {
						$(".module-7-morebtn").show()
					} else{
						$(".module-7-morebtn").hide()
					}
					$(".module-7-morebtn").click(function() {
						$(".cont-7-ul li").show()
						$(".module-7-morebtn").hide()
					})
					
					
					
					var swiper02Small = new Swiper(".img-swiper-4-small", {
						loop: true,
						spaceBetween: 10,
						slidesPerView: 4,
						freeMode: true,
						watchSlidesProgress: true,
					});
					var swiper02 = new Swiper(".img-swiper-4-big", {
						loop: true,
						spaceBetween: 10,
						navigation: {
							nextEl: ".img-swiper-thumbnail-4small-swiper1-next",
							prevEl: ".img-swiper-thumbnail-4small-swiper1-prev",
						},
						thumbs: {
							swiper: swiper02Small,
						},
					});
				},
				
			})	
			
			
			
		})
		
	</script>

	</body>
</html>






