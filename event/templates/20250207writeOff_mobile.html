<!DOCTYPE html>
<html lang="zh">
  <head>
    <meta charset="UTF-8" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no"
    />
    <title>房小二网活动核销页面</title>
    <script
      type="text/javascript"
      src="https://static.fangxiaoer.com/js/jquery.min.js"
    ></script>
    <!-- 引入 CryptoJS 库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js"></script>
    <script src="../static/2025/m0207/js/flexible.js"></script>
    <script src="../static/2025/m0207/js/verify.js?v=11"></script>
    <link
      href="../static/2025/m0207/css/writeOff.css?v=1"
      rel="stylesheet"
      type="text/css"
    />
    <style>
      .sms-section{
        margin-top: 0.5rem;
        display: flex;
        justify-content: space-between;
      }
      .smsCode{
        width: 5.47rem;
        height: 1.12rem;
        background: #FFFFFF;
        border-radius: 0.11rem 0.11rem 0.11rem 0.11rem;
        border: 0.03rem solid #999999;
        padding: 0 0.21rem;
      }
      .sendCode{
        text-align: center;
        width: 2.51rem;
        line-height: 1rem;
      }
    </style>
  </head>
  <body>
<!--  {% include "header_m_csrf.html" %}-->


    <h1>房小二网活动核销页面</h1>
    <div class="wmain">
      <div>请输入中奖手机号码：</div>
      <div class="wirteoff">
        <input
          class="phone2"
          type="number"
          oninput="if(value.length>11)value=value.slice(0,11)"
        />
        <i class="codeWrite">号码核销</i>
      </div>

      <div class="infos"></div>

      <!-- 验证码输入框和发送按钮 -->
      <div class="sms-section" style="display: none;">
        <input
          type="text"
          class="smsCode"
          placeholder="请输入验证码"
          maxlength="6"
        />
        <button class="sendCode">发送验证码</button>
      </div>

      <div class="confirmOff">确认核销</div>

      <div class="footmore">
        <p>使用说明：</p>
        <p>1、输入中奖号码，点击号码核验。</p>
        <p>—核验状态：</p>
        <p>未中奖：表示号码未中奖（不能核销）</p>
        <p>已中奖、已核销：表示号码已中奖，且已领奖（不能核销）</p>
        <p>已中奖、未核销：表示号码已中奖，尚未领奖（可以核销）</p>
        <p>—获取奖品：展示该号码获得的奖品名称</p>
        <p style="margin-top: 0.7rem">
          2、核验状态是已中奖，未核销时，出现确认核销按钮。
        </p>
        <p>点击确认核销后，提供奖品给用户，即完成奖品核销工作。</p>
      </div>
    </div>

    <div class="fxe_alert"><span></span></div>

    <script>
      // http://**************/event/templates/20250207writeOff_mobile.html?storeId=1
      $(function () {
        var promotionId = 2
        // var ltapi = "http://************:8170";
        // var ltapi = "http://********:8000";
        var ltapi = "https://gateway.fangxiaoer.cn";
        var countdown = 60;
        var timer = null;
        var zjData = [];
        var recordIds = [];


        /*------------*/
        // var SIGN_SECRET = '123456'; // 替换为你的密钥
        var SIGN_SECRET = 'c56A26Qtr4'; // 替换为你的密钥

        function generateSignature(data, secret) {
          var hash = CryptoJS.HmacSHA256(data, secret);
          return CryptoJS.enc.Base64.stringify(hash);
        }

        function generateNonce(length) {
          var chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
          var nonce = '';
          for (var i = 0; i < length; i++) {
            nonce += chars.charAt(Math.floor(Math.random() * chars.length));
          }
          return nonce;
        }

        /*------------*/





        // 号码核销
        $(".codeWrite").click(function () {
          let phe2 = $(".phone2").val();
          if (!phe2.match(/^1[3-9]{1}[0-9]{9}$/)) {
            fxe_alert("请正确输入中奖手机号码");
            return;
          }
          getPrizeStatus(phe2)
        });

        // 选择奖品
        $(document).on("click",".clickFn",function () {
          if($(this).attr('d')=="0"){
            recordIds.push($(this).attr('pd'))
            $(this).attr('d',"1")
            $(this).find("i").addClass('ivs')
          }else{
            recordIds.splice(recordIds.indexOf($(this).attr('pd')),1)
            $(this).attr('d',"0")
            $(this).find("i").removeClass('ivs')
          }
        })


        // 发送验证码
        $(".sendCode").click(function () {
          let phe2 = $(".phone2").val();
          if (!phe2) {
            fxe_alert("请输入手机号");
            return;
          }


          let url = ltapi + "/services/share-promotion-service/api/v1/public/sendSmsCode"
          var method = 'POST';
          var path = '/api/v1/public/sendSmsCode';
          var query = `phone=${phe2}`;
          var timestamp = Date.now().toString();
          var nonce = generateNonce(16);
          var body = ''

          var dataToSign = method + '\n' + path + '\n' + query + '\n' + body + '\n' + timestamp + '\n' + nonce;
          console.log("待签名数据：", dataToSign);
          var signature = generateSignature(dataToSign, SIGN_SECRET);



          $.ajax({
            method: method,
            url: url+ '?' + query,
            // data: { phone: phe2 },
            success: function (res, textStatus, xhr) {
              if (xhr.status === 200) {
                fxe_alert("验证码已发送");
                startCountdown();
              }
            },
            beforeSend: function(xhr) {
              xhr.setRequestHeader('X-Signature', signature);
              xhr.setRequestHeader('X-Timestamp', timestamp);
              xhr.setRequestHeader('X-Nonce', nonce);
            },
            error: function(xhr, status, error) {
              try {
                let errResponse = JSON.parse(xhr.responseText);
                if (errResponse) {
                  fxe_alert(errResponse.detail);
                }
              } catch (e) {
                fxe_alert("请求失败，请检查网络连接");
              }
            }
          });
        });

        // 计时器
        function startCountdown() {
          let btn = $(".sendCode");
          btn.prop("disabled", true).text(`${countdown}s`);
          timer = setInterval(() => {
            countdown--;
            if (countdown > 0) {
              btn.text(`${countdown}s`);
            } else {
              clearInterval(timer);
              btn.prop("disabled", false).text("发送验证码");
              countdown = 60;
            }
          }, 1000);
        }

        // 确认核销
        $(".confirmOff").click(function () {
          let storeId=getQueryString('storeId')
          let phe2 = $(".phone2").val();
          let smsCode = $(".smsCode").val();

          if(!storeId){
            fxe_alert("请联系客服，获取门店ID!")
            return;
          }else if(recordIds.length==0) {
            fxe_alert("请选择要核销得奖品!")
            return;
          }/*else if(!smsCode) {
            fxe_alert("请输入验证码!");
            return;
          }*/


          if (storeId && zjData && phe2) {
            let url = ltapi + "/services/share-promotion-service/api/v1/public/verifyPrize"
            var method = 'PUT';
            var path = '/api/v1/public/verifyPrize';
            var query = ``;
            var timestamp = Date.now().toString();
            var nonce = generateNonce(16);
            var body = JSON.stringify({
              // smsCode: smsCode,
              phone: phe2,
              promotionId: promotionId,
              companyId: storeId,
              recordIds: recordIds,//奖品列表
            })

            var dataToSign = method + '\n' + path + '\n' + query + '\n' + body + '\n' + timestamp + '\n' + nonce;
            console.log("待签名数据：", dataToSign);
            var signature = generateSignature(dataToSign, SIGN_SECRET);


            $.ajax({
              method: method,
              // url: ltapi + "/services/share-promotion-service/api/v1/public/verifyPrize",
              url: url+ '?' + query,
              contentType: "application/json",
              data: body,
              beforeSend: function(xhr) {
                xhr.setRequestHeader('X-Signature', signature);
                xhr.setRequestHeader('X-Timestamp', timestamp);
                xhr.setRequestHeader('X-Nonce', nonce);
              },
              success: function (res, textStatus, xhr) {
                if (xhr.status === 200) {
                  fxe_alert("核销成功");
                  getPrizeStatus(phe2)
                  recordIds=[]
                  $(".smsCode").val('')
                  clearInterval(timer)
                  countdown = 60
                  $(".sendCode").prop("disabled", false).text("发送验证码");
                  $(".confirmOff, .sms-section").hide()
                }
              },
              error: function(xhr, status, error) {
                try {
                  let errResponse = JSON.parse(xhr.responseText);
                  if (errResponse) {
                    fxe_alert(errResponse.detail);
                  }
                } catch (e) {
                  fxe_alert("请求失败，请检查网络连接");
                }
              }
            });
          } else {
            fxe_alert("参数不全");
          }
        });



        //查询中奖状态
        function getPrizeStatus(phe2){

          let url = ltapi + "/services/share-promotion-service/api/v1/public/getPrizeStatus"
          var method = 'GET';
          var path = '/api/v1/public/getPrizeStatus';
          var query = `promotionId=${promotionId}&phone=${phe2}`;
          var timestamp = Date.now().toString();
          var nonce = generateNonce(16);
          var body = ''

          var dataToSign = method + '\n' + path + '\n' + query + '\n' + body + '\n' + timestamp + '\n' + nonce;
          console.log("待签名数据：", dataToSign);
          var signature = generateSignature(dataToSign, SIGN_SECRET);


          $.ajax({
            method: method,
            url: url+ '?' + query,
            beforeSend: function(xhr) {
              xhr.setRequestHeader('X-Signature', signature);
              xhr.setRequestHeader('X-Timestamp', timestamp);
              xhr.setRequestHeader('X-Nonce', nonce);
            },
            success: function (res, textStatus, xhr) {
              if (xhr.status === 200) {
                $(".infos").empty();
                zjData = res

                if (res.length == 0) {
                  $(".infos").empty()
                  fxe_alert(res.msg || "没有中奖记录");
                  $(".infos, .confirmOff, .sms-section").hide();
                } else {
                  $(".infos").show();

                  for (var i = 0; i < res.length; i++) {
                    let str = `
                    <div class="infos-li ${res[i].prizeStatus==0?'clickFn':''}" d="0" pd="${res[i].id}">
                      <div class="${res[i].prizeStatus == 0?'':'line'} infos-num">${i+1}:</div>
                      <div>
                        <div class="${res[i].prizeStatus == 0?'':'line'}"><strong>核销状态：</strong>已中奖 ${res[i].prizeStatus == 0 ? "未核销" : "已核销"}</div>
                        <div class="${res[i].prizeStatus == 0?'':'line'}"><strong>获取奖品：</strong>${res[i].prizeName}</div>
                        <div class="${res[i].prizeStatus == 0?'':'line'}"><strong>中奖时间：</strong>${res[i].createdDate}</div>
                        <i class="${res[i].prizeStatus == 0 ? '' : 'hide'}" ></i>
                      </div>
                    </div>`
                    $(".infos").append(str)

                    // res[i].prizeStatus==0 ? $(".confirmOff, .sms-section").show() : ''
                    res[i].prizeStatus==0 ? $(".confirmOff").show() : ''
                  }
                }
              }
            },
            error: function(xhr, status, error) {
              console.error('失败', xhr.responseText);
              $(".infos").empty()
              try {
                let errResponse = JSON.parse(xhr.responseText);
                if (errResponse) {
                  fxe_alert(errResponse.detail);
                }
              } catch (e) {
                fxe_alert("请求失败，请检查网络连接");
              }
            }
          });
        }

      });



      // 获取URL参数
      function getQueryString(name) {
        var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)", "i");
        var r = window.location.search.substr(1).match(reg);
        if (r != null) return unescape(r[2]);
        return null;
      }

      // 提示框
      function fxe_alert(text) {
        $(".fxe_alert span").text(text);
        $(".fxe_alert").fadeIn().delay(2000).fadeOut();
      }
    </script>
  </body>
</html>
